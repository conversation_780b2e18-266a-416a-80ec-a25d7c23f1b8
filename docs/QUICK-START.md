# SLine LSP 快速启动指南

## 🚀 立即测试 LSP 功能

### 1. 启动调试环境
```bash
# 方法 1: 使用快捷键
按 F5

# 方法 2: 使用命令面板
Ctrl+Shift+P (Windows/Linux) 或 Cmd+Shift+P (Mac)
输入 "Debug: Start Debugging"
选择 "Run Extension"
```

### 2. 打开测试文件
在新的 VSCode 窗口中：
1. 打开 `test-lsp.sline` 文件
2. 确认状态栏右下角显示 "SLine"

### 3. 测试自动完成功能

#### 测试 1: 基本控制结构
1. 在文件末尾输入：`{{#`
2. 应该看到丰富的提示（50+ 项）：
   - **控制流**: `if`, `each`, `for`, `with`, `unless`, `else`
   - **SLine 指令**: `component`, `layout`, `content`, `section`, `var`, `schema`
   - **区块系统**: `blocks`, `block`
3. 选择 `if` 并按 Tab，应该插入：
   ```sline
   {{#if condition}}
   {{/if}}
   ```
4. 注意每个项目都有：
   - 🎯 **专门的图标** (不再是通用的 {abc} 图标)
   - 📖 **详细描述** (包含表情符号和用法说明)
   - 🏷️ **分类标签** (如 "SLine 控制流", "SLine 组件系统")

#### 测试 2: 组件调用
1. 输入：`{{#component "`
2. 应该看到组件名称提示：`button`, `header`, `footer` 等
3. 选择一个组件名称

#### 测试 3: 过滤器 (25+ 个)
1. 输入：`{{price | `
2. 应该看到丰富的过滤器提示：
   - **货币**: `money`, `money_with_currency`, `money_without_currency`
   - **文本**: `upper`, `lower`, `capitalize`, `truncate`, `trim`
   - **数组**: `first`, `last`, `size`, `join`
   - **日期**: `date`, `format`
   - **通用**: `default`, `t`, `json`, `class_list`

### 4. 测试错误检查

#### 测试未闭合标签
1. 输入：`{{#if test}}`
2. 应该看到红色波浪线错误提示
3. 悬停查看错误信息："未闭合的标签: if"

#### 测试未定义组件
1. 输入：`{{#component "nonexistent" /}}`
2. 应该看到黄色波浪线警告
3. 悬停查看警告信息："未找到组件: nonexistent"

### 5. 测试悬停信息
1. 将鼠标悬停在 `if` 关键字上
2. 应该显示帮助信息和语法示例
3. 尝试悬停在其他关键字上：`each`, `component`, `money` 等

## 🔍 检查日志

### 查看语言服务器日志
1. 在调试窗口中，打开 **输出面板** (View > Output)
2. 在下拉菜单中选择 **"SLine Language Server"**
3. 应该看到类似的日志：
   ```
   SLine Language Server initialized successfully!
   Configuration capability registered
   Document opened: file:///path/to/test-lsp.sline
   Completion triggered at line X, char Y
   ```

### 如果没有日志
1. 检查是否选择了正确的输出通道
2. 重启语言服务器：`Ctrl+Shift+P` > "Restart SLine Language Server"
3. 重新打开测试文件

## ✅ 功能检查清单

- [ ] F5 启动成功，没有错误
- [ ] 测试文件正确识别为 SLine 语言
- [ ] 输入 `{{#` 显示自动完成
- [ ] 输入 `{{#component "` 显示组件提示
- [ ] 输入 `| ` 显示过滤器提示
- [ ] 未闭合标签显示红色错误
- [ ] 未定义组件显示黄色警告
- [ ] 悬停显示帮助信息
- [ ] 输出面板有服务器日志

## 🐛 如果功能不工作

### 立即尝试的解决方案
1. **重启语言服务器**：
   - `Ctrl+Shift+P` > "Restart SLine Language Server"

2. **手动触发完成**：
   - 按 `Ctrl+Space` (Windows/Linux) 或 `Cmd+Space` (Mac)

3. **检查文件类型**：
   - 确认状态栏显示 "SLine"
   - 如果不是，点击切换语言类型

4. **重新编译**：
   ```bash
   npm run compile:all
   ```

5. **重启 VSCode**：
   - 关闭调试窗口
   - 重新按 F5

### 查看详细故障排除
如果问题仍然存在，请查看 `TROUBLESHOOTING.md` 文件获取详细的诊断步骤。

## 🎯 成功标志

当所有功能正常工作时，你应该能够：
1. 快速输入 SLine 模板语法
2. 获得智能的代码提示
3. 实时看到语法错误
4. 通过悬停获得帮助信息

这将大大提升你的 SLine 模板开发效率！
