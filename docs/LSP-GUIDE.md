# SLine LSP 功能使用指南

## 🚀 语言服务器协议(LSP)功能概述

SLine VSCode 插件现在支持完整的语言服务器协议，为 SLine 模板开发提供智能化的编程体验。

## 📋 功能列表

### 1. 智能代码完成 (IntelliSense)

#### 触发方式
- 输入 `{{#` - 自动提示控制结构
- 输入 `{{#component` - 提示组件调用语法
- 输入 `|` - 提示可用过滤器
- 输入 `.` - 提示对象属性

#### 支持的完成项
- **控制结构**: `if`, `each`, `with`, `unless`
- **SLine 指令**: `component`, `layout`, `content`, `section`, `var`
- **过滤器**: `money`, `asset_url`, `date`, `t`, `upper`, `lower`

### 2. 实时错误检查 (Diagnostics)

#### 错误类型
- **语法错误**: 未闭合的 Handlebars 标签
- **警告**: 未定义的组件调用
- **提示**: 语法建议和最佳实践

#### 错误示例
```sline
{{#if condition}}
    <p>内容</p>
<!-- 错误：缺少 {{/if}} -->

{{#component "unknown-component" /}}
<!-- 警告：未找到组件 -->
```

### 3. 悬停信息 (Hover)

将鼠标悬停在 SLine 语法上可以看到：
- 语法说明
- 使用示例
- 参数说明

### 4. 配置选项

在 VSCode 设置中可以自定义：

```json
{
  "slineLanguageServer.enableDiagnostics": true,
  "slineLanguageServer.enableCompletion": true,
  "slineLanguageServer.maxNumberOfProblems": 1000
}
```

## 🛠️ 开发和调试

### 重启语言服务器
如果遇到问题，可以通过命令面板重启：
1. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
2. 输入 "Restart SLine Language Server"
3. 选择命令执行

### 调试模式
语言服务器支持调试模式，调试端口：6009

### 日志查看
在 VSCode 输出面板中选择 "SLine Language Server" 查看日志。

## 📝 最佳实践

### 1. 文件组织
- 将 SLine 模板文件保存为 `.sline` 或 `.html` 扩展名
- 在项目根目录包含 `theme-sline` 目录以获得更好的组件提示

### 2. 代码风格
- 使用一致的缩进
- 为复杂逻辑添加注释
- 合理使用组件化

### 3. 错误处理
- 及时修复语法错误
- 注意标签的正确闭合
- 验证组件名称的正确性

## 🔧 故障排除

### 常见问题

1. **LSP 功能不工作**
   - 检查插件是否正确安装
   - 重启 VSCode
   - 重启语言服务器

2. **代码完成不触发**
   - 检查文件扩展名是否正确
   - 确认配置选项已启用
   - 尝试手动触发 (Ctrl+Space)

3. **错误检查不准确**
   - 更新到最新版本
   - 检查语法是否符合 SLine 规范
   - 查看输出面板的错误信息

## 📚 相关资源

- [SLine 模板语法文档](./README.md)
- [VSCode LSP 官方文档](https://code.visualstudio.com/api/language-extensions/language-server-extension-guide)
- [项目更新日志](./CHANGELOG.md)

## 🤝 贡献和反馈

如果您发现问题或有改进建议，欢迎：
- 提交 Issue
- 发起 Pull Request
- 分享使用经验
