# SLine 括号颜色一致性修复指南

如果您在使用 SLine 语法高亮时发现括号颜色不一致，请按照以下步骤解决：

## 方案一：重新加载 VS Code 窗口

1. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
2. 输入 "Developer: Reload Window" 并执行
3. 重新打开 `.sline` 文件查看效果

## 方案二：使用 Scope Inspector 调试

1. 打开 `debug-scope-test.sline` 文件
2. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
3. 输入 "Developer: Inspect Editor Tokens and Scopes"
4. 点击任何 `{{` 或 `}}` 括号
5. 检查 scope 是否正确显示为：
   - `punctuation.definition.tag.begin.sline` (开始括号)
   - `punctuation.definition.tag.end.sline` (结束括号)

## 方案三：切换默认主题测试

1. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
2. 输入 "Preferences: Color Theme"
3. 选择 "Dark+ (default dark)" 或 "Light+ (default light)"
4. 查看括号颜色是否一致

## 方案四：手动主题配置

如果您的主题不支持标准的 punctuation scope，您可以在 VS Code 设置中添加自定义颜色规则：

1. 打开 VS Code 设置 (`Ctrl+,` 或 `Cmd+,`)
2. 搜索 "workbench.colorCustomizations"
3. 在 `settings.json` 中添加以下配置：

```json
{
  "editor.tokenColorCustomizations": {
    "textMateRules": [
      {
        "name": "SLine Brackets",
        "scope": [
          "punctuation.definition.tag.begin.sline",
          "punctuation.definition.tag.end.sline"
        ],
        "settings": {
          "foreground": "#569CD6"  // 您可以更改为任何您喜欢的颜色
        }
      }
    ]
  }
}
```

## 方案五：主题开发者配置

如果您是主题开发者，建议在主题中添加以下规则以支持 SLine：

```json
{
  "name": "SLine punctuation",
  "scope": [
    "punctuation.definition.tag.begin.sline",
    "punctuation.definition.tag.end.sline",
    "punctuation.definition.block.hash.sline",
    "punctuation.definition.block.slash.sline"
  ],
  "settings": {
    "foreground": "#569CD6"
  }
}
```

## 验证修复结果

使用 `debug-scope-test.sline` 文件验证所有括号颜色是否一致：

- ✅ 所有 `{{` 应该有相同颜色
- ✅ 所有 `}}` 应该有相同颜色  
- ✅ 在标准主题中，`{{` 和 `}}` 应该是相同颜色

如果问题仍然存在，请提供 Scope Inspector 的截图以便进一步诊断。 