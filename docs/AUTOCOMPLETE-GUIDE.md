# SLine 自动完成功能指南

## 功能概述

SLine 扩展现在提供智能的自动完成功能，包括：

1. **过滤器自动完成** - 在管道符 `|` 后提示所有75个可用过滤器
2. **标签自动完成** - 在 `{{#` 后提示所有56个Sline标签
3. **关键词自动完成** - 提示 Sline 语法关键词和助手
4. **智能触发** - 自动检测上下文并提供相关建议

> ⚠️ **重要提示**: Sline 过滤器必须以函数形式调用，使用 `()` 括号。例如：`{{ name | upcase() }}` 而不是 `{{ name | upcase }}`。

## 触发方式

### 🔧 自动触发字符

- `|` - 管道符：触发过滤器完成
- `#` - 井号：触发块助手完成  
- `{` - 大括号：在表达式内触发完成

### ⌨️ 手动触发

- `Ctrl+Space` (Windows/Linux) 或 `Cmd+Space` (macOS)

## 使用示例

### 1. 过滤器自动完成

当您输入管道符后，会自动显示所有75个可用过滤器：

```sline
{{ product.title | }}
<!-- 输入 | 后会显示所有过滤器：abs(), append(), asset_url(), capitalize()... -->

{{ product.title | up }}
<!-- 输入 'up' 会过滤显示：upcase(), url_encode(), url_escape()... -->
```

### 2. 过滤器链完成

支持多个过滤器链：

```sline
{{ product.price | money() | }}
<!-- 第二个 | 后仍会显示过滤器建议 -->

{{ product.description | truncate(100) | strip_html() | }}
<!-- 可以继续添加更多过滤器 -->
```

### 3. 标签自动完成

当您输入 `{{#` 后，会自动显示所有56个可用标签：

```sline
{{#}}
<!-- 显示所有标签：activate_customer_password_form, block, blocks, capture, cart_form... -->

{{#customer}}
<!-- 过滤显示客户相关标签：customer_form, customer_login_form, customer_address_form... -->

{{#image}}
<!-- 过滤显示：image_tag -->

{{#link}}
<!-- 过滤显示：link_to, link_to_customer_login, link_to_customer_logout... -->
```

### 4. 关键词和助手完成

在空表达式位置：

```sline
{{}}
<!-- 显示常用关键词：if, each, unless, with, layout, component... -->
```

### 5. 标签使用示例

#### 控制流标签
```sline
{{#if product.available}}        <!-- 条件判断 -->
  产品可用
{{/if}}

{{#for item in products}}        <!-- 循环遍历 -->
  {{ item.title }}
{{/for}}

{{#capture product_info}}        <!-- 捕获输出 -->
  {{ product.title }} - {{ product.price }}
{{/capture}}

{{#case product.type}}           <!-- 多分支选择 -->
  {{#when "digital"}}数字产品{{/when}}
  {{#when "physical"}}实体产品{{/when}}
{{/case}}
```

#### 布局组件标签
```sline
{{#layout "product"}}            <!-- 页面布局 -->
{{#component "header"}}          <!-- 组件引用 -->
{{#section "content"}}           <!-- 页面区块 -->
  内容区域
{{/section}}
{{#content "sidebar"}}           <!-- 内容区域 -->
```

#### 表单标签
```sline
{{#customer_login_form}}         <!-- 登录表单 -->
  <input type="email" name="customer[email]">
  <input type="password" name="customer[password]">
  <button type="submit">登录</button>
{{/customer_login_form}}

{{#cart_form}}                   <!-- 购物车表单 -->
  <button type="submit">更新购物车</button>
{{/cart_form}}

{{#contact_form}}                <!-- 联系表单 -->
  <input type="text" name="contact[name]">
  <textarea name="contact[message]"></textarea>
{{/contact_form}}
```

#### 媒体资源标签
```sline
{{#image_tag src="product.jpg" alt="产品图片"}}  <!-- 图片标签 -->
{{#video_tag src="demo.mp4"}}                    <!-- 视频标签 -->
{{#stylesheet "theme.css"}}                      <!-- 样式表 -->
{{#script "app.js"}}                             <!-- 脚本文件 -->
```

#### 功能标签
```sline
{{#link_to "/products"}}查看产品{{/link_to}}          <!-- 生成链接 -->
{{#link_to_customer_login}}登录{{/link_to_customer_login}}  <!-- 登录链接 -->
{{#payment_button}}立即支付{{/payment_button}}       <!-- 支付按钮 -->
{{#format_address customer.address}}               <!-- 地址格式化 -->
```

### 6. 常用过滤器示例

```sline
<!-- 文本处理 -->
{{ product.title | upcase() }}           <!-- 转大写 -->
{{ product.title | capitalize() }}       <!-- 首字母大写 -->
{{ content | truncate(100) }}            <!-- 截断文本 -->
{{ content | strip_html() }}             <!-- 去除HTML -->

<!-- 数组操作 -->
{{ products | map("title") }}            <!-- 提取属性 -->
{{ categories | join(", ") }}            <!-- 连接数组 -->
{{ products | where("available") }}      <!-- 筛选数组 -->
{{ products | sort("created_at") }}      <!-- 排序 -->

<!-- 数值处理 -->
{{ product.price | money() }}            <!-- 货币格式 -->
{{ discount | abs() }}                   <!-- 绝对值 -->
{{ quantity | plus(1) }}                 <!-- 加法 -->

<!-- 日期和URL -->
{{ created_at | date("%Y-%m-%d") }}      <!-- 日期格式化 -->
{{ "style.css" | asset_url() }}          <!-- 资源URL -->
```

## 🎯 智能特性

### 1. 上下文感知
- 只在 `{{ }}` 表达式内部提供建议
- 支持多行表达式
- 智能检测管道符位置

### 2. 过滤匹配
- 支持部分匹配（如输入 'up' 匹配 'upcase'）
- 不区分大小写
- 按相关性排序

### 3. 详细信息
每个自动完成项都提供：
- **标签** - 过滤器/关键词名称
- **详细说明** - 功能描述
- **使用示例** - 具体用法演示

## 📝 标签分类

### 控制流标签 (7个)
- **条件判断**: `if`, `case`, `switch`
- **循环控制**: `for`
- **变量操作**: `var`, `set`, `capture`

### 布局组件标签 (8个)
- **布局控制**: `layout`, `block`, `blocks`
- **组件系统**: `component`, `content`, `section`, `sections`
- **模式定义**: `schema`

### 表单标签 (25个)
- **客户表单**: `customer_form`, `customer_login_form`, `customer_address_form`
- **注册登录**: `create_customer_form`, `customer_register_link`, `customer_login_link`, `customer_logout_link`
- **密码管理**: `activate_customer_password_form`, `reset_customer_password_form`
- **账户管理**: `update_customer_form`, `delete_customer_form`, `cancel_delete_customer_form`
- **绑定操作**: `bind_customer_email_form`, `bind_customer_phone_form`
- **订阅管理**: `customer_subscribe_form`, `customer_unsubscribe_form`
- **企业账户**: `company_account_application_form`
- **其他表单**: `cart_form`, `contact_form`, `new_comment_form`, `order_tracking_form`, `localization_form`, `storefront_password_form`

### 媒体资源标签 (10个)
- **图片处理**: `image_tag`, `placeholder_svg`
- **视频处理**: `video_tag`, `external_video_tag`
- **样式资源**: `style`, `stylesheet`, `script`, `preload_tag`
- **时间标签**: `time_tag`
- **支付图标**: `payment_type_svg`

### 功能标签 (6个)
- **链接生成**: `link_to`, `link_to_customer_login`, `link_to_customer_logout`, `link_to_customer_register`
- **支付功能**: `payment_button`
- **地址格式**: `format_address`
- **高亮显示**: `highlight`
- **元字段**: `metafield_tag`

## 📝 过滤器分类

### 文本处理 (Text)
- `capitalize`, `upcase`, `downcase`
- `truncate`, `truncate_words`
- `strip_html`, `strip_newlines`
- `escape`, `trim`, `trim_left`, `trim_right`

### 数组操作 (Array)
- `first`, `last`, `size`, `reverse`
- `map`, `where`, `sort`, `uniq`
- `join`, `slice`

### 字符串操作 (String)
- `append`, `prepend`, `concat`
- `replace`, `replace_first`
- `remove`, `remove_first`
- `split`, `contains`, `starts_with`, `ends_with`

### 数值计算 (Math)
- `abs`, `ceil`, `floor`, `round`
- `plus`, `minus`, `times`, `divided_by`, `modulo`

### 格式化 (Format)
- `money`, `money_with_currency`, `money_without_currency`
- `date`, `json`, `newline_to_br`

### URL和资源 (URL/Assets)
- `asset_url`, `file_url`, `file_img_url`
- `url_encode`, `url_decode`, `url_escape`

## 🔧 安装和使用

1. **安装扩展包**：
   ```bash
   # 在 VS Code 中安装
   # 命令面板 -> "Extensions: Install from VSIX..."
   # 选择 sline-highlight-0.1.3.vsix
   ```

2. **重新加载窗口**：
   ```
   Ctrl+Shift+P -> "Developer: Reload Window"
   ```

3. **开始使用**：
   - 打开 `.sline` 文件
   - 在 `{{ }}` 表达式中输入 `|` 即可看到过滤器提示

## 🐛 故障排除

如果自动完成不工作：

1. **检查文件扩展名** - 确保文件以 `.sline` 结尾
2. **重启语言服务器** - 命令：`SLine: 重启 SLine 语言服务器`
3. **重新加载窗口** - `Developer: Reload Window`
4. **检查语法** - 确保在有效的 `{{ }}` 表达式内

## ✨ 提示和技巧

1. **链式过滤器**：可以使用多个 `|` 连接过滤器
2. **参数支持**：某些过滤器支持参数，如 `truncate: 100`
3. **性能优化**：过滤器按使用频率排序
4. **快速选择**：使用上下箭头键导航，回车选择

---

**注意**：
- 本指南基于 `sline_filters_list.md` 中的**75个**官方过滤器列表
- 本指南基于 `sline_tags_list.md` 中的**56个**官方标签列表
- 总计支持**131个**自动完成项（75个过滤器 + 56个标签） 