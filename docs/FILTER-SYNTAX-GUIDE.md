# Sline Filter 语法指南

## 📋 重要说明

**在 Sline 模板引擎中，所有的 filter 都必须以函数调用方式使用，即包含括号 `()`**

## ❌ 错误语法
```sline
{{product.price | money}}
{{image | asset_url}}
{{"hello" | t}}
{{text | truncate: 50}}
```

## ✅ 正确语法
```sline
{{product.price | money()}}
{{image | asset_url()}}
{{"hello" | t()}}
{{text | truncate(50)}}
```

## 🎯 Filter 分类和正确用法

### 1. 货币和数字格式化
```sline
{{product.price | money()}}
{{product.price | money_with_currency()}}
{{product.price | money_without_currency()}}
{{10 | plus(5)}}
{{10 | minus(3)}}
{{10 | times(2)}}
{{10 | divided_by(2)}}
{{3.7 | ceil()}}
{{3.2 | floor()}}
```

### 2. 字符串处理
```sline
{{"hello" | upcase()}}
{{"HELLO" | downcase()}}
{{"hello world" | capitalize()}}
{{text | strip_html()}}
{{text | trim()}}
{{long_text | truncate(50)}}
{{text | append("suffix")}}
{{text | prepend("prefix")}}
{{text | replace("old", "new")}}
```

### 3. URL 和资源
```sline
{{"logo.png" | asset_url()}}
{{product.image | image_url()}}
{{product.video_id | external_video_url()}}
{{text | url_encode()}}
{{encoded_text | url_decode()}}
```

### 4. 数组操作
```sline
{{products | first()}}
{{products | last()}}
{{products | size()}}
{{tags | join(",")}}
{{products | sort("title")}}
{{products | reverse()}}
{{products | slice(0, 3)}}
{{products | map("title")}}
{{products | where("available", true)}}
```

### 5. 日期处理
```sline
{{order.created_at | date(format="%Y-%m-%d")}}
{{order.created_at | date(format="%B %d, %Y")}}
```

### 6. 通用功能
```sline
{{description | default("暂无描述")}}
{{"general.title" | t()}}
{{data | json()}}
{{tags | contains("sale")}}
```

### 7. SLine 特有功能
```sline
{{settings | class_list()}}
{{"primary-color" | css_var()}}
{{font | font_face()}}
{{product | get_variants()}}
{{blog.articles | get_pagination()}}
```

## 🔄 Filter 链
可以将多个 filter 链接使用：
```sline
{{product.title | truncate(20) | upcase()}}
{{product.price | money() | default("价格未设定")}}
```

## 🧩 在控制结构中使用
```sline
{{#if product.price | money()}}
  <span class="price">{{product.price | money()}}</span>
{{/if}}

{{#each products | where("available", true)}}
  <div class="product">
    <h3>{{this.title | capitalize()}}</h3>
    <p>{{this.price | money()}}</p>
  </div>
{{/each}}
```

## 📝 在变量定义中使用
```sline
{{#var formatted_title = product.title | upcase() /}}
{{#var product_count = products | size() /}}
{{#var display_price = product.price | money() | default("免费") /}}
```

## 🎨 语法高亮支持
更新后的语法高亮规则现在支持：
- ✅ Filter 函数名称高亮（`support.function.filter.sline`）
- ✅ 括号语法高亮（`punctuation.definition.parameters`）
- ✅ 参数语法高亮（字符串、数字、变量）
- ✅ 参数分隔符高亮（`,` 和 `=`）

## 📖 参考资源
- 查看 `test-correct-filter-syntax.sline` 获取完整示例
- 查看 `src/server/server.ts` 中的 LSP 自动完成功能
- 查看 `snippets/sline.json` 中的代码片段

---
**记住：在 Sline 中，filter 永远是函数！** 