# SLine LSP 故障排除指南

## 🔍 如何测试 LSP 功能

### 1. 启动调试环境
1. 在 VSCode 中打开项目
2. 按 `F5` 或运行 "Run Extension" 调试配置
3. 在新的 VSCode 窗口中打开 `test-lsp.sline` 文件

### 2. 检查语言服务器状态
1. 在调试窗口中，打开 **输出面板** (View > Output)
2. 在下拉菜单中选择 **"SLine Language Server"**
3. 查看是否有启动日志：
   ```
   SLine Language Server initialized successfully!
   Document opened: file:///path/to/test-lsp.sline
   ```

### 3. 测试自动完成
1. 在 `test-lsp.sline` 文件中找到测试行
2. 输入 `{{#` 
3. 应该看到自动完成提示：`if`, `each`, `component` 等
4. 如果没有提示，按 `Ctrl+Space` (Windows/Linux) 或 `Cmd+Space` (Mac) 手动触发

### 4. 测试错误检查
1. 创建一个未闭合的标签：`{{#if condition}}`
2. 应该在编辑器中看到红色波浪线错误提示
3. 悬停查看错误信息

### 5. 测试悬停信息
1. 将鼠标悬停在 `if`, `each`, `component` 等关键字上
2. 应该显示语法帮助信息

## 🐛 常见问题及解决方案

### 问题 1: LSP 功能完全不工作
**症状**: 没有自动完成、错误检查、悬停信息

**解决方案**:
1. 检查输出面板是否有错误信息
2. 重启语言服务器：`Ctrl+Shift+P` > "Restart SLine Language Server"
3. 重新编译项目：`npm run vscode:prepublish`
4. 重启 VSCode

### 问题 2: 自动完成不触发
**症状**: 输入 `{{#` 没有提示

**解决方案**:
1. 确认文件扩展名是 `.sline`
2. 检查文件是否被正确识别为 SLine 语言
3. 手动触发完成：`Ctrl+Space`
4. 查看输出面板的日志

### 问题 3: 错误检查不准确
**症状**: 正确的语法显示错误，或错误的语法没有提示

**解决方案**:
1. 检查语法是否符合 SLine 规范
2. 更新到最新版本
3. 查看输出面板的详细错误信息

### 问题 4: 语言服务器启动失败
**症状**: 输出面板显示连接错误

**解决方案**:
1. 检查 Node.js 版本是否兼容
2. 重新安装依赖：`npm install`
3. 重新编译：`npm run compile && npm run compile:server`

## 🔧 调试步骤

### 启用详细日志
1. 在 VSCode 设置中添加：
   ```json
   {
     "slineLanguageServer.trace.server": "verbose"
   }
   ```

### 调试语言服务器
1. 启动扩展调试 (F5)
2. 在 VSCode 中运行 "Debug Language Server" 配置
3. 在服务器代码中设置断点

### 检查文件关联
1. 确认 `.sline` 文件被正确识别
2. 在状态栏右下角应该显示 "SLine"
3. 如果显示其他语言，点击切换为 "SLine"

## 📝 测试清单

- [ ] 语言服务器启动成功
- [ ] 文档正确打开和识别
- [ ] 输入 `{{#` 显示自动完成
- [ ] 输入 `{{#component "` 显示组件提示
- [ ] 输入 `| ` 显示过滤器提示
- [ ] 未闭合标签显示错误
- [ ] 未定义组件显示警告
- [ ] 悬停显示帮助信息

## 📞 获取帮助

如果问题仍然存在：
1. 收集输出面板的完整日志
2. 记录重现步骤
3. 提交 Issue 并附上日志信息
