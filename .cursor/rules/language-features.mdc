# SLine Language Features Development

## Syntax Highlighting ([syntaxes/sline.tmLanguage.json](mdc:syntaxes/sline.tmLanguage.json))

When updating TextMate grammar:
- Test patterns in regex101.com before adding
- Use descriptive names for capture groups
- Include common SLine template constructs: `{{}}`, `{{#}}`, `{{/}}`, `{{!}}`
- Support nested expressions and string literals
- Validate against sample SLine files

## Language Configuration ([language-configuration.json](mdc:language-configuration.json))

Maintains editor behavior:
- **Comments**: Uses SLine-specific comment syntax (`{{!-- --}}`)
- **Brackets**: Includes template brackets `{{` `}}`
- **Auto-closing**: Pairs template tags automatically
- **Folding**: Supports block helpers (`{{#if}}` to `{{/if}}`)
- **Indentation**: Smart indentation for nested blocks

## Code Snippets ([snippets/sline.json](mdc:snippets/sline.json))

When adding snippets:
- Use descriptive prefixes (e.g., "if", "each", "with")
- Include common SLine helpers and patterns
- Provide meaningful placeholders with `$1`, `$2`, etc.
- Use `$0` for final cursor position
- Add descriptions for each snippet

## Language Server Integration

The language server ([src/server/server.ts](mdc:src/server/server.ts)) should provide:
- Syntax validation and error reporting
- Auto-completion for variables and helpers
- Hover information for template functions
- Go-to-definition for variables and partials
- Document formatting and range formatting

## Testing Language Features

Create test files with various SLine constructs:
- Template variables: `{{ variable }}`
- Block helpers: `{{#if condition}}...{{/if}}`
- Comments: `{{!-- comment --}}`
- Nested expressions: `{{ helper (nested value) }}`
description:
globs:
alwaysApply: false
---
