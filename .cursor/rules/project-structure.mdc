# SLine VS Code Extension Project Structure

This is a VS Code extension for SLine template engine language support. The project follows modern VS Code extension development best practices.

## Key Files and Directories

- [package.json](mdc:package.json) - Extension manifest with configuration, commands, and dependencies
- [src/extension.ts](mdc:src/extension.ts) - Main extension entry point
- [src/client.ts](mdc:src/client.ts) - Language Server Protocol client implementation
- [src/server/server.ts](mdc:src/server/server.ts) - Language server implementation
- [syntaxes/sline.tmLanguage.json](mdc:syntaxes/sline.tmLanguage.json) - TextMate grammar for syntax highlighting
- [language-configuration.json](mdc:language-configuration.json) - Language configuration (brackets, comments, indentation)
- [snippets/sline.json](mdc:snippets/sline.json) - Code snippets for SLine language
- [esbuild.js](mdc:esbuild.js) - Build configuration for Node.js extension
- [esbuild-web.js](mdc:esbuild-web.js) - Build configuration for web extension

## Build System

The project uses esbuild for bundling with separate configurations for Node.js and web environments. The output goes to `dist/` directory:
- `dist/extension.js` - Main extension bundle
- `dist/server/server.js` - Language server bundle  
- `dist/web/extension.js` - Web extension bundle

## Development Workflow

1. Use `npm run watch` for parallel development builds
2. Use F5 in VS Code to launch Extension Development Host
3. Test with `.sline` files to verify syntax highlighting and language features
description:
globs:
alwaysApply: false
---
