# Build and Deployment Guidelines

## Build System Overview

The project uses esbuild for fast bundling with two configurations:
- [esbuild.js](mdc:esbuild.js) - Node.js extension bundle
- [esbuild-web.js](mdc:esbuild-web.js) - Web extension bundle

## Development Build

```bash
npm run watch    # Parallel development builds with file watching
npm run compile  # One-time compilation
```

## Production Build

```bash
npm run package  # Type check + production build for both platforms
```

## Testing Before Release

1. Run `npm run test` to execute all tests
2. Test in Extension Development Host (F5)
3. Test web extension at vscode.dev
4. Verify syntax highlighting with test files
5. Check language server features work correctly

## Publishing Process

1. Update version: `npm version patch|minor|major`
2. Update [CHANGELOG.md](mdc:CHANGELOG.md) with changes
3. Run `npm run package` to ensure clean build
4. Test the .vsix package locally: `code --install-extension *.vsix`
5. Publish: `npm run publish` or `vsce publish`

## Distribution Files

The [.vscodeignore](mdc:.vscodeignore) file controls what gets packaged:
- Include: `dist/`, `syntaxes/`, `snippets/`, `language-configuration.json`
- Exclude: `src/`, `node_modules/`, build files, development tools

## Web Extension Considerations

- No Node.js APIs available in web environment
- Language server runs in web worker
- File system access is limited
- Test thoroughly in vscode.dev before release
description:
globs:
alwaysApply: false
---
