# TypeScript Coding Standards for SLine Extension

## General Guidelines

- Always use semicolons at the end of statements
- Use strict TypeScript configuration as defined in [tsconfig.json](mdc:tsconfig.json)
- Prefer `const` over `let` when variables are not reassigned
- Use meaningful variable and function names
- Add JSDoc comments for public APIs

## VS Code Extension Specific

- Import VS Code types from 'vscode' module: `import * as vscode from 'vscode';`
- Use proper disposal patterns for resources (subscriptions, disposables)
- Follow VS Code API patterns for commands, providers, and language features
- Handle errors gracefully and provide user-friendly error messages

## Language Server Development

- Use `vscode-languageserver` and `vscode-languageclient` packages
- Implement proper LSP message handling
- Validate text document changes before processing
- Cache parsed results when possible for performance

## Example Code Style

```typescript
export function activate(context: vscode.ExtensionContext): void {
    const disposable = vscode.commands.registerCommand('sline.command', () => {
        vscode.window.showInformationMessage('SLine command executed!');
    });
    
    context.subscriptions.push(disposable);
}
```
description:
globs:
alwaysApply: false
---
