# Package.json Maintenance Guidelines

When modifying [package.json](mdc:package.json), follow these guidelines:

## Version Management

- Follow semantic versioning (semver): MAJOR.MINOR.PATCH
- Update version before publishing: `npm version patch|minor|major`
- Keep dependencies up to date but test thoroughly after updates

## Extension Manifest Fields

- `engines.vscode` should specify minimum supported VS Code version (currently ^1.84.0)
- `categories` should include "Programming Languages" and "Snippets"
- `activationEvents` is deprecated - rely on contributes points for activation
- `main` points to Node.js bundle: "./dist/extension.js"
- `browser` points to web bundle: "./dist/web/extension.js"

## Scripts Maintenance

- `vscode:prepublish` runs before packaging - should include production build
- `compile` should use esbuild for both Node.js and web bundles
- `watch` should run parallel builds for development
- `test` should include type checking and extension tests

## Dependencies

- Keep `@types/vscode` aligned with `engines.vscode` version
- Use exact versions for build tools (esbuild, typescript)
- Regular dependencies should be minimal - most code should be bundled

## Keywords for Discoverability

Include relevant keywords: sline, template, handlebars, syntax, highlighting, language-server, intellisense, template-engine
description:
globs:
alwaysApply: false
---
