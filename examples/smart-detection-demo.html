<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{shop.name}} - SLine 智能检测演示</title>
</head>
<body>
    <!-- 
        当您打开这个 .html 文件时，SLine 插件会自动检测到以下 SLine 语法：
        1. 对象属性访问：{{shop.name}}
        2. 条件语句：{{#if}}...{{#else}}...{{/if}}
        3. 循环语句：{{#for}}...{{/for}}
        4. 过滤器使用：{{product.price | money()}}
        5. SLine 注释：{{!-- comment --}}
        
        插件会提示您切换到 SLine 语言模式以获得完整的开发体验！
    -->

    <header class="site-header">
        <h1>{{shop.name}}</h1>
        <nav>
            {{#if customer.logged_in}}
                <a href="{{routes.account_url}}">我的账户</a>
                <a href="{{routes.account_logout_url}}">退出登录</a>
            {{#else}}
                <a href="{{routes.account_login_url}}">登录</a>
                <a href="{{routes.account_register_url}}">注册</a>
            {{/if}}
        </nav>
    </header>

    <main class="main-content">
        <section class="featured-products">
            <h2>精选产品</h2>
            <div class="product-grid">
                {{#for product in collections.featured.products limit:8}}
                    <div class="product-card">
                        <a href="{{product.url}}">
                            <img src="{{product.featured_image | image_url(width=300)}}" 
                                 alt="{{product.title}}" 
                                 loading="lazy">
                        </a>
                        <div class="product-info">
                            <h3><a href="{{product.url}}">{{product.title}}</a></h3>
                            <p class="product-price">
                                {{#if product.compare_at_price}}
                                    <span class="original-price">{{product.compare_at_price | money()}}</span>
                                    <span class="sale-price">{{product.price | money()}}</span>
                                {{#else}}
                                    <span class="price">{{product.price | money()}}</span>
                                {{/if}}
                            </p>
                            {{#if product.available}}
                                <button class="add-to-cart" data-product-id="{{product.id}}">
                                    加入购物车
                                </button>
                            {{#else}}
                                <button class="sold-out" disabled>
                                    售罄
                                </button>
                            {{/if}}
                        </div>
                    </div>
                {{/for}}
            </div>
        </section>

        {{!-- 这是 SLine 注释，也会被检测到 --}}
        <section class="newsletter">
            <h2>订阅我们的新闻</h2>
            <p>获取最新产品信息和优惠活动</p>
            {{#customer_subscribe_form}}
                <div class="form-group">
                    <input type="email" 
                           name="customer[email]" 
                           placeholder="输入您的邮箱地址"
                           required>
                    <button type="submit">订阅</button>
                </div>
                {{#if form.posted_successfully}}
                    <p class="success-message">订阅成功！</p>
                {{/if}}
                {{#if form.errors}}
                    <p class="error-message">{{form.errors.messages}}</p>
                {{/if}}
            {{/customer_subscribe_form}}
        </section>
    </main>

    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>{{shop.name}}</h3>
                <p>{{shop.description | truncate(100)}}</p>
            </div>
            <div class="footer-section">
                <h3>联系我们</h3>
                <p>邮箱：{{shop.email}}</p>
                <p>电话：{{shop.phone}}</p>
            </div>
            <div class="footer-section">
                <h3>关注我们</h3>
                <div class="social-links">
                    {{#if settings.social_facebook_link}}
                        <a href="{{settings.social_facebook_link}}">Facebook</a>
                    {{/if}}
                    {{#if settings.social_twitter_link}}
                        <a href="{{settings.social_twitter_link}}">Twitter</a>
                    {{/if}}
                    {{#if settings.social_instagram_link}}
                        <a href="{{settings.social_instagram_link}}">Instagram</a>
                    {{/if}}
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; {{date | date('%Y')}} {{shop.name}}. 保留所有权利。</p>
        </div>
    </footer>

    <script>
        // 普通的 JavaScript 代码不会触发 SLine 检测
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
        });
    </script>
</body>
</html>
