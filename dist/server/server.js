"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const node_1 = require("vscode-languageserver/node");
const vscode_languageserver_textdocument_1 = require("vscode-languageserver-textdocument");
const referenceData_1 = require("./referenceData");
const contextAnalysis_1 = require("./contextAnalysis");
const snippets_1 = require("./snippets");
const connection = (0, node_1.createConnection)(node_1.ProposedFeatures.all);
const documents = new node_1.TextDocuments(vscode_languageserver_textdocument_1.TextDocument);
let referenceDataManager;
let snippetManager;
let hasConfigurationCapability = false;
let hasWorkspaceFolderCapability = false;
let hasDiagnosticRelatedInformationCapability = false;
connection.onInitialize(async (params) => {
    const capabilities = params.capabilities;
    hasConfigurationCapability = !!(capabilities.workspace && !!capabilities.workspace.configuration);
    hasWorkspaceFolderCapability = !!(capabilities.workspace && !!capabilities.workspace.workspaceFolders);
    hasDiagnosticRelatedInformationCapability = !!(capabilities.textDocument &&
        capabilities.textDocument.publishDiagnostics &&
        capabilities.textDocument.publishDiagnostics.relatedInformation);
    snippetManager = new snippets_1.SnippetManager();
    try {
        const extensionPath = params.rootPath || params.workspaceFolders?.[0]?.uri.replace('file://', '') || process.cwd();
        referenceDataManager = new referenceData_1.ReferenceDataManager(extensionPath);
        await referenceDataManager.loadReferenceData();
        console.log('SLine reference data loaded successfully');
    }
    catch (error) {
        console.error('Failed to initialize SLine reference data manager:', error);
        console.log('Falling back to basic completion functionality');
    }
    const result = {
        capabilities: {
            textDocumentSync: node_1.TextDocumentSyncKind.Incremental,
            completionProvider: {
                resolveProvider: true,
                triggerCharacters: ['|', '#', '{', '.']
            },
            hoverProvider: true,
            signatureHelpProvider: {
                triggerCharacters: ['(', ',', ' ']
            }
        }
    };
    if (hasWorkspaceFolderCapability) {
        result.capabilities.workspace = {
            workspaceFolders: {
                supported: true
            }
        };
    }
    return result;
});
connection.onInitialized(() => {
    if (hasConfigurationCapability) {
        connection.client.register(node_1.DidChangeConfigurationNotification.type, undefined);
    }
    if (hasWorkspaceFolderCapability) {
        connection.workspace.onDidChangeWorkspaceFolders(_event => {
            connection.console.log('Workspace folder change event received.');
        });
    }
});
const defaultSettings = { maxNumberOfProblems: 1000 };
let globalSettings = defaultSettings;
const documentSettings = new Map();
connection.onDidChangeConfiguration(change => {
    if (hasConfigurationCapability) {
        documentSettings.clear();
    }
    else {
        globalSettings = ((change.settings.slineLanguageServer || defaultSettings));
    }
    documents.all().forEach(validateTextDocument);
});
function getDocumentSettings(resource) {
    if (!hasConfigurationCapability) {
        return Promise.resolve(globalSettings);
    }
    let result = documentSettings.get(resource);
    if (!result) {
        result = connection.workspace.getConfiguration({
            scopeUri: resource,
            section: 'slineLanguageServer'
        });
        documentSettings.set(resource, result);
    }
    return result;
}
documents.onDidClose(e => {
    documentSettings.delete(e.document.uri);
});
documents.onDidChangeContent(change => {
    validateTextDocument(change.document);
});
documents.onDidOpen(change => {
    validateTextDocument(change.document);
});
async function validateTextDocument(textDocument) {
    try {
        const settings = await getDocumentSettings(textDocument.uri);
        const maxProblems = settings?.maxNumberOfProblems || defaultSettings.maxNumberOfProblems;
        const text = textDocument.getText();
        const diagnostics = [];
        const problems = findBasicSyntaxErrors(text);
        if (referenceDataManager) {
            problems.push(...findSmartSyntaxErrors(text, textDocument));
        }
        let problemCount = 0;
        for (const problem of problems) {
            if (problemCount >= maxProblems) {
                break;
            }
            let severity = node_1.DiagnosticSeverity.Warning;
            if (problem.message.includes('Unclosed tag') ||
                problem.message.includes('Unmatched brackets') ||
                problem.message.includes('Unexpected closing tag')) {
                severity = node_1.DiagnosticSeverity.Error;
            }
            else if (problem.message.includes('should be self-closing')) {
                severity = node_1.DiagnosticSeverity.Warning;
            }
            else if (problem.message.includes('Incomplete')) {
                severity = node_1.DiagnosticSeverity.Error;
            }
            else if (problem.message.includes('Unknown') || problem.message.includes('Invalid')) {
                severity = node_1.DiagnosticSeverity.Error;
            }
            else if (problem.message.includes('Deprecated')) {
                severity = node_1.DiagnosticSeverity.Warning;
            }
            const diagnostic = {
                severity: severity,
                range: problem.range,
                message: problem.message,
                source: 'SLine',
                code: problem.code || 'syntax-error'
            };
            if (hasDiagnosticRelatedInformationCapability) {
                diagnostic.relatedInformation = [
                    {
                        location: {
                            uri: textDocument.uri,
                            range: problem.range
                        },
                        message: problem.suggestion || 'SLine 语法问题 - 请检查标签语法是否正确'
                    }
                ];
            }
            diagnostics.push(diagnostic);
            problemCount++;
        }
        connection.sendDiagnostics({ uri: textDocument.uri, diagnostics });
        if (diagnostics.length > 0) {
            connection.console.log(`Found ${diagnostics.length} syntax issues in ${textDocument.uri}`);
        }
    }
    catch (error) {
        connection.console.log('Error in validateTextDocument: ' + error);
        connection.sendDiagnostics({ uri: textDocument.uri, diagnostics: [] });
    }
}
function findSmartSyntaxErrors(text, textDocument) {
    const problems = [];
    if (!referenceDataManager) {
        return problems;
    }
    const tagRegex = /\{\{#([a-zA-Z_][a-zA-Z0-9_]*)/g;
    let match;
    while ((match = tagRegex.exec(text)) !== null) {
        const tagName = match[1];
        const startPos = match.index;
        const endPos = startPos + match[0].length;
        if (!referenceDataManager.hasTag(tagName)) {
            const startPosition = textDocument.positionAt(startPos);
            const endPosition = textDocument.positionAt(endPos);
            const suggestions = referenceDataManager.getSimilarTags(tagName);
            let message = `Unknown tag '${tagName}'`;
            let suggestion = 'Check the tag name for typos';
            if (suggestions.length > 0) {
                message += `. Did you mean: ${suggestions.join(', ')}?`;
                suggestion = `Consider using: ${suggestions[0]}`;
            }
            problems.push({
                range: {
                    start: startPosition,
                    end: endPosition
                },
                message: message,
                code: 'unknown-tag',
                suggestion: suggestion
            });
        }
        else {
            const hoverInfo = referenceDataManager.getHoverInfo(tagName);
            if (hoverInfo?.deprecated) {
                const startPosition = textDocument.positionAt(startPos);
                const endPosition = textDocument.positionAt(endPos);
                problems.push({
                    range: {
                        start: startPosition,
                        end: endPosition
                    },
                    message: `Deprecated tag '${tagName}' - consider using an alternative`,
                    code: 'deprecated-tag',
                    suggestion: 'Check documentation for recommended alternatives'
                });
            }
        }
    }
    const filterRegex = /\|\s*([a-zA-Z_][a-zA-Z0-9_]*)/g;
    while ((match = filterRegex.exec(text)) !== null) {
        const matchStart = match.index;
        const pipePosition = matchStart;
        const prevChar = pipePosition > 0 ? text[pipePosition - 1] : '';
        const nextChar = pipePosition < text.length - 1 ? text[pipePosition + 1] : '';
        if (prevChar === '|' || nextChar === '|') {
            continue;
        }
        const filterName = match[1];
        const startPos = match.index + match[0].indexOf(filterName);
        const endPos = startPos + filterName.length;
        if (!referenceDataManager.hasFilter(filterName)) {
            const startPosition = textDocument.positionAt(startPos);
            const endPosition = textDocument.positionAt(endPos);
            const suggestions = referenceDataManager.getSimilarFilters(filterName);
            let message = `Unknown filter '${filterName}'`;
            let suggestion = 'Check the filter name for typos';
            if (suggestions.length > 0) {
                message += `. Did you mean: ${suggestions.join(', ')}?`;
                suggestion = `Consider using: ${suggestions[0]}`;
            }
            problems.push({
                range: {
                    start: startPosition,
                    end: endPosition
                },
                message: message,
                code: 'unknown-filter',
                suggestion: suggestion
            });
        }
        else {
            const hoverInfo = referenceDataManager.getHoverInfo(filterName);
            if (hoverInfo?.deprecated) {
                const startPosition = textDocument.positionAt(startPos);
                const endPosition = textDocument.positionAt(endPos);
                problems.push({
                    range: {
                        start: startPosition,
                        end: endPosition
                    },
                    message: `Deprecated filter '${filterName}' - consider using an alternative`,
                    code: 'deprecated-filter',
                    suggestion: 'Check documentation for recommended alternatives'
                });
            }
        }
    }
    return problems;
}
function calculateEditDistance(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;
    for (let i = 0; i <= len1; i++) {
        matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
        matrix[0][j] = j;
    }
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            if (str1[i - 1] === str2[j - 1]) {
                matrix[i][j] = matrix[i - 1][j - 1];
            }
            else {
                matrix[i][j] = Math.min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j - 1] + 1);
            }
        }
    }
    return matrix[len1][len2];
}
function checkSelfClosingTags(text) {
    const problems = [];
    try {
        const lines = text.split('\n');
        const allTags = [];
        for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
            const line = lines[lineIndex];
            const startTagRegex = /\{\{#([a-zA-Z_][a-zA-Z0-9_]*)\s*[^}]*\}\}/g;
            let startMatch;
            while ((startMatch = startTagRegex.exec(line)) !== null) {
                const tagName = startMatch[1];
                const fullMatch = startMatch[0];
                const isSelfClosing = fullMatch.endsWith('/}}');
                allTags.push({
                    name: tagName,
                    line: lineIndex,
                    character: startMatch.index,
                    fullMatch: fullMatch,
                    isSelfClosing: isSelfClosing,
                    isClosingTag: false
                });
            }
            const endTagRegex = /\{\{\/([a-zA-Z_][a-zA-Z0-9_]*)\}\}/g;
            let endMatch;
            while ((endMatch = endTagRegex.exec(line)) !== null) {
                const tagName = endMatch[1];
                allTags.push({
                    name: tagName,
                    line: lineIndex,
                    character: endMatch.index,
                    fullMatch: endMatch[0],
                    isSelfClosing: false,
                    isClosingTag: true
                });
            }
        }
        const tagStack = [];
        const unmatchedStartTags = [];
        for (const tag of allTags) {
            if (tag.isClosingTag) {
                let matched = false;
                for (let i = tagStack.length - 1; i >= 0; i--) {
                    if (tagStack[i].name === tag.name) {
                        tagStack.splice(i, 1);
                        matched = true;
                        break;
                    }
                }
                if (!matched) {
                }
            }
            else {
                if (tag.isSelfClosing) {
                    continue;
                }
                else {
                    tagStack.push(tag);
                }
            }
        }
        for (const unmatchedTag of tagStack) {
            problems.push({
                range: {
                    start: { line: unmatchedTag.line, character: unmatchedTag.character },
                    end: {
                        line: unmatchedTag.line,
                        character: unmatchedTag.character + unmatchedTag.fullMatch.length
                    }
                },
                message: `Tag '${unmatchedTag.name}' is not self-closing and has no matching closing tag. Either add '{{/${unmatchedTag.name}}}' or make it self-closing with '/}}'`
            });
        }
    }
    catch (error) {
        console.error('Error in checkSelfClosingTags:', error);
    }
    return problems;
}
function findBasicSyntaxErrors(text) {
    const problems = [];
    try {
        const lines = text.split('\n');
        let openBrackets = 0;
        const selfClosingErrors = checkSelfClosingTags(text);
        problems.push(...selfClosingErrors);
        const unclosedExpressions = [];
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const openMatches = line.match(/\{\{/g);
            const closeMatches = line.match(/\}\}/g);
            if (openMatches) {
                let startIndex = 0;
                for (let j = 0; j < openMatches.length; j++) {
                    startIndex = line.indexOf('{{', startIndex);
                    if (startIndex >= 0) {
                        unclosedExpressions.push({ line: i, character: startIndex });
                        openBrackets++;
                        startIndex += 2;
                    }
                }
            }
            if (closeMatches) {
                const closeCount = Math.min(closeMatches.length, unclosedExpressions.length);
                for (let j = 0; j < closeCount; j++) {
                    unclosedExpressions.pop();
                    openBrackets--;
                }
            }
        }
        for (const unclosedExpr of unclosedExpressions) {
            problems.push({
                range: {
                    start: { line: unclosedExpr.line, character: unclosedExpr.character },
                    end: { line: unclosedExpr.line, character: unclosedExpr.character + 2 }
                },
                message: 'Incomplete Sline expression - missing closing }}'
            });
        }
    }
    catch (error) {
        connection.console.log('Error in syntax checking: ' + error);
    }
    return problems;
}
connection.onDidChangeWatchedFiles(_change => {
    connection.console.log('We received a file change event');
});
const SLINE_KEYWORDS = [
    'if', 'each', 'unless', 'with', 'layout', 'component', 'content', 'section', 'var',
    'schema', 'else', 'elseif'
];
function getBasicCompletions(document, textDocumentPosition) {
    const text = document.getText();
    const offset = document.offsetAt(textDocumentPosition.position);
    const beforeCursor = text.substring(0, offset);
    const afterCursor = text.substring(offset);
    const lastOpenBrace = beforeCursor.lastIndexOf('{{');
    const lastCloseBrace = beforeCursor.lastIndexOf('}}');
    const nextCloseBrace = afterCursor.indexOf('}}');
    const insideBraces = lastOpenBrace !== -1 &&
        (lastCloseBrace === -1 || lastOpenBrace > lastCloseBrace) &&
        nextCloseBrace !== -1;
    if (!insideBraces) {
        return [];
    }
    const expressionContent = beforeCursor.substring(lastOpenBrace + 2);
    const pipeIndex = contextAnalysis_1.ContextAnalyzer.findLastPipeIndex(expressionContent);
    if (pipeIndex !== -1) {
        const afterPipe = expressionContent.substring(pipeIndex + 1).trim();
        if (referenceDataManager) {
            return referenceDataManager.getFilterCompletions(afterPipe);
        }
        return [];
    }
    const trimmedExpression = expressionContent.trim();
    if (trimmedExpression.startsWith('#')) {
        const searchTerm = trimmedExpression.replace('#', '').toLowerCase();
        if (referenceDataManager) {
            return referenceDataManager.getTagCompletions(searchTerm);
        }
        return [];
    }
    if (trimmedExpression === '') {
        const matchingKeywords = SLINE_KEYWORDS.map((keyword, index) => {
            const isHelper = ['layout', 'component', 'content', 'section', 'var', 'schema'].includes(keyword);
            return {
                label: keyword,
                kind: isHelper ? node_1.CompletionItemKind.Function : node_1.CompletionItemKind.Keyword,
                data: SLINE_KEYWORDS.indexOf(keyword) + 1,
                insertText: keyword,
                detail: `Sline ${isHelper ? '助手' : '关键词'}`,
                sortText: `2${index.toString().padStart(3, '0')}`
            };
        });
        return matchingKeywords;
    }
    return [];
}
connection.onCompletion((textDocumentPosition) => {
    const document = documents.get(textDocumentPosition.textDocument.uri);
    if (!document || !referenceDataManager) {
        if (!document) {
            return [];
        }
        return getBasicCompletions(document, textDocumentPosition);
    }
    const context = contextAnalysis_1.ContextAnalyzer.analyzeCompletionContext(document, textDocumentPosition.position);
    switch (context.type) {
        case 'tag':
            return referenceDataManager.getTagCompletions(context.prefix);
        case 'filter':
            return referenceDataManager.getFilterCompletions(context.prefix);
        case 'object_property':
            if (context.objectName) {
                return referenceDataManager.getObjectPropertyCompletions(context.objectName, context.prefix);
            }
            return referenceDataManager.getAllObjectNames()
                .filter(name => name.toLowerCase().startsWith(context.prefix.toLowerCase()))
                .map(name => ({
                label: name,
                kind: node_1.CompletionItemKind.Class,
                detail: `Sline Object`,
                insertText: name,
                data: { type: 'object', name }
            }));
        case 'tag_parameter':
            if (context.tagName) {
                return referenceDataManager.getTagParameterCompletions(context.tagName, context.prefix);
            }
            break;
        case 'filter_parameter':
            if (context.filterName) {
                return referenceDataManager.getFilterParameterCompletions(context.filterName, context.prefix);
            }
            break;
        default:
            const suggestions = [];
            if (snippetManager) {
                suggestions.push(...snippetManager.getSnippetCompletions(context.prefix).slice(0, 5));
            }
            suggestions.push(...referenceDataManager.getTagCompletions('').slice(0, 10));
            const commonObjects = ['product', 'cart', 'customer', 'shop', 'blog', 'article'];
            for (const objName of commonObjects) {
                if (referenceDataManager.hasObject(objName)) {
                    suggestions.push({
                        label: objName,
                        kind: node_1.CompletionItemKind.Class,
                        detail: `Sline Object`,
                        insertText: objName,
                        data: { type: 'object', name: objName }
                    });
                }
            }
            return suggestions;
    }
    return [];
});
connection.onHover((params) => {
    const document = documents.get(params.textDocument.uri);
    if (!document || !referenceDataManager) {
        return null;
    }
    if (!contextAnalysis_1.ContextAnalyzer.isInSlineExpression(document, params.position)) {
        return null;
    }
    const wordRange = contextAnalysis_1.ContextAnalyzer.getWordRangeAtPosition(document, params.position);
    const word = document.getText(wordRange);
    if (!word) {
        return null;
    }
    const context = contextAnalysis_1.ContextAnalyzer.analyzeCompletionContext(document, params.position);
    const hoverInfo = referenceDataManager.getHoverInfo(word, context);
    if (hoverInfo) {
        let content = `# ${hoverInfo.name}\n\n${hoverInfo.summary}\n\n`;
        if (hoverInfo.syntax) {
            content += `## Syntax\n\`\`\`sline\n${hoverInfo.syntax}\n\`\`\`\n\n`;
        }
        if (hoverInfo.parameters && hoverInfo.parameters.length > 0) {
            content += `## Parameters\n`;
            for (const param of hoverInfo.parameters) {
                content += `- **${param.name}** (${param.types.join(' | ')}): ${param.description}\n`;
            }
            content += '\n';
        }
        if (hoverInfo.returnType) {
            content += `## Returns\n**${hoverInfo.returnType}**\n\n`;
        }
        if (hoverInfo.examples && hoverInfo.examples.length > 0) {
            content += `## Example\n\`\`\`sline\n${hoverInfo.examples[0].raw_sline}\n\`\`\`\n\n`;
        }
        if (hoverInfo.deprecated) {
            content = `⚠️ **Deprecated** ⚠️\n\n${content}`;
        }
        content += `[📖 Documentation](${hoverInfo.link})`;
        return {
            contents: {
                kind: node_1.MarkupKind.Markdown,
                value: content
            },
            range: wordRange
        };
    }
    return null;
});
connection.onSignatureHelp((params) => {
    const document = documents.get(params.textDocument.uri);
    if (!document || !referenceDataManager) {
        return null;
    }
    if (!contextAnalysis_1.ContextAnalyzer.isInSlineExpression(document, params.position)) {
        return null;
    }
    const context = contextAnalysis_1.ContextAnalyzer.analyzeCompletionContext(document, params.position);
    let signatures = [];
    let activeSignature = 0;
    let activeParameter = 0;
    if (context.type === 'tag_parameter' && context.tagName) {
        const hoverInfo = referenceDataManager.getHoverInfo(context.tagName);
        if (hoverInfo && hoverInfo.parameters) {
            const parameters = hoverInfo.parameters.map(param => ({
                label: `${param.name}: ${param.types.join(' | ')}`,
                documentation: param.description
            }));
            signatures.push({
                label: `${context.tagName}(${hoverInfo.parameters.map(p => p.name).join(', ')})`,
                documentation: hoverInfo.summary,
                parameters: parameters
            });
            const expression = contextAnalysis_1.ContextAnalyzer.getCurrentExpression(document, params.position);
            if (expression) {
                const tagMatch = expression.match(new RegExp(`#${context.tagName}\\s+(.*)$`));
                if (tagMatch) {
                    const paramText = tagMatch[1];
                    const paramCount = (paramText.match(/\s+/g) || []).length;
                    activeParameter = Math.min(paramCount, parameters.length - 1);
                }
            }
        }
    }
    else if (context.type === 'filter_parameter' && context.filterName) {
        const hoverInfo = referenceDataManager.getHoverInfo(context.filterName);
        if (hoverInfo && hoverInfo.parameters) {
            const parameters = hoverInfo.parameters.map(param => ({
                label: `${param.name}: ${param.types.join(' | ')}`,
                documentation: param.description
            }));
            signatures.push({
                label: `${context.filterName}(${hoverInfo.parameters.map(p => p.name).join(', ')})`,
                documentation: hoverInfo.summary,
                parameters: parameters
            });
            const expression = contextAnalysis_1.ContextAnalyzer.getCurrentExpression(document, params.position);
            if (expression) {
                const filterMatch = expression.match(new RegExp(`\\|\\s*${context.filterName}\\s*\\(([^)]*)$`));
                if (filterMatch) {
                    const paramText = filterMatch[1];
                    const paramCount = (paramText.match(/,/g) || []).length;
                    activeParameter = Math.min(paramCount, parameters.length - 1);
                }
            }
        }
    }
    if (signatures.length > 0) {
        return {
            signatures: signatures,
            activeSignature: activeSignature,
            activeParameter: activeParameter
        };
    }
    return null;
});
connection.onCompletionResolve((item) => {
    switch (item.data) {
        case 1:
            item.detail = 'if 条件块';
            item.documentation = '条件判断块: {{#if condition}}...{{/if}}';
            break;
        case 2:
            item.detail = 'each 循环块';
            item.documentation = '遍历数组: {{#each items}}...{{/each}}';
            break;
        case 3:
            item.detail = 'unless 反向条件块';
            item.documentation = '反向条件判断: {{#unless condition}}...{{/unless}}';
            break;
        case 4:
            item.detail = 'with 上下文块';
            item.documentation = '上下文切换: {{#with object}}...{{/with}}';
            break;
        case 5:
            item.detail = 'layout 布局助手';
            item.documentation = '布局组件: {{#layout "name" /}}';
            break;
        case 6:
            item.detail = 'component 组件助手';
            item.documentation = '组件引用: {{#component "name" /}}';
            break;
        case 7:
            item.detail = 'content 内容助手';
            item.documentation = '内容区域: {{#content "name" /}}';
            break;
        case 8:
            item.detail = 'section 区块助手';
            item.documentation = '区块定义: {{#section "name"}}...{{/section}}';
            break;
        case 9:
            item.detail = 'var 变量助手';
            item.documentation = '变量定义: {{#var name="value"}}';
            break;
        case 10:
            item.detail = 'schema 模式助手';
            item.documentation = '数据模式: {{#schema}}...{{/schema}}';
            break;
        case 11:
            item.detail = 'else 否则块';
            item.documentation = 'else 分支: {{#if}}...{{else}}...{{/if}}';
            break;
        case 12:
            item.detail = 'elseif 否则如果块';
            item.documentation = 'elseif 分支: {{#if}}...{{elseif condition}}...{{/if}}';
            break;
    }
    return item;
});
documents.listen(connection);
connection.listen();
