"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextAnalyzer = void 0;
class ContextAnalyzer {
    static analyzeCompletionContext(document, position) {
        const text = document.getText();
        const offset = document.offsetAt(position);
        const beforeCursor = text.substring(0, offset);
        const afterCursor = text.substring(offset);
        const lastOpenBrace = beforeCursor.lastIndexOf('{{');
        const lastCloseBrace = beforeCursor.lastIndexOf('}}');
        if (lastCloseBrace > lastOpenBrace) {
            return { type: 'unknown', prefix: '' };
        }
        if (lastOpenBrace === -1) {
            return { type: 'unknown', prefix: '' };
        }
        const nextCloseBrace = afterCursor.indexOf('}}');
        const isInAutoCompletedBraces = nextCloseBrace === 0;
        const expressionContent = beforeCursor.substring(lastOpenBrace + 2);
        if (isInAutoCompletedBraces && expressionContent.length <= 3) {
            if (expressionContent === '#') {
                return {
                    type: 'tag',
                    prefix: ''
                };
            }
            if (expressionContent.startsWith('#') && expressionContent.length > 1) {
                return {
                    type: 'tag',
                    prefix: expressionContent.substring(1)
                };
            }
            if (expressionContent.trim() === '') {
                return {
                    type: 'unknown',
                    prefix: ''
                };
            }
        }
        return this.analyzeExpressionContent(expressionContent);
    }
    static analyzeExpressionContent(content) {
        const trimmedContent = content.trim();
        const propertyMatch = trimmedContent.match(/([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)$/);
        if (propertyMatch) {
            return {
                type: 'object_property',
                prefix: propertyMatch[2],
                objectName: propertyMatch[1]
            };
        }
        const objectAccessMatch = trimmedContent.match(/([a-zA-Z_][a-zA-Z0-9_]*)\.$/);
        if (objectAccessMatch) {
            return {
                type: 'object_property',
                prefix: '',
                objectName: objectAccessMatch[1]
            };
        }
        const pipeIndex = ContextAnalyzer.findLastPipeIndex(trimmedContent);
        if (pipeIndex !== -1) {
            const afterPipe = trimmedContent.substring(pipeIndex + 1).trim();
            const filterParamMatch = afterPipe.match(/([a-zA-Z_][a-zA-Z0-9_]*)\s*\(\s*([a-zA-Z_][a-zA-Z0-9_]*=?)?$/);
            if (filterParamMatch) {
                return {
                    type: 'filter_parameter',
                    prefix: filterParamMatch[2] || '',
                    filterName: filterParamMatch[1]
                };
            }
            const filterMatch = afterPipe.match(/^([a-zA-Z_][a-zA-Z0-9_]*)$/);
            if (filterMatch) {
                return {
                    type: 'filter',
                    prefix: filterMatch[1]
                };
            }
            return {
                type: 'filter',
                prefix: afterPipe
            };
        }
        if (trimmedContent.startsWith('#')) {
            const tagContent = trimmedContent.substring(1);
            const tagParamMatch = tagContent.match(/^([a-zA-Z_][a-zA-Z0-9_]*)\s+(.*)$/);
            if (tagParamMatch) {
                const tagName = tagParamMatch[1];
                const paramContent = tagParamMatch[2];
                const paramMatch = paramContent.match(/([a-zA-Z_][a-zA-Z0-9_]*=?)?$/);
                if (paramMatch) {
                    return {
                        type: 'tag_parameter',
                        prefix: paramMatch[1] || '',
                        tagName: tagName
                    };
                }
            }
            const tagNameMatch = tagContent.match(/^([a-zA-Z_][a-zA-Z0-9_]*)$/);
            if (tagNameMatch) {
                return {
                    type: 'tag',
                    prefix: tagNameMatch[1]
                };
            }
            return {
                type: 'tag',
                prefix: tagContent
            };
        }
        const variableMatch = trimmedContent.match(/^([a-zA-Z_][a-zA-Z0-9_]*)$/);
        if (variableMatch) {
            return {
                type: 'unknown',
                prefix: variableMatch[1]
            };
        }
        if (trimmedContent === '') {
            return {
                type: 'tag',
                prefix: ''
            };
        }
        return { type: 'unknown', prefix: trimmedContent };
    }
    static getWordRangeAtPosition(document, position) {
        const text = document.getText();
        const offset = document.offsetAt(position);
        let start = offset;
        let end = offset;
        while (start > 0 && this.isWordCharacter(text.charAt(start - 1))) {
            start--;
        }
        while (end < text.length && this.isWordCharacter(text.charAt(end))) {
            end++;
        }
        return {
            start: document.positionAt(start),
            end: document.positionAt(end)
        };
    }
    static isWordCharacter(char) {
        return /[a-zA-Z0-9_]/.test(char);
    }
    static isInSlineExpression(document, position) {
        const text = document.getText();
        const offset = document.offsetAt(position);
        const beforeCursor = text.substring(0, offset);
        const lastOpenBrace = beforeCursor.lastIndexOf('{{');
        const lastCloseBrace = beforeCursor.lastIndexOf('}}');
        return lastOpenBrace > lastCloseBrace;
    }
    static getCurrentExpression(document, position) {
        const text = document.getText();
        const offset = document.offsetAt(position);
        let start = offset;
        while (start >= 0 && text.substring(start, start + 2) !== '{{') {
            start--;
        }
        if (start < 0) {
            return null;
        }
        let end = offset;
        while (end < text.length - 1 && text.substring(end, end + 2) !== '}}') {
            end++;
        }
        if (end >= text.length - 1) {
            return text.substring(start + 2);
        }
        return text.substring(start + 2, end);
    }
    static isInTag(document, position) {
        const expression = this.getCurrentExpression(document, position);
        if (!expression) {
            return { inTag: false };
        }
        const trimmed = expression.trim();
        if (trimmed.startsWith('#')) {
            const tagMatch = trimmed.match(/^#([a-zA-Z_][a-zA-Z0-9_]*)/);
            if (tagMatch) {
                return { inTag: true, tagName: tagMatch[1] };
            }
        }
        return { inTag: false };
    }
    static isInFilterChain(document, position) {
        const expression = this.getCurrentExpression(document, position);
        if (!expression) {
            return { inFilter: false };
        }
        const pipeIndex = ContextAnalyzer.findLastPipeIndex(expression);
        if (pipeIndex !== -1) {
            const afterPipe = expression.substring(pipeIndex + 1).trim();
            const filterMatch = afterPipe.match(/^([a-zA-Z_][a-zA-Z0-9_]*)/);
            if (filterMatch) {
                return { inFilter: true, filterName: filterMatch[1] };
            }
            return { inFilter: true };
        }
        return { inFilter: false };
    }
    static analyzeNestedContext(document, position) {
        const text = document.getText();
        const currentOffset = document.offsetAt(position);
        const result = {
            inLoop: false,
            loopVariable: undefined,
            inConditional: false,
            availableVariables: []
        };
        const tagRegex = /\{\{#(for|if|unless|with)\s+([^}]+)\}\}/g;
        let match;
        while ((match = tagRegex.exec(text)) !== null) {
            const tagStart = match.index;
            if (tagStart < currentOffset) {
                const tagType = match[1];
                const tagContent = match[2];
                if (tagType === 'for') {
                    const forMatch = tagContent.match(/(\w+)\s+in\s+(\w+)/);
                    if (forMatch) {
                        result.inLoop = true;
                        result.loopVariable = forMatch[1];
                        result.availableVariables.push(forMatch[1]);
                        result.availableVariables.push('forloop');
                    }
                }
                else if (tagType === 'if' || tagType === 'unless') {
                    result.inConditional = true;
                }
                else if (tagType === 'with') {
                    const withMatch = tagContent.match(/(\w+)\s+as\s+(\w+)/);
                    if (withMatch) {
                        result.availableVariables.push(withMatch[2]);
                    }
                }
            }
        }
        return result;
    }
    static findLastPipeIndex(content) {
        let lastPipeIndex = -1;
        for (let i = content.length - 1; i >= 0; i--) {
            if (content[i] === '|') {
                const prevChar = i > 0 ? content[i - 1] : '';
                const nextChar = i < content.length - 1 ? content[i + 1] : '';
                if (prevChar === '|' || nextChar === '|') {
                    continue;
                }
                lastPipeIndex = i;
                break;
            }
        }
        return lastPipeIndex;
    }
}
exports.ContextAnalyzer = ContextAnalyzer;
