{"version": "2.0.0", "tasks": [{"label": "watch", "dependsOn": ["npm: watch:tsc", "npm: watch:esbuild"], "presentation": {"reveal": "never"}, "group": {"kind": "build", "isDefault": true}, "runOptions": {"runOn": "folderOpen"}}, {"type": "npm", "script": "watch:esbuild", "group": "build", "problemMatcher": "$esbuild-watch", "isBackground": true, "label": "npm: watch:esbuild", "presentation": {"group": "watch", "reveal": "never"}}, {"type": "npm", "script": "watch:tsc", "group": "build", "problemMatcher": "$tsc-watch", "isBackground": true, "label": "npm: watch:tsc", "presentation": {"group": "watch", "reveal": "never"}}, {"type": "npm", "script": "compile", "group": "build", "label": "npm: compile", "problemMatcher": ["$esbuild", "$tsc"]}, {"type": "npm", "script": "package", "group": "build", "label": "npm: package", "problemMatcher": ["$esbuild", "$tsc"]}, {"type": "npm", "script": "compile:all", "group": "build", "label": "npm: compile:all", "detail": "Compile extension and server", "problemMatcher": ["$esbuild", "$tsc"]}, {"label": "compile:all", "type": "shell", "command": "npm", "args": ["run", "compile:all"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$esbuild", "$tsc"]}, {"type": "npm", "script": "compile:server", "group": "build", "label": "npm: compile:server", "problemMatcher": "$tsc"}]}