{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/dist/**/*.js"], "preLaunchTask": "compile:all"}, {"name": "Run Extension Tests", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--extensionTestsPath=${workspaceFolder}/dist/test"], "outFiles": ["${workspaceFolder}/dist/test/**/*.js"], "preLaunchTask": "compile:all"}, {"name": "Debug Language Server", "type": "node", "request": "attach", "port": 6009, "restart": true, "outFiles": ["${workspaceFolder}/dist/server/**/*.js"]}]}