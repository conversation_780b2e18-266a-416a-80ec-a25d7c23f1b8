#!/bin/bash

# Build and Package Script for SLine VSCode Extension

set -e

echo "🚀 Building SLine Highlight Extension..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf out
rm -f *.vsix

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Lint code
echo "🔍 Linting code..."
npm run lint || true

# Compile TypeScript
echo "🔨 Compiling TypeScript..."
npm run compile:all

# Run tests
echo "🧪 Running tests..."
npm test || true

# Package extension
echo "📦 Packaging extension..."
npm run package

echo "✅ Build completed successfully!"
echo "📋 Extension package created: $(ls *.vsix)"

# Show package info
echo "📊 Package information:"
npx vsce ls
