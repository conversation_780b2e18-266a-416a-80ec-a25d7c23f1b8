{{!-- 测试着色一致性修复 --}}

{{!-- 1. 自闭合标签后面跟非自闭合标签 --}}
{{#layout "theme" /}}
{{#if user.logged_in}}
  <p>用户已登录</p>
{{/if}}

{{#var title = "页面标题" /}}
{{#each products}}
  <div>{{this.title}}</div>
{{/each}}

{{#component "button" text="点击我" /}}
{{#unless disabled}}
  <button>启用的按钮</button>
{{/unless}}

{{!-- 2. 多个自闭合标签后面跟非自闭合标签 --}}
{{#layout "theme" /}}
{{#var title = "标题" /}}
{{#stylesheet "main.css" /}}
{{#script "app.js" /}}
{{#if user.admin}}
  <p>管理员面板</p>
{{/if}}

{{!-- 3. 混合嵌套结构 --}}
{{#layout "main" /}}
{{#if user.logged_in}}
  {{#var greeting = "欢迎回来" /}}
  {{#component "user-info" user=user /}}
  {{#each user.notifications}}
    <div class="notification">
      {{#component "notification-item" notification=this /}}
      {{#if this.unread}}
        <span class="unread-badge">新</span>
      {{/if}}
    </div>
  {{/each}}
{{/if}}

{{!-- 4. 验证 {{ 和 }} 的着色一致性 --}}
{{#content "header" /}}
{{#if condition}}
  <p>{{ 和 }} 应该有相同的颜色</p>
{{/if}}

{{#section "main" /}}
{{#each items}}
  <div>{{ 和 }} 应该有相同的颜色</div>
{{/each}}

{{#meta-tags title="测试页面" /}}
{{#unless disabled}}
  <p>{{ 和 }} 应该有相同的颜色</p>
{{/unless}} 