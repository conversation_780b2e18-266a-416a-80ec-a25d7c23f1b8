{{!-- 测试统一的语法错误检测 --}}

{{!-- ❌ 以下这些错误应该都显示为红色背景 + 错误提示 --}}

{{!-- 1. 缺少斜杠的自闭合标签 --}}
{{#layout "theme"}}
{{#var title = "测试标题"}}
{{#component "button" text="点击我" type="primary"}}
{{#content "header"}}
{{#section "main"}}
{{#stylesheet "main.css"}}
{{#script "app.js"}}
{{#meta-tags title="页面标题"}}

{{!-- 2. 不完整的标签 --}}
{{#layout "theme"
{{#var title = "测试"
{{#component "button"

{{!-- 3. 未闭合的非自闭合标签 --}}
{{#if user.logged_in}}
  <p>用户已登录</p>
  
{{#each products}}
  <div>产品：{{this.name}}</div>
  
{{#unless disabled}}
  <p>功能启用</p>

{{!-- ✅ 以下是正确的语法 --}}

{{!-- 1. 正确的自闭合标签 --}}
{{#layout "theme" /}}
{{#var title = "测试标题" /}}
{{#component "button" text="点击我" type="primary" /}}
{{#content "header" /}}
{{#section "main" /}}
{{#stylesheet "main.css" /}}
{{#script "app.js" /}}
{{#meta-tags title="页面标题" /}}

{{!-- 2. 正确的非自闭合标签 --}}
{{#if user.logged_in}}
  <p>用户已登录</p>
{{/if}}

{{#each products}}
  <div>产品：{{this.name}}</div>
{{/each}}

{{#unless disabled}}
  <p>功能启用</p>
{{/unless}}

{{!-- 3. 正确的嵌套结构 --}}
{{#if user.logged_in}}
  {{#each user.orders}}
    <div>
      {{#component "order-item" order=this /}}
      {{#if this.status == "pending"}}
        <p>待处理</p>
      {{/if}}
    </div>
  {{/each}}
{{/if}} 