{{!-- SLine LSP 测试文件 --}}
{{!-- 用于测试语言服务器功能 --}}
{{!-- 请在这里测试自动完成功能 --}}

{{#layout "theme" /}}

{{#var page_title = "LSP 测试页面" /}}

<div class="test-container">
    {{!-- 测试变量输出 --}}
    <h1>{{page_title}}</h1>

    {{!-- 测试条件语句 --}}
    {{#if customer}}
        <p>欢迎，{{customer.first_name}}！</p>
    {{#else}}
        <p>请先登录</p>
    {{/if}}

    {{!-- 测试循环语句 --}}
    {{#each products}}
        <div class="product">
            <h3>{{title}}</h3>
            <p>价格: {{price | money}}</p>
        </div>
    {{/each}}

    {{!-- 测试组件调用 --}}
    {{#component "button"
        type="link"
        text="点击我"
        link_url="/test"
    /}}

    {{!-- 测试未定义的组件（应该显示警告） --}}
    {{#component "unknown-component" /}}

    {{!-- 测试未闭合的标签（应该显示错误） --}}
    {{#if test_condition}}
        <p>这个标签没有正确闭合</p>

    {{!-- 测试过滤器 --}}
    <p>日期: {{created_at | date('Y-m-d')}}</p>
    <p>资源: {{image | asset_url()}}</p>
    <p>翻译: {{"general.title" | t()}}</p>

    {{!-- 测试区域 1: 在下面这行测试自动完成，输入 {{# 然后看提示 --}}
    {{!-- 应该看到：if, each, for, with, unless, component, layout, content, section, var, schema 等 --}}


    {{!-- 测试区域 2: 在下面这行测试过滤器完成，输入 {{value | 然后看提示 --}}
    {{!-- 应该看到：money, asset_url, date, upper, lower, truncate, default, t 等 --}}


    {{!-- 测试区域 3: 在下面这行测试变量完成，输入 {{ 然后看提示 --}}
    {{!-- 应该看到：shop, customer, request, settings, product, article, page 等 --}}


    {{!-- 测试区域 4: 在下面这行测试组件名称完成，输入 {{#component " 然后看提示 --}}
    {{!-- 应该看到：button, header, footer, page-head 等组件名称 --}}


    {{!-- 测试区域 5: 测试悬停信息，将鼠标悬停在下面的关键字上 --}}
    {{#if customer}}
        {{#each products}}
            {{title | upper}}
            {{price | money}}
        {{/each}}
    {{/if}}

    {{!-- 测试区域 6: 测试更多 SLine 语法 --}}
    {{#with shop}}
        {{name}}
    {{/with}}

    {{#unless customer}}
        <p>请登录</p>
    {{/unless}}

    {{#capture page_title}}
        {{shop.name}} - {{page.title}}
    {{/capture}}

    {{#blocks}}
        {{#block /}}
    {{/blocks}}

</div>

{{!-- 测试 Schema 配置 --}}
{{#schema}}
{
  "name": "LSP 测试页面",
  "settings": [
    {
      "type": "text",
      "id": "test_setting",
      "label": "测试设置"
    }
  ]
}
{{/schema}}
