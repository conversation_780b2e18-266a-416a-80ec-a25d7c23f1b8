{{!-- 测试 capture 标签修复 --}}

{{!-- ✅ 这些应该显示正常的语法高亮，不应该报错 --}}

{{#capture total_number}}
  {{#if cart && cart.item_count > 0}}
    {{#if cart.item_count > 99}}99+{{#else /}}{{cart.item_count}}{{/if}}
  {{/if}}
{{/capture}}

{{#capture subtitle}}
  这是一个副标题
{{/capture}}

{{#capture header_content}}
  <h1>{{site.title}}</h1>
  <p>{{site.description}}</p>
{{/capture}}

{{!-- 使用 capture 的内容 --}}
<span class="cart-count">{{total_number}}</span>
<div class="subtitle">{{subtitle}}</div>
<header>{{header_content}}</header>

{{!-- ❌ 这些仍然应该报错 --}}
{{#layout "theme"}}  {{!-- 缺少 /}} --}}
{{#var title = "test"}}  {{!-- 缺少 /}} --}}
{{#component "button"}}  {{!-- 缺少 /}} --}} 