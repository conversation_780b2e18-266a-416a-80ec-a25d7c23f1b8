{{!-- 测试自闭合和非自闭合标签混排的颜色一致性 --}}

{{!-- ✅ 所有非自闭合标签的 {{ 应该有相同的颜色 --}}
{{#if user.logged_in}}
  <p>用户已登录</p>
{{/if}}

{{#each products}}
  <div>产品：{{this.name}}</div>
{{/each}}

{{#unless disabled}}
  <p>功能启用</p>
{{/unless}}

{{#with user}}
  <p>用户名：{{name}}</p>
{{/with}}

{{#for item in items}}
  <div>{{item.title}}</div>
{{/for}}

{{!-- 混排测试 --}}
{{#layout "theme" /}}
{{#if user.admin}}
  {{#var greeting = "欢迎管理员" /}}
  {{#component "admin-panel" /}}
  {{#each user.permissions}}
    <div>权限：{{this}}</div>
  {{/each}}
{{/if}}

{{#content "main" /}}
{{#unless user.banned}}
  {{#stylesheet "user.css" /}}
  {{#with user.profile}}
    <div>个人资料：{{name}}</div>
  {{/with}}
{{/unless}}

{{#script "app.js" /}}
{{#for notification in notifications}}
  <div class="notification">{{notification.message}}</div>
{{/for}}

{{!-- 嵌套混排测试 --}}
{{#section "content" /}}
{{#if user.verified}}
  {{#meta-tags title="验证用户" /}}
  {{#each user.badges}}
    {{#component "badge" badge=this /}}
    {{#unless this.hidden}}
      <span>{{this.name}}</span>
    {{/unless}}
  {{/each}}
{{/if}}

{{!-- ❌ 错误语法测试 --}}
{{#layout "error"}}
{{#var title = "错误"}}
{{#component "error-display"}} 