{{!-- 测试无空格自闭合标签和着色一致性 --}}

{{!-- 1. 测试无空格自闭合标签（应该正确识别） --}}
{{#layout "theme"/}}
{{#var title="页面标题"/}}
{{#component "button" text="点击我"/}}
{{#content "header"/}}
{{#section "main"/}}

{{!-- 2. 测试有空格的自闭合标签 --}}
{{#layout "theme" /}}
{{#var title="页面标题" /}}
{{#component "button" text="点击我" /}}
{{#content "header" /}}
{{#section "main" /}}

{{!-- 3. 测试非自闭合标签的着色一致性 --}}
{{#layout "theme" /}}
{{#if user.logged_in}}
  <p>用户已登录</p>
{{/if}}

{{#var title="标题" /}}
{{#each products}}
  <div>{{this.name}}</div>
{{/each}}

{{#component "button" /}}
{{#unless disabled}}
  <button>启用的按钮</button>
{{/unless}}

{{!-- 4. 测试语法错误（应该显示红色错误） --}}
{{#layout "theme"}}
{{#var title="测试"}}
{{#component "button"}}
{{#content "header"}}

{{!-- 5. 测试混合嵌套 --}}
{{#layout "main" /}}
{{#if user.admin}}
  {{#var greeting="欢迎管理员" /}}
  {{#component "admin-panel" /}}
  {{#each user.permissions}}
    <div>权限：{{this.name}}</div>
  {{/each}}
{{/if}}
{{#unless user.banned}}
  <p>欢迎访问</p>
{{/unless}} 