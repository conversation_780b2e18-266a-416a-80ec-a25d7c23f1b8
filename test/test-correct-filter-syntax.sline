{{!-- Sline 正确过滤器语法示例 --}}
{{!-- 基于 theme-sline 项目和 LSP 服务器的正确语法 --}}

{{!-- ❌ 错误的语法 - 缺少括号 --}}
{{!-- {{product.price | money}} --}}
{{!-- {{image | asset_url}} --}}
{{!-- {{"hello" | t}} --}}

{{!-- ✅ 正确的语法 - 函数调用方式 --}}

{{!-- 1. 货币和数字格式化 --}}
{{product.price | money()}}
{{product.price | money_with_currency()}}
{{product.price | money_without_currency()}}
{{10 | plus(5)}}
{{10 | minus(3)}}
{{10 | times(2)}}
{{10 | divided_by(2)}}
{{10 | modulo(3)}}
{{3.7 | ceil()}}
{{3.2 | floor()}}
{{number | to_float()}}

{{!-- 2. 字符串处理 --}}
{{"hello world" | capitalize()}}
{{"HELLO" | downcase()}}
{{"hello" | upcase()}}
{{text | strip_html()}}
{{text | newline_to_br()}}
{{text | replace("old", "new")}}
{{text | remove_first("target")}}
{{text | split(",")}}
{{text | trim()}}
{{long_text | truncate(50)}}
{{text | append("suffix")}}
{{text | prepend("prefix")}}

{{!-- 3. URL 和资源处理 --}}
{{"logo.png" | asset_url()}}
{{product.image | image_url()}}
{{product.video_id | external_video_url()}}
{{text | url_encode()}}
{{encoded_text | url_decode()}}

{{!-- 4. 数组操作 --}}
{{products | first()}}
{{products | last()}}
{{products | size()}}
{{tags | join(",")}}
{{products | sort("title")}}
{{products | reverse()}}
{{products | slice(0, 3)}}
{{products | map("title")}}
{{products | where("available", true)}}

{{!-- 5. 日期处理 --}}
{{order.created_at | date(format="%Y-%m-%d")}}
{{order.created_at | date(format="%B %d, %Y")}}

{{!-- 6. 通用功能 --}}
{{description | default("暂无描述")}}
{{"general.title" | t()}}
{{data | json()}}
{{tags | contains("sale")}}

{{!-- 7. SLine 特有功能 --}}
{{settings | class_list()}}
{{"primary-color" | css_var()}}
{{font | font_face()}}
{{font | font_modify("weight", "bold")}}
{{product | get_variants()}}
{{blog.articles | get_pagination()}}
{{collection.products | get_product_pagination()}}
{{customer.orders | get_order_pagination()}}
{{article.comments | get_comment_pagination()}}
{{search.results | get_search_pagination()}}

{{!-- 8. 复杂的过滤器链 --}}
{{product.title | truncate(20) | upcase()}}
{{product.price | money() | default("价格未设定")}}
{{"logo.png" | asset_url() | default("/images/default-logo.png")}}

{{!-- 9. 条件语句中的过滤器 --}}
{{#if product.price | money()}}
  <span class="price">{{product.price | money()}}</span>
{{/if}}

{{#each products | where("available", true)}}
  <div class="product">
    <h3>{{this.title | capitalize()}}</h3>
    <p>{{this.price | money()}}</p>
    <img src="{{this.image | image_url()}}" alt="{{this.title}}">
  </div>
{{/each}}

{{!-- 10. 变量定义中的过滤器 --}}
{{#var formatted_title = product.title | upcase() /}}
{{#var product_count = products | size() /}}
{{#var display_price = product.price | money() | default("免费") /}}

{{!-- 使用定义的变量 --}}
<h1>{{formatted_title}}</h1>
<p>共有 {{product_count}} 个产品</p>
<span class="price">{{display_price}}</span> 