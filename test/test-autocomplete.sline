<!DOCTYPE html>
<html>
<head>
    <title>测试自动完成</title>
</head>
<body>
    <!-- 测试管道符自动完成 -->
    {{ product.title | }}
    
    <!-- 测试标签自动完成 -->
    {{#}}
    
    <!-- 测试控制流标签 -->
    {{#if}}
    {{#for}}
    {{#case}}
    {{#capture}}
    
    <!-- 测试布局组件标签 -->
    {{#layout}}
    {{#component}}
    {{#section}}
    {{#content}}
    
    <!-- 测试表单标签 -->
    {{#customer_form}}
    {{#cart_form}}
    {{#contact_form}}
    
    <!-- 测试媒体资源标签 -->
    {{#image_tag}}
    {{#video_tag}}
    {{#stylesheet}}
    
    <!-- 测试功能标签 -->
    {{#link_to}}
    {{#payment_button}}
    {{#format_address}}
    
    <!-- 测试过滤器链 -->
    {{ product.price | money | }}
    
    <!-- 测试块助手 -->
    {{#if product.available}}
        {{ product.title | upcase() | }}
    {{/if}}
    
    <!-- 测试常用过滤器 -->
    {{ product.description | truncate(100) | }}
    {{ products | map("title") | join(", ") | }}
    {{ created_at | date("%Y-%m-%d") | }}
    
    <!-- 测试标签搜索 -->
    {{#cus}}  <!-- 应该提示客户相关标签 -->
    {{#image}} <!-- 应该提示 image_tag -->
    {{#link}}  <!-- 应该提示 link_to 相关标签 -->
</body>
</html> 