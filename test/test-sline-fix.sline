{{!-- SLine 测试文件 - 验证修复是否有效 --}}

{{!-- 测试基本标签 - 这些应该不再显示 "Unknown tag" --}}
{{#component "stylesheet" src="./tips-card.css" | asset_url() /}}

{{#var class_name = "" /}}

{{#if props.type == "error"}}
  {{#set class_name = "tips-card-fail" /}}
{{/if}}

<div class="tips-card body6 flex-align-center {{class_name}} {{props.class}}">
  {{#if props.type == "success"}}
    {{#component "icons/success" /}}
  {{/if}}
  
  {{#if props.type == "error"}}
    {{#component "icons/warning" /}}
  {{/if}}
  
  {{props.text}}
</div>

{{!-- 测试过滤器 - 这些应该在 | 后面提供自动完成 --}}
{{ product.title | upcase }}
{{ product.price | money }}
{{ product.image | image_url(width: 300) }}

{{!-- 测试对象属性 - 这些应该提供属性建议 --}}
{{ product.title }}
{{ customer.first_name }}
{{ cart.total_price }}

{{!-- 测试嵌套结构 --}}
{{#for product in products}}
  <div class="product-item">
    <h3>{{ product.title }}</h3>
    <p>{{ product.price | money }}</p>
  </div>
{{/for}}

{{!-- 测试条件语句 --}}
{{#if customer.logged_in}}
  <p>Welcome back, {{ customer.first_name }}!</p>
{{#else}}
  <p>Please log in to continue.</p>
{{/if}}
