{{!-- 测试自闭合标签和非自闭合标签的混合使用 --}}

{{!-- 非自闭合标签示例 --}}
{{#if user.logged_in}}
  <p>欢迎回来，{{user.name}}!</p>
{{/if}}

{{#each products}}
  <div>{{this.title}} - {{this.price}}</div>
{{/each}}

{{!-- 这些需要闭合标签的应该继续检测 --}}
{{#if user.logged_in}}
  <p>欢迎!</p>
{{/if}}

{{#each products}}
  <div>{{this.title}}</div>
{{/each}}

{{!-- 自闭合标签不应该影响后续标签 --}}
{{#component props /}}

{{!-- 插入自闭合标签后，后面的非自闭合标签应该正常高亮 --}}
{{#if user.admin}}
  <p>管理员功能</p>
{{/if}}

{{#layout "theme" /}}

{{#unless user.banned}}
  <p>正常用户</p>
{{/unless}}

{{#var title = "测试页面" /}}

{{#each categories}}
  <h2>{{this.name}}</h2>
  {{#component "product-list" products=this.products /}}
{{/each}}

{{#content "header" /}}

{{#capture sidebar}}
  <div>侧边栏内容</div>
{{/capture}}

{{!-- 关键测试：非自闭合标签内嵌套自闭合标签 --}}
{{#each categories}}
  <h2>{{this.name}}</h2>
  {{#content "header" /}}
  <p>这里插入了自闭合标签</p>
{{/each}}

{{!-- 测试复杂嵌套：多层嵌套中插入自闭合标签 --}}
{{#if settings.show_products}}
  {{#each featured_products}}
    <div class="product">
      <h3>{{this.title}}</h3>
      {{#component "price-tag" price=this.price /}}
      {{#if this.on_sale}}
        <span class="sale">特价</span>
        {{#var discount_rate = this.discount /}}
      {{/if}}
    </div>
  {{/each}}
{{/if}}

{{!-- 验证修复：开始和结束标签应该颜色一致 --}}
{{#unless user.banned}}
  <p>正常用户</p>
  {{#layout "special" /}}
  <p>自闭合标签不应影响外层标签</p>
{{/unless}} 