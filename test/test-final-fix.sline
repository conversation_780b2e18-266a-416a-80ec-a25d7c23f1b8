{{!-- 最终修复验证：开始和结束标签应该完全一致 --}}

{{!-- 基础测试：这些标签对应该颜色完全一致 --}}
{{#if user.logged_in}}
  <p>欢迎回来！</p>
{{/if}}

{{#each products}}
  <div>{{this.title}}</div>
{{/each}}

{{#unless user.banned}}
  <span>正常用户</span>
{{/unless}}

{{!-- 自闭合标签：应该有独立的颜色 --}}
{{#layout "theme" /}}
{{#var title = "测试页面" /}}
{{#component "button" text="点击" /}}

{{!-- 嵌套测试：外层标签不受内部自闭合标签影响 --}}
{{#if settings.enabled}}
  {{#var message = "启用" /}}
  <p>功能已启用</p>
{{/if}}

{{#each categories}}
  {{#component "header" title=this.name /}}
  <div>{{this.description}}</div>
{{/each}}

{{!-- 复杂嵌套：多层标签应该都保持一致 --}}
{{#if user.authenticated}}
  {{#each user.orders}}
    {{#unless this.cancelled}}
      <div class="order">
        {{#component "order-status" status=this.status /}}
        <h3>订单 #{{this.number}}</h3>
      </div>
    {{/unless}}
  {{/each}}
{{/if}} 