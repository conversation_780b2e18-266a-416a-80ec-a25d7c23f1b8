{{!-- 测试基本语言服务器功能 --}}

{{!-- 测试自动完成 --}}
{{#layout "theme" /}}
{{#var title = "测试页面" /}}
{{#component "header" class="main-header" /}}

{{!-- 测试过滤器自动完成 --}}
{{ user.name | uppercase | truncate(50) }}
{{ product.price | money }}

{{!-- 测试代码着色 --}}
{{#if user.logged_in}}
  <p>欢迎回来，{{ user.name }}！</p>
{{else}}
  <p>请登录</p>
{{/if}}

{{#each products}}
  <div class="product">
    <h3>{{ this.title }}</h3>
    <p>{{ this.description | truncate(100) }}</p>
    <span>{{ this.price | money }}</span>
  </div>
{{/each}} 