{{! 多行标签语法测试文件 }}

{{! 正确的多行标签 - 应该没有错误 }}
{{#component
  "page-head"
  page_title="products.giftcard.seoTitle" | t(value=gift_card_balance, shop=shop.name)
  page_description="products.giftcard.title" | t()
/}}

{{! 正确的多行标签 - 非自闭合 }}
{{#section
  class="main-content"
  id="product-section"
}}
  <h1>Product Title</h1>
  <p>Product Description</p>
{{/section}}

{{! 正确的单行标签 }}
{{#var name="test" value="123" /}}

{{! 错误：真正未关闭的表达式 }}
{{#incomplete

{{! 正确的多属性多行标签 }}
{{#layout
  "theme"
  class="responsive"
  meta_title="Shop Products"
  meta_description="Browse our collection"
/}}

{{! 错误：多行标签缺少结束 }}
{{#component
  "product-card"
  product=current_product
  show_price=true

{{! 正确的嵌套多行标签 }}
{{#if user.logged_in}}
  {{#form
    action="/customer/login"
    method="post"
    class="login-form"
  }}
    <input type="email" name="email">
    <button type="submit">Login</button>
  {{/form}}
{{/if}} 