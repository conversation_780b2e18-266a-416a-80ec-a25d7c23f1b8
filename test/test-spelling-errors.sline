{{!-- 拼写错误检测测试文件 --}}

{{!-- 标签拼写错误测试 --}}
{{#compont "script" src="base/index.js" /}}
{{#conponent "header" /}}
{{#layot "theme" /}}
{{#var titel = "测试页面" /}}
{{#captur variable_name}}内容{{/captur}}

{{!-- 过滤器拼写错误测试 - 带括号 --}}
{{ product.image | asset_ur() }}
{{ user.name | capitaliz() }}
{{ product.price | mony() }}
{{ content | truncat(100) }}

{{!-- 过滤器拼写错误测试 - 无括号 --}}
{{ user.email | dowcase }}
{{ product.title | upcase }}
{{ user.name | capialize }}
{{ product.tags | jon }}

{{!-- 正确的语法作为对比 --}}
{{#component "script" src="base/index.js" /}}
{{#layout "theme" /}}
{{#var title = "测试页面" /}}
{{#capture variable_name}}内容{{/capture}}

{{ product.image | asset_url() }}
{{ user.name | capitalize() }}
{{ product.price | money() }}
{{ content | truncate(100) }}

{{ user.email | downcase }}
{{ product.title | upcase }}
{{ product.tags | join }} 