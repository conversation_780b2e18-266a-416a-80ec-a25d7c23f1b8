{{#layout "theme" /}}

{{!-- SLine 模板引擎示例文件 --}}
{{!-- 展示 Handlebars 风格的语法特性 --}}

{{#var page_title = "SLine 模板引擎例" /}}

<!DOCTYPE html>
<!-- html 注释 -->
<html lang="{{request.locale.iso_code}}">
<head>
    <title>{{page_title}}</title>
    <meta charset="UTF-8">
</head>
<body>
    {{!-- 这应该显示为绿色注释 --}}
    <!-- 这是标准的HTML注释 -->
    
    <h1>{{page_title}}</h1>
    
    {{#if user.is_logged_in}}
        <p>欢迎，{{user.name}}！</p>
    {{#else}}
        <p>请登录</p>
    {{/if}}
    
    {{#each products}}
        <div class="product">
            <h3>{{this.name}}</h3>
            <p>价格：{{this.price | money}}</p>
        </div>
    {{/each}}
</body>
</html> 