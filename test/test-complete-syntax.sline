{{!-- Sline 完整语法测试文件 --}}
{{!-- 测试所有标签和过滤器的语法高亮 --}}

{{!-- ❌ 错误语法：自闭合标签缺少斜杠 --}}
{{#layout "main"}}
{{#var title = "测试标题"}}
{{#component "button" text="点击"}}
{{#image_tag "logo.png" alt="Logo"}}
{{#stylesheet "main.css"}}
{{#script "app.js"}}

{{!-- ✅ 正确语法：自闭合标签 --}}
{{#layout "main" /}}
{{#var title = "测试标题" /}}
{{#set page_class = "home" /}}
{{#component "button" text="点击" /}}
{{#content "header" /}}
{{#section "main" /}}
{{#sections "sidebar" /}}
{{#schema /}}
{{#capture "meta_description" /}}

{{!-- 媒体和资源标签 --}}
{{#image_tag "product.jpg" alt="产品图片" /}}
{{#video_tag "demo.mp4" /}}
{{#external_video_tag "youtube_id" /}}
{{#time_tag "2024-01-01" /}}
{{#metafield_tag "product.custom_field" /}}
{{#preload_tag "font.woff2" /}}
{{#stylesheet "theme.css" /}}
{{#script "analytics.js" /}}
{{#style /}}

{{!-- 链接标签 --}}
{{#link_to "首页" "/" /}}
{{#link_to_customer_login "登录" /}}
{{#link_to_customer_logout "退出" /}}
{{#link_to_customer_register "注册" /}}

{{!-- 支付和功能标签 --}}
{{#payment_button /}}
{{#payment_type_svg "visa" /}}
{{#placeholder_svg "product" /}}
{{#format_address address /}}
{{#highlight search_terms /}}

{{!-- 表单标签 --}}
{{#customer_form /}}
{{#customer_login_form /}}
{{#customer_address_form /}}
{{#create_customer_form /}}
{{#update_customer_form /}}
{{#delete_customer_form /}}
{{#cart_form /}}
{{#contact_form /}}
{{#new_comment_form /}}
{{#order_tracking_form /}}
{{#localization_form /}}

{{!-- 密码和账户表单 --}}
{{#activate_customer_password_form /}}
{{#reset_customer_password_form /}}
{{#bind_customer_email_form /}}
{{#bind_customer_phone_form /}}
{{#customer_subscribe_form /}}
{{#customer_unsubscribe_form /}}
{{#company_account_application_form /}}
{{#storefront_password_form /}}

{{!-- ✅ 正确语法：非自闭合标签 --}}
{{#if user.logged_in}}
  <p>欢迎回来，{{user.name}}！</p>
{{/if}}

{{#unless cart.empty}}
  <p>购物车中有 {{cart.item_count}} 件商品</p>
{{/unless}}

{{#each products}}
  <div class="product">
    <h3>{{this.title}}</h3>
    <p>{{this.price | money}}</p>
  </div>
{{/each}}

{{#for i in (1..5)}}
  <span>第 {{i}} 项</span>
{{/for}}

{{#with product}}
  <h1>{{title}}</h1>
  <p>{{description}}</p>
{{/with}}

{{#case product.type}}
  {{#when "book"}}
    <p>这是一本书</p>
  {{#when "music"}}
    <p>这是音乐</p>
  {{#else}}
    <p>其他类型</p>
{{/case}}

{{#block "content"}}
  <p>默认内容</p>
{{/block}}

{{#blocks "sidebar"}}
  <div>侧边栏内容</div>
{{/blocks}}

{{!-- 测试所有过滤器 --}}
<div>
  {{!-- 数学运算过滤器 --}}
  <p>绝对值: {{-5 | abs}}</p>
  <p>加法: {{10 | plus: 5}}</p>
  <p>减法: {{10 | minus: 3}}</p>
  <p>乘法: {{10 | times: 2}}</p>
  <p>除法: {{10 | divided_by: 2}}</p>
  <p>取余: {{10 | modulo: 3}}</p>
  <p>四舍五入: {{3.7 | round}}</p>
  <p>向上取整: {{3.2 | ceil}}</p>
  <p>向下取整: {{3.8 | floor}}</p>
  <p>最小值: {{5 | at_least: 10}}</p>
  <p>最大值: {{15 | at_most: 10}}</p>

  {{!-- 字符串处理过滤器 --}}
  <p>大写: {{"hello" | upcase}}</p>
  <p>小写: {{"WORLD" | downcase}}</p>
  <p>首字母大写: {{"hello world" | capitalize}}</p>
  <p>驼峰命名: {{"hello world" | camelize}}</p>
  <p>句柄化: {{"Hello World!" | handleize}}</p>
  <p>追加: {{"hello" | append: " world"}}</p>
  <p>前置: {{"world" | prepend: "hello "}}</p>
  <p>替换: {{"hello world" | replace: "world", "universe"}}</p>
  <p>替换首个: {{"hello world world" | replace_first: "world", "universe"}}</p>
  <p>移除: {{"hello world" | remove: " world"}}</p>
  <p>移除首个: {{"hello world world" | remove_first: " world"}}</p>
  <p>截断: {{"这是一个很长的文本" | truncate: 10}}</p>
  <p>截断单词: {{"This is a long text" | truncate_words: 3}}</p>
  <p>去除HTML: {{"<p>Hello</p>" | strip_html}}</p>
  <p>去除换行: {{"line1\nline2" | strip_newlines}}</p>
  <p>换行转BR: {{"line1\nline2" | newline_to_br}}</p>
  <p>修剪: {{"  hello  " | trim}}</p>
  <p>左修剪: {{"  hello  " | trim_left}}</p>
  <p>右修剪: {{"  hello  " | trim_right}}</p>
  <p>转义: {{"<script>" | escape}}</p>

  {{!-- 数组操作过滤器 --}}
  <p>大小: {{products | size}}</p>
  <p>首个: {{products | first}}</p>
  <p>最后: {{products | last}}</p>
  <p>连接: {{tags | join: ", "}}</p>
  <p>分割: {{"apple,banana,orange" | split: ","}}</p>
  <p>排序: {{products | sort: "title"}}</p>
  <p>反转: {{products | reverse}}</p>
  <p>唯一: {{tags | uniq}}</p>
  <p>切片: {{products | slice: 0, 3}}</p>
  <p>映射: {{products | map: "title"}}</p>
  <p>筛选: {{products | where: "available", true}}</p>

  {{!-- URL和资源过滤器 --}}
  <p>资源URL: {{"logo.png" | asset_url}}</p>
  <p>图片URL: {{product.featured_image | image_url}}</p>
  <p>文件URL: {{"document.pdf" | file_url}}</p>
  <p>文件图片URL: {{"image.jpg" | file_img_url}}</p>
  <p>字体URL: {{"font.woff2" | font_url}}</p>
  <p>外部视频URL: {{product.video_id | external_video_url}}</p>
  <p>支付图片URL: {{"visa" | payment_type_img_url}}</p>
  <p>URL编码: {{"hello world" | url_encode}}</p>
  <p>URL解码: {{"hello%20world" | url_decode}}</p>
  <p>URL转义: {{"hello&world" | url_escape}}</p>
  <p>URL参数转义: {{"hello=world&foo=bar" | url_param_escape}}</p>

  {{!-- 格式化过滤器 --}}
  <p>金额: {{product.price | money}}</p>
  <p>带货币金额: {{product.price | money_with_currency}}</p>
  <p>无货币金额: {{product.price | money_without_currency}}</p>
  <p>日期: {{order.created_at | date: "%Y-%m-%d"}}</p>
  <p>翻译: {{"hello" | t}}</p>
  <p>默认值: {{product.description | default: "暂无描述"}}</p>
  <p>复数: {{cart.item_count | pluralize: "item", "items"}}</p>
  <p>JSON: {{product | json}}</p>

  {{!-- 检查过滤器 --}}
  <p>包含: {{product.tags | contains: "sale"}}</p>
  <p>开始于: {{product.title | starts_with: "New"}}</p>
  <p>结束于: {{product.title | ends_with: "Sale"}}</p>

  {{!-- 特殊过滤器 --}}
  <p>类列表: {{section.settings | class_list}}</p>
  <p>CSS变量: {{"primary-color" | css_var}}</p>
  <p>字体修改: {{font | font_modify: "weight", "bold"}}</p>
  <p>字体面: {{font | font_face}}</p>
  <p>元字段文本: {{product.metafields.custom.field | metafield_text}}</p>
  <p>连接: {{array1 | concat: array2}}</p>

  {{!-- 获取类过滤器 --}}
  <p>获取产品: {{"product-handle" | get_product}}</p>
  <p>获取集合: {{collections | get_collections}}</p>
  <p>获取变体: {{product | get_variants}}</p>
  <p>获取元字段: {{product | get_metafields}}</p>
  <p>获取分页: {{blog.articles | get_pagination}}</p>
  <p>获取文章分页: {{blog.articles | get_article_pagination}}</p>
  <p>获取产品分页: {{collection.products | get_product_pagination}}</p>
  <p>获取订单分页: {{customer.orders | get_order_pagination}}</p>
  <p>获取评论分页: {{article.comments | get_comment_pagination}}</p>
  <p>获取搜索分页: {{search.results | get_search_pagination}}</p>
  <p>获取: {{object | get: "property"}}</p>
</div>

{{!-- Schema 块 --}}
{{#schema}}
{
  "name": "测试区块",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "标题"
    }
  ]
}
{{/schema}} 