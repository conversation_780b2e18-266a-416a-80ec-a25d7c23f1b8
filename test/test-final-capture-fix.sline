{{!-- 最终测试：capture 标签修复验证 --}}

{{!-- ✅ 这些 capture 用法应该完全正常，没有任何红色错误提示 --}}

{{#capture total_number}}
  {{#if cart && cart.item_count > 0}}
    {{#if cart.item_count > 99}}99+{{#else /}}{{cart.item_count}}{{/if}}
  {{/if}}
{{/capture}}

{{#capture subtitle}}
  动态生成的副标题内容
{{/capture}}

{{#capture header_content}}
  <h1>{{site.title}}</h1>
  <p>{{site.description}}</p>
  {{#if user.logged_in}}
    <span>欢迎，{{user.name}}！</span>
  {{/if}}
{{/capture}}

{{#capture product_list}}
  {{#each products}}
    <div class="product">
      <h3>{{this.title}}</h3>
      <p>{{this.price | money()}}</p>
    </div>
  {{/each}}
{{/capture}}

{{!-- 使用capture的内容 --}}
<span class="cart-count">{{total_number}}</span>
<div class="subtitle">{{subtitle}}</div>
<header>{{header_content}}</header>
<div class="products">{{product_list}}</div>

{{!-- ❌ 这些仍然应该显示错误（用于对比测试） --}}
{{#layout "theme"}}  {{!-- 错误：应该是 {{#layout "theme" /}} --}}
{{#var title = "test"}}  {{!-- 错误：应该是 {{#var title = "test" /}} --}}
{{#component "button"}}  {{!-- 错误：应该是 {{#component "button" /}} --}} 