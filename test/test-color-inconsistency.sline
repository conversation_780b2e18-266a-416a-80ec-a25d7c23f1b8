{{!-- 测试着色不一致问题 --}}
{{!-- 重现截图中的问题：错误和嵌套影响后续语法符号着色 --}}

{{!-- 第一个错误情况 --}}
{{#if user.is_logged_in}}
  <p>欢迎，{{user.name}}！</p>
{{#else}}
  <p>请登录</p>
{{/if}}

{{!-- 这之后的 {{ 符号着色应该保持一致 --}}
{{#each products}}
  <div class="product">
    <h3>{{this.name}}</h3>
    <p>价格: {{this.price | money()}}</p>
  </div>
{{/each}}

{{!-- 测试嵌套情况 --}}
{{#if user.logged_in}}
  {{#each user.orders}}
    <div class="order">
      {{#if this.status == "pending"}}
        <span class="pending">待处理</span>
      {{/if}}
      <p>订单: {{this.id}}</p>
    </div>
  {{/each}}
{{/if}}

{{!-- 这些标签的 {{ 着色应该都一致 --}}
{{user.profile.avatar}}
{{settings.store_name}}
{{#component "footer" /}}

{{!-- 测试错误后的恢复 --}}
{{#layout "theme"}}  {{!-- 错误：缺少 / --}}
{{#var title = "测试"}}  {{!-- 错误：缺少 / --}}

{{!-- 这些应该正常着色 --}}
{{#layout "theme" /}}
{{#var title = "测试" /}}
{{product.title}}
{{#component "button" text="点击" /}} 