// 这是一个 SLine 语言的示例文件
// 用于测试语法高亮功能

/* 
 * 多行注释示例
 * 这里可以写详细的说明
 */

// 变量声明
var name = "SLine Language";
let version = 1.0;
const isActive = true;

// 函数定义
function calculateSum(a, b) {
    if (a > 0 && b > 0) {
        return a + b;
    } else {
        return 0;
    }
}

// 控制流示例
for (let i = 0; i < 10; i++) {
    if (i % 2 == 0) {
        console.log("偶数: " + i);
    } else {
        console.log('奇数: ' + i);
    }
}

// 条件语句
if (version >= 1.0) {
    console.log("版本检查通过");
} else {
    console.log("版本过低");
}

// 循环语句
while (isActive) {
    // 执行某些操作
    break;
}

// 数组和对象（如果支持的话）
var numbers = [1, 2, 3, 4, 5];
var config = {
    debug: false,
    timeout: 5000
};

// 函数调用
var result = calculateSum(10, 20);
console.log("计算结果: " + result);
