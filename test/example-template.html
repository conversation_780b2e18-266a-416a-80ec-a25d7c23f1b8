{{#layout "theme" /}}

{{!-- SLine 模板引擎示例文件 --}}
{{!-- 展示 Handlebars 风格的语法特性 --}}

{{#var page_title = "SLine 模板引擎示例" /}}
{{#var user_name = "张三" /}}

<!DOCTYPE html>
<html lang="{{request.locale.iso_code}}">
<head>
    {{#component "page-head" /}}
    <title>{{page_title}}</title>
</head>
<body>
    {{#component "header" /}}

    <main class="page-content">
        <h1>欢迎使用 SLine 模板引擎</h1>

        {{!-- 变量输出示例 --}}
        <section class="user-info">
            <p>用户名: {{user_name}}</p>
            <p>当前时间: {{request.current_time | date('Y-m-d H:i:s')}}</p>
            <p>商店名称: {{shop.name}}</p>
        </section>

        {{!-- 条件语句示例 --}}
        {{#if customer}}
            <div class="logged-in-user">
                <h2>用户信息</h2>
                <p>邮箱: {{customer.email}}</p>
                <p>姓名: {{customer.first_name}} {{customer.last_name}}</p>
                {{#if customer.default_address}}
                    <p>地址: {{customer.default_address.address1}}</p>
                {{/if}}
            </div>
        {{#else}}
            <div class="login-prompt">
                <p>请先登录</p>
                <a href="{{routes.account_login_url}}">登录</a>
            </div>
        {{/if}}

        {{!-- 循环语句示例 --}}
        {{#if collections.featured.products}}
            <section class="product-list">
                <h2>特色产品</h2>
                {{#each collections.featured.products}}
                    <div class="product-item">
                        <h3>{{title}}</h3>
                        <p>价格: {{price | money}}</p>
                        <p>描述: {{description | truncate(100)}}</p>

                        {{#if available}}
                            <span class="in-stock">有库存</span>
                            {{#component "button"
                                type="link"
                                text="立即购买"
                                link_url=url
                            /}}
                        {{#else}}
                            <span class="out-of-stock">缺货</span>
                        {{/if}}

                        {{!-- 产品图片 --}}
                        {{#if featured_image}}
                            <img src="{{featured_image | asset_url}}" alt="{{title}}" />
                        {{/if}}
                    </div>
                {{/each}}
            </section>
        {{/if}}

        {{!-- 组件调用示例 --}}
        {{#component "newsletter-signup"
            title="订阅我们的新闻"
            description="获取最新产品信息和优惠"
        /}}

        {{!-- 内容区域示例 --}}
        {{#content "main-content" /}}

        {{!-- 三重大括号不转义输出 --}}
        <div class="custom-html">
            {{{settings.custom_html_content}}}
        </div>

        {{!-- 过滤器示例 --}}
        <section class="filters-demo">
            <p>格式化日期: {{article.published_at | date('Y年m月d日')}}</p>
            <p>大写转换: {{shop.name | upper}}</p>
            <p>货币格式: {{product.price | money_with_currency}}</p>
            <p>默认值: {{product.vendor | default('未知品牌')}}</p>
            <p>翻译: {{"general.search.title" | t}}</p>
        </section>
    </main>

    {{#component "footer" /}}

    {{!-- Schema 配置示例 --}}
    {{#schema}}
    {
      "name": "示例页面",
      "settings": [
        {
          "type": "text",
          "id": "page_title",
          "label": "页面标题",
          "default": "SLine 模板示例"
        },
        {
          "type": "textarea",
          "id": "custom_html_content",
          "label": "自定义 HTML 内容"
        }
      ]
    }
    {{/schema}}
</body>
</html>
