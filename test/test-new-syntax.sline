{{!-- 测试新的语法高亮和错误检测 --}}

{{!-- ❌ 错误语法：应该显示为红色（markup.deleted） --}}
{{#layout "theme"}}
{{#var title = "测试"}}
{{#component "button"}}
{{#else}}

{{!-- ✅ 正确语法：应该有颜色高亮 --}}
{{#layout "theme" /}}
{{#var title = "测试" /}}
{{#component "button" /}}
{{#else /}}

{{!-- 测试标签名高亮 --}}
{{#if user.logged_in}}
  <p>欢迎回来，{{user.name}}！</p>
{{/if}}

{{#each products}}
  <div>{{this.title}}</div>
{{/each}}

{{!-- 测试过滤器高亮 - 正确的函数调用语法 --}}
{{product.price | money()}}
{{user.name | upcase()}}
{{content | truncate(50)}}
{{image | image_url("medium")}}
{{date | date(format="%Y-%m-%d")}}
{{"translation.key" | t()}}
{{description | default("暂无描述")}}

{{!-- 测试变量高亮 --}}
{{user.name}}
{{product.title}}
{{settings.store_name}} 