{{!-- 🔍 括号颜色一致性测试文件 --}}
{{!-- 
🎯 关键修复：现在所有的 {{ 和 }} 括号都使用相同的 scope！

请按以下步骤验证修复效果：

1. 重新加载 VS Code 窗口：
   - 按 Ctrl+Shift+P (Windows/Linux) 或 Cmd+Shift+P (Mac)
   - 输入 "Developer: Reload Window" 并执行

2. 检查颜色一致性：
   - 观察下面所有的 {{ 和 }} 括号应该有相同的颜色
   - 如果还有不同颜色，尝试切换到 "Dark+ (default dark)" 主题

3. 使用 Scope Inspector 验证（可选）：
   - 按 Ctrl+Shift+P (Windows/Linux) 或 Cmd+Shift+P (Mac)  
   - 输入 "Developer: Inspect Editor Tokens and Scopes"
   - 点击任何 {{ 或 }} 括号
   - 应该看到 scope 为 "punctuation.definition.tag.sline"
--}}

{{!-- 测试案例 1: 基本块标签 --}}
{{#if condition}}
    <p>测试内容</p>
{{/if}}

{{!-- 测试案例 2: 循环标签 --}}
{{#each items}}
    <div>{{this}}</div>
{{/each}}

{{!-- 测试案例 3: 组件标签 --}}
{{#component "button"}}
    内容
{{/component}}

{{!-- 测试案例 4: 变量输出 --}}
<p>{{variable}}</p>
<p>{{object.property}}</p>

{{!-- 测试案例 5: 三重括号 --}}
{{{raw_html}}}

{{!-- 测试案例 6: 自闭合标签 --}}
{{#var name = "test" /}}
{{#content "header" /}}

{{!-- 测试案例 7: Schema块 --}}
{{#schema}}
{
  "name": "test"
}
{{/schema}}

{{!-- 
预期结果：
- 所有的 {{ 应该有相同的颜色 (punctuation.definition.tag.begin.sline)
- 所有的 }} 应该有相同的颜色 (punctuation.definition.tag.end.sline)  
- 在大多数主题中，begin 和 end 应该是相同的颜色
--}} 