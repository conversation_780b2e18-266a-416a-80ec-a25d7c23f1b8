# SLine Language Support

[![Visual Studio Marketplace Version](https://img.shields.io/visual-studio-marketplace/v/your-publisher-name.sline-highlight)](https://marketplace.visualstudio.com/items?itemName=your-publisher-name.sline-highlight)
[![Visual Studio Marketplace Downloads](https://img.shields.io/visual-studio-marketplace/d/your-publisher-name.sline-highlight)](https://marketplace.visualstudio.com/items?itemName=your-publisher-name.sline-highlight)

为 VS Code 提供 SLine 模板引擎的完整语言支持，包括语法高亮、**智能语言服务器**、上下文感知代码完成、悬停文档和代码片段。

> 🚀 **重大更新**: 全新的智能语言服务器！支持 20+ 标签、80+ 过滤器、15+ 对象的完整参考数据，提供上下文感知的代码完成、悬停文档、参数提示和智能错误检测。

## ✨ 特性

### 🎨 语法高亮
- **完整的 SLine 语法高亮**: 支持所有 Handlebars 风格的 SLine 语法
- **注释高亮**: SLine 注释 `{{!-- --}}` 与 HTML 注释保持一致的绿色显示
- **统一的标点符号颜色**: 解决了 `{{#` 标点符号颜色不一致的问题

### 🧠 智能语言服务器
- **📚 完整参考数据库**:
  - 20+ SLine 标签（for、if、component、layout 等）
  - 80+ 过滤器函数（money、date、image_url 等）
  - 15+ 核心对象（product、cart、customer、shop 等）
- **🎯 上下文感知完成**:
  - 标签完成：在 `{{#` 后智能提示可用标签
  - 过滤器完成：在 `|` 后提示相关过滤器
  - 对象属性完成：在 `object.` 后提示可用属性
  - 参数完成：为标签和过滤器提供参数提示
- **📖 悬停文档**:
  - 即时显示语法文档和示例
  - 参数类型和描述信息
  - 官方文档链接
  - 弃用警告
- **📋 参数签名帮助**:
  - 实时参数提示和类型信息
  - 活动参数高亮
  - 智能参数位置识别
- **🔍 智能错误诊断**:
  - 未知标签和过滤器检测
  - 拼写错误智能建议
  - 对象属性验证
  - 弃用语法警告
- **🎯 智能语言模式切换**:
  - 自动检测 HTML 文件中的 SLine 语法
  - 智能建议切换到 SLine 语言模式
  - 可配置自动切换或手动确认
  - 支持 `.sline` 和 `.html` 文件扩展名

### 📝 代码片段系统
- **15+ 预定义片段**: 涵盖常用开发模式
  - 控制流：for 循环、if 条件、unless 等
  - 表单：登录表单、注册表单、购物车表单
  - 布局：基础布局模板
  - 数据展示：产品卡片、文章预览
  - 实用工具：分页导航、面包屑导航
- **智能插入**: 支持 Tab 键跳转和参数占位符
- **分类管理**: 按功能分类便于查找

### 🔧 高级功能
- **实时语法检查**: 提供即时的错误诊断和修复建议
- **性能优化**: 毫秒级响应速度，支持大文件编辑
- **多语言支持**: 中英文文档和界面
- **向后兼容**: 与现有功能完全兼容
- **Web 支持**: 在 vscode.dev 和 github.dev 中运行
- **文件关联**: 自动识别 `.sline` 文件

## 🚀 安装

### 从 VS Code Marketplace

1. 打开 VS Code
2. 按 `Ctrl+Shift+X` (Windows/Linux) 或 `Cmd+Shift+X` (Mac) 打开扩展面板
3. 搜索 "SLine Language Support"
4. 点击安装

### 从 VSIX 包

```bash
code --install-extension sline-highlight-0.1.3.vsix
```

## 🚀 快速开始

安装扩展后，创建一个 `.sline` 文件并体验强大的智能语言服务器功能：

### 1. 🎯 上下文感知代码完成

```sline
<!-- 标签完成：输入 {{# 后智能提示 -->
{{#fo
<!-- 会提示 'for' 标签，包含完整语法和示例 -->

<!-- 对象属性完成：输入 object. 后提示属性 -->
{{ product.ti
<!-- 会提示 'title', 'type' 等可用属性 -->

<!-- 过滤器完成：输入 | 后提示过滤器 -->
{{ product.price | mon
<!-- 会提示 'money', 'money_with_currency' 等过滤器 -->
```

### 2. 📖 悬停文档

将鼠标悬停在任何 SLine 标签、过滤器或对象上，即可查看：
- 完整的语法文档和示例
- 参数类型和描述
- 官方文档链接
- 使用建议

### 3. 📋 参数签名帮助

```sline
<!-- 输入标签参数时自动显示参数提示 -->
{{#for item in products limit:
<!-- 会显示 for 标签的所有可用参数 -->

<!-- 输入过滤器参数时显示参数类型 -->
{{ price | money(currency=
<!-- 会显示 currency 参数的类型和说明 -->
```

### 4. 📝 代码片段

输入片段前缀并按 Tab 键：
- `for` → 完整的 for 循环结构
- `loginform` → 客户登录表单模板
- `productcard` → 产品卡片展示模板
- `layout` → 基础布局模板

<!-- 3. 尝试智能搜索 -->
{{ name | up }}         <!-- 显示 upcase(), url_encode() 等 -->
{{#customer}}           <!-- 显示客户相关标签 -->
{{#image}}              <!-- 显示 image_tag -->
```

就这么简单！享受智能编码体验 🎉

## 📖 使用方法

### 🚀 快速开始

#### 方式一：智能自动切换（推荐）
1. 在 VS Code 中打开包含 SLine 语法的 `.html` 文件
2. 插件会自动检测 SLine 语法并提示切换语言模式
3. 点击"切换"或"总是自动切换"即可享受完整的 SLine 支持

#### 方式二：手动切换
1. 打开 `.html` 文件
2. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac) 打开命令面板
3. 输入 "SLine" 并选择 "切换到 SLine 语言模式"
4. 或者点击状态栏右下角的语言模式，选择 "SLine"

#### 方式三：使用 .sline 文件扩展名
直接创建 `.sline` 文件，插件会自动激活所有功能。

### ⚙️ 配置选项

在 VS Code 设置中搜索 "sline" 可以找到以下配置：

- `slineLanguageServer.autoSwitchMode`: 自动切换语言模式（默认：false）
- `slineLanguageServer.showModeSuggestion`: 显示切换建议（默认：true）
- `slineLanguageServer.enableDiagnostics`: 启用语法诊断（默认：true）
- `slineLanguageServer.enableCompletion`: 启用代码完成（默认：true）

### 基本语法

```sline
{{! 这是注释 }}

{{#if user.isLoggedIn}}
  <h1>欢迎, {{user.name}}!</h1>
{{else}}
  <h1>请登录</h1>
{{/if}}

{{#each items}}
  <div class="item">{{this.title}}</div>
{{/each}}

{{#component "button" type="primary"}}
  点击我
{{/component}}
```

### 支持的语法结构

#### 块级语句（需要闭合标签）
- **条件语句**: `{{#if}}...{{/if}}`, `{{#unless}}...{{/unless}}`
- **循环语句**: `{{#each}}...{{/each}}`, `{{#with}}...{{/with}}`
- **Schema 定义**: `{{#schema}}...{{/schema}}`
- **内容捕获**: `{{#capture}}...{{/capture}}`

#### 自闭合标签（不需要闭合标签）
- **布局设置**: `{{#layout "theme" /}}` 或 `{{#layout "theme"}}`
- **变量定义**: `{{#var name = "value" /}}` 或 `{{#var name = "value"}}`
- **组件调用**: `{{#component "button" text="点击" /}}`
- **内容区域**: `{{#content "main" /}}`, `{{#sections "header" /}}`
- **样式脚本**: `{{#stylesheet}}`, `{{#script}}`

#### 变量和注释
- **变量输出**: `{{variable}}`, `{{{unescaped}}}`
- **注释**: `{{! comment }}`, `{{!-- block comment --}}`

### 🤖 智能自动完成

SLine 扩展提供强大的智能自动完成功能，支持 **131个自动完成项**（75个过滤器 + 56个标签）：

#### 过滤器自动完成（75个）

在管道符 `|` 后自动触发，支持过滤器链。**注意：所有过滤器都必须以函数形式调用，使用 `()` 括号**：

```sline
{{ product.title | }}               <!-- 显示所有75个过滤器 -->
{{ product.title | up }}            <!-- 筛选显示：upcase(), url_encode()... -->
{{ product.price | money() | up }}    <!-- 支持过滤器链 -->
```

**常用过滤器分类**：
- **文本处理**: `capitalize()`, `upcase()`, `downcase()`, `truncate()`, `strip_html()`
- **数组操作**: `map()`, `where()`, `sort()`, `join()`, `first()`, `last()`, `size()`
- **数值计算**: `plus()`, `minus()`, `times()`, `divided_by()`, `abs()`, `round()`
- **格式化**: `money()`, `date()`, `json()`, `url_encode()`, `escape()`

#### 标签自动完成（56个）

在 `{{#` 后自动触发，按分类智能提示：

```sline
{{#}}                               <!-- 显示所有56个标签 -->
{{#customer}}                       <!-- 筛选客户相关标签 -->
{{#image}}                          <!-- 显示 image_tag -->
{{#link}}                           <!-- 显示 link_to 系列标签 -->
```

**标签分类**：
- **控制流标签** (7个): `if`, `case`, `switch`, `for`, `capture`, `set`, `var`
- **布局组件标签** (8个): `layout`, `component`, `content`, `section`, `schema`
- **表单标签** (25个): `customer_form`, `cart_form`, `contact_form`, `customer_login_form`...
- **媒体资源标签** (10个): `image_tag`, `video_tag`, `stylesheet`, `script`...
- **功能标签** (6个): `link_to`, `payment_button`, `format_address`, `highlight`...

#### 智能触发

- **自动触发字符**: `|` (过滤器), `#` (标签), `{` (表达式)
- **手动触发**: `Ctrl+Space` (Windows/Linux) 或 `Cmd+Space` (Mac)
- **智能匹配**: 部分匹配、大小写不敏感、实时过滤
- **上下文感知**: 只在 `{{ }}` 表达式内部提供相关建议

#### 使用示例

```sline
<!-- 过滤器使用 -->
{{ product.title | capitalize() | truncate(50) }}
{{ products | where("available", true) | map("title") | join(", ") }}
{{ created_at | date("%Y-%m-%d") }}

<!-- 标签使用 -->
{{#if product.available}}
  {{#customer_login_form}}
    <input type="email" name="customer[email]">
    <button type="submit">登录</button>
  {{/customer_login_form}}
{{/if}}

{{#for item in products}}
  {{#image_tag src=item.image alt=item.title}}
  {{#link_to item.url}}{{item.title}}{{/link_to}}
{{/for}}
```

### 可用的代码片段

| 前缀 | 描述 |
|------|------|
| `if` | if 条件语句 |
| `ifelse` | if-else 条件语句 |
| `each` | each 循环 |
| `component` | 组件调用 |
| `layout` | 布局模板 |
| `var` | 变量输出 |

## 🏗️ 项目结构

```
sline-highlight-vscode/
├── src/
│   ├── extension.ts              # VSCode 扩展主入口
│   ├── server/                   # 语言服务器
│   │   ├── server.ts            # 语言服务器主文件
│   │   ├── referenceData.ts     # 参考数据管理器
│   │   ├── contextAnalysis.ts   # 上下文分析器
│   │   ├── snippets.ts          # 代码片段管理器
│   │   ├── *.test.ts           # 单元测试文件
│   │   └── integration.test.ts  # 集成测试
│   ├── tag.json                 # SLine 标签参考数据
│   ├── objects.json             # SLine 对象参考数据
│   └── filter.json              # SLine 过滤器参考数据
├── syntaxes/
│   └── sline.tmLanguage.json    # 语法高亮规则
├── snippets/
│   └── sline.json               # 代码片段定义
├── language-configuration.json  # 语言配置
├── package.json                 # 扩展配置和依赖
├── jest.config.js              # Jest 测试配置
└── README.md                   # 项目文档
```

### 核心模块说明

- **ReferenceDataManager**: 负责加载和管理 JSON 参考数据，提供统一的验证和完成功能
- **ContextAnalyzer**: 分析光标位置的上下文，识别标签、过滤器、对象属性等不同场景
- **SnippetManager**: 管理代码片段，提供分类和智能插入功能
- **Language Server**: 实现 LSP 协议，统一处理所有语法验证、代码完成、悬停、签名帮助等功能

### 🔧 最新改进

- **统一验证系统**: 移除了重复的验证逻辑，现在所有标签、过滤器和对象验证都通过 `ReferenceDataManager` 统一处理
- **降低系统复杂度**: 移除了硬编码常量，避免了竞争条件和不一致的验证结果
- **提升性能**: 简化了代码路径，减少了重复计算和数据加载
- **智能模式切换**: 优化了 HTML 文件中 SLine 语法的检测和模式切换建议
- **改进自动完成体验**: 解决了 `{{#` 自动完成的问题，提供更智能的上下文分析
- **标签和过滤器补充**: 确保所有常用标签（如 `set`）和过滤器（如 `plus`）都能被正确识别

### 📝 文件更新说明

- **contextAnalysis.ts**: 增强了上下文分析器，能够智能识别自动完成的大括号场景，修复了逻辑或运算符 `||` 被误识别为管道符的问题
- **referenceData.ts**: 添加了 `supplementMissingTags` 和 `supplementMissingFilters` 方法，确保所有常用标签和过滤器都可用
- **server.ts**: 修复了管道符识别逻辑，避免逻辑或运算符的误报
- **package.json**: 修复了 `.html` 文件扩展名自动关联，确保智能模式切换正常工作
- **标签覆盖**: 新增支持 `image_tag`、`video_tag`、`time_tag`、`external_video_tag`、`metafield_tag`、`preload_tag`、`payment_button` 等媒体和功能标签
- **过滤器覆盖**: 新增支持 `contains`、`divided_by`、`modulo`、`abs`、`ceil`、`floor`、`round` 等数学和逻辑过滤器



## ⚙️ 配置

在 VS Code 设置中可以配置以下选项：

```json
{
  "slineLanguageServer.enableDiagnostics": true,
  "slineLanguageServer.enableCompletion": true,
  "slineLanguageServer.maxNumberOfProblems": 1000
}
```

### 智能语言服务器配置

扩展默认启用所有智能功能，包括：

- ✅ **上下文感知完成**: 根据光标位置智能提示相关内容
- ✅ **悬停文档**: 鼠标悬停显示完整文档和示例
- ✅ **参数签名帮助**: 实时显示参数类型和说明
- ✅ **智能错误诊断**: 检测拼写错误并提供修复建议
- ✅ **代码片段**: 15+ 预定义片段快速插入常用结构

### 触发方式

- **代码完成**: 输入 `{{#`, `|`, `.` 时自动触发，或按 `Ctrl+Space`
- **悬停文档**: 鼠标悬停在标签、过滤器、对象上
- **参数帮助**: 输入 `(`, `,`, ` ` 时自动显示参数提示
- **代码片段**: 输入片段前缀（如 `for`, `loginform`）并按 Tab
- **错误诊断**: 实时检测并在问题面板显示错误和建议

## 🛠️ 开发

### 先决条件

- Node.js 18+
- VS Code 1.84.0+

### 构建

```bash
# 安装依赖
npm install

# 编译
npm run compile

# 监听模式
npm run watch

# 打包
npm run package
```

### 测试

```bash
# 运行 VSCode 扩展测试
npm run test

# 运行单元测试
npm run test:unit

# 运行单元测试（监听模式）
npm run test:unit:watch

# 生成测试覆盖率报告
npm run test:unit:coverage
```

#### 测试覆盖范围

- **ReferenceDataManager**: 测试 JSON 数据加载、索引构建、完成项生成
- **ContextAnalyzer**: 测试上下文识别、光标位置分析、嵌套表达式处理
- **SnippetManager**: 测试代码片段管理、分类过滤、完成项创建
- **Integration Tests**: 测试完整的语言服务器工作流程和性能

```bash
# 运行测试
npm test

# 监听测试
npm run test:watch
```

## 📝 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解详细的版本历史。

## 🤝 贡献

欢迎贡献！请阅读我们的贡献指南。

1. Fork 这个项目
2. 创建你的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交你的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个 Pull Request

## 📄 许可证

本项目基于 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🐛 故障排除

### 自动完成不工作？

如果自动完成功能无法正常工作，请尝试以下解决方案：

1. **检查文件扩展名** - 确保文件以 `.sline` 结尾
2. **重启语言服务器** - 命令面板 → "SLine: 重启 SLine 语言服务器"
3. **重新加载窗口** - `Ctrl+Shift+P` → "Developer: Reload Window"
4. **检查语法** - 确保在有效的 `{{ }}` 表达式内部
5. **检查设置** - 确认 `slineLanguageServer.enableCompletion` 为 `true`

### 常见问题

**Q: 为什么在某些位置没有自动完成？**
A: 自动完成只在 `{{ }}` 表达式内部工作。确保光标在正确的位置。

**Q: 如何手动触发自动完成？**
A: 按 `Ctrl+Space` (Windows/Linux) 或 `Cmd+Space` (Mac)。

**Q: 过滤器提示为什么不显示？**
A: 确保在管道符 `|` 之后，且在有效的表达式内部。记住过滤器必须以函数形式调用，如 `capitalize()` 而不是 `capitalize`。

## 🐛 问题报告

如果您发现了 bug 或有功能请求，请在 [GitHub Issues](https://github.com/your-username/sline-highlight-vscode/issues) 中报告。

## 📞 支持

- 📧 Email: <EMAIL>
- 💬 [GitHub Discussions](https://github.com/your-username/sline-highlight-vscode/discussions)
- 📖 [Documentation](https://github.com/your-username/sline-highlight-vscode/wiki)
- 🤖 [自动完成指南](AUTOCOMPLETE-GUIDE.md) - 详细的智能完成功能说明

### 📚 相关文档

- [过滤器列表](sline_filters_list.md) - 75个可用过滤器的完整列表
- [标签列表](sline_tags_list.md) - 56个可用标签的完整列表
- [故障排除](TROUBLESHOOTING.md) - 常见问题解决方案

---

如果这个扩展对您有帮助，请考虑给它一个 ⭐️！
