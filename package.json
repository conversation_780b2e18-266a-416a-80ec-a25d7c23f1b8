{"name": "sline-highlight", "displayName": "SLine Language Support", "description": "语法高亮、智能感知和语言服务器支持 SLine 模板引擎", "version": "0.1.5", "publisher": "your-publisher-name", "license": "MIT", "engines": {"vscode": "^1.84.0", "node": ">=18.0.0"}, "categories": ["Programming Languages", "Snippets"], "keywords": ["sline", "template", "syntax", "highlighting", "language-server", "intellisense", "template-engine"], "repository": {"type": "git", "url": "https://github.com/your-username/sline-highlight-vscode.git"}, "bugs": {"url": "https://github.com/your-username/sline-highlight-vscode/issues"}, "homepage": "https://github.com/your-username/sline-highlight-vscode#readme", "galleryBanner": {"color": "#1e1e1e", "theme": "dark"}, "icon": "sline.png", "contributes": {"languages": [{"id": "sline", "aliases": ["SLine", "sline"], "extensions": [".sline"], "configuration": "./language-configuration.json", "icon": {"light": "./sline.png", "dark": "./sline.png"}}], "grammars": [{"language": "sline", "scopeName": "source.sline", "path": "./syntaxes/sline.tmLanguage.json"}], "snippets": [{"language": "sline", "path": "./snippets/sline.json"}], "commands": [{"command": "sline-highlight.helloWorld", "title": "Hello SLine", "category": "SLine"}, {"command": "sline-highlight.restartServer", "title": "重启 SLine 语言服务器", "category": "SLine"}, {"command": "sline-highlight.setLanguageMode", "title": "切换到 SLine 语言模式", "category": "SLine"}], "configuration": {"type": "object", "title": "SLine Language Server", "properties": {"slineLanguageServer.enableDiagnostics": {"type": "boolean", "default": true, "description": "启用 SLine 语法诊断"}, "slineLanguageServer.enableCompletion": {"type": "boolean", "default": true, "description": "启用 SLine 代码完成"}, "slineLanguageServer.maxNumberOfProblems": {"type": "number", "default": 1000, "description": "最大问题数量"}, "slineLanguageServer.autoSwitchMode": {"type": "boolean", "default": false, "description": "检测到 SLine 语法时自动切换语言模式"}, "slineLanguageServer.showModeSuggestion": {"type": "boolean", "default": true, "description": "检测到 SLine 语法时显示切换建议"}}}}, "activationEvents": [], "main": "./dist/extension.js", "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && node esbuild.js", "compile:server": "tsc -p ./src/server", "compile:all": "npm run compile && npm run compile:server", "check-types": "tsc --noEmit", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:web": "node esbuild-web.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "compile:web": "node esbuild-web.js", "pretest": "npm run compile:all", "test": "vscode-test", "test:watch": "npm run pretest && vscode-test --watch", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "lint": "eslint src --ext ts", "package": "npm run check-types && node esbuild.js --production", "publish": "vsce publish"}, "devDependencies": {"@types/node": "^20.x", "@types/vscode": "^1.84.0", "@types/mocha": "^10.0.0", "@types/jest": "^29.5.0", "typescript": "^5.3.0", "@vscode/test-electron": "^2.3.8", "@vscode/test-cli": "^0.0.4", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.0.0", "@vscode/vsce": "^2.22.0", "mocha": "^10.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "esbuild": "^0.19.0", "npm-run-all": "^4.1.5"}, "dependencies": {"vscode-languageclient": "^9.0.1", "vscode-languageserver": "^9.0.1", "vscode-languageserver-textdocument": "^1.0.12"}}