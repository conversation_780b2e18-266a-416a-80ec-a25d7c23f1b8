{"if": {"prefix": "if", "body": ["{{#if ${1:condition}}}", "\t$0", "{{/if}}"], "description": "SLine if block"}, "ifelse": {"prefix": "ifelse", "body": ["{{#if ${1:condition}}}", "\t${2:true_content}", "{{#else/}}", "\t${3:false_content}", "{{/if}}"], "description": "SLine if-else block"}, "each": {"prefix": "each", "body": ["{{#each ${1:items}}}", "\t$0", "{{/each}}"], "description": "SLine each loop"}, "component": {"prefix": "comp", "body": ["{{#component \"${1:component-name}\" ${2:props} /}}"], "description": "SLine self-closing component"}, "component-block": {"prefix": "component", "body": ["{{#component \"${1:component-name}\" ${2:props}}}", "\t$0", "{{/component}}"], "description": "SLine block component"}, "layout": {"prefix": "layout", "body": ["{{#layout \"${1:layout-name}\" /}}"], "description": "SLine layout declaration"}, "var": {"prefix": "var", "body": ["{{#var ${1:variable_name} = ${2:value} /}}"], "description": "SLine variable declaration"}, "set": {"prefix": "set", "body": ["{{#set ${1:variable_name} = ${2:value} /}}"], "description": "SLine variable assignment"}, "content": {"prefix": "content", "body": ["{{#content \"${1:content-name}\" /}}"], "description": "SLine content area"}, "sections": {"prefix": "sections", "body": ["{{#sections \"${1:section-group}\" /}}"], "description": "SLine sections group"}, "stylesheet": {"prefix": "stylesheet", "body": ["{{#component \"stylesheet\" src=\"${1:path}\" | asset_url() /}}"], "description": "SLine stylesheet include"}, "script": {"prefix": "script", "body": ["{{#component \"script\" src=\"${1:path}\" | asset_url() /}}"], "description": "SLine script include"}, "schema": {"prefix": "schema", "body": ["{{#schema}}", "{", "\t\"name\": \"${1:name}\",", "\t\"settings\": [", "\t\t$0", "\t]", "}", "{{/schema}}"], "description": "SLine schema block"}, "capture": {"prefix": "capture", "body": ["{{#capture ${1:variable_name}}}", "\t$0", "{{/capture}}"], "description": "SLine capture block"}, "block-comment": {"prefix": "commentblock", "body": ["{{!--", "${1:comment}", "--}}"], "description": "SLine block comment"}, "comment": {"prefix": "comment", "body": ["{{! ${1:comment} }}"], "description": "SLine single line comment"}, "variable": {"prefix": "output", "body": ["{{${1:variable}}}"], "description": "SLine variable output"}, "raw-variable": {"prefix": "raw", "body": ["{{{${1:variable}}}}"], "description": "SLine raw/unescaped variable output"}, "variable-with-filter": {"prefix": "filter", "body": ["{{${1:variable} | ${2:filter}(${3:params})}}"], "description": "SLine variable with filter"}, "translation": {"prefix": "t", "body": ["{{\"${1:translation_key}\" | t()}}"], "description": "SLine translation"}, "default-filter": {"prefix": "default", "body": ["{{${1:variable} | default(${2:default_value})}}"], "description": "SLine variable with default value"}, "date-filter": {"prefix": "date", "body": ["{{${1:date_variable} | date(format=\"${2:%Y-%m-%d}\")}}"], "description": "SLine date formatting"}, "money-filter": {"prefix": "money", "body": ["{{${1:price} | money_with_currency()}}"], "description": "SLine money formatting"}}