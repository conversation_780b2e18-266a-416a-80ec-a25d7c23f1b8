# 更新日志

本文件记录了 SLine Highlight VSCode Extension 的所有重要更改。

## [0.1.4] - 2024-12-19

### 🔧 Configuration Changes
- ✅ **暂时禁用对象属性验证**: 由于对象属性数据还不够完善，暂时禁用了 "Unknown property" 语法提示，避免误报
- 🔍 **调试过滤器验证问题**: 调查并验证了 `plus` 过滤器的识别问题，确认过滤器数据加载正常
- 🔧 **重构验证系统**: 统一了标签、过滤器和对象的验证逻辑，移除了重复的验证系统，降低了系统复杂度
  - 移除了 `findBasicSyntaxErrors` 中的重复验证逻辑
  - 统一使用 `referenceDataManager` 进行所有验证
  - 移除了硬编码的 `SLINE_TAGS` 和 `SLINE_FILTERS` 常量
  - 简化了自动完成和错误检测的代码路径
  - 避免了竞争条件和不一致的验证结果
- 🔧 **修复智能语言模式切换**: 移除了 package.json 中 `.html` 扩展名的自动关联，恢复智能检测功能
  - 问题原因：`.html` 文件被自动识别为 `sline` 语言，导致智能检测逻辑不被触发
  - 解决方案：只保留 `.sline` 扩展名的自动关联，让 `.html` 文件保持 `html` 语言模式
  - 现在：打开包含 SLine 语法的 `.html` 文件时会正确触发智能模式切换建议
- 🔧 **修复标签验证缺失**: 重新添加了 `supplementMissingTags` 和 `supplementMissingFilters` 方法
  - 问题原因：重构过程中这些方法意外丢失，导致某些标签（如 `set`）无法被识别
  - 解决方案：重新实现了标签和过滤器的补充逻辑，确保所有常用标签和过滤器都可用
  - 现在：`set`、`reset_customer_password_form` 等标签都能被正确识别
- 🎯 **优化自动完成用户体验**: 改进了 `{{` 自动完成后的上下文分析
  - 问题：用户输入 `{{` 自动完成为 `{{}}`，然后输入 `#` 时会产生错误的语法建议
  - 解决方案：增强上下文分析器，能够智能识别自动完成的大括号场景
  - 现在：在自动完成的 `{{}}` 中输入 `#` 或标签名时，提供正确的标签建议
  - 修复了对象属性访问的正则表达式匹配问题

## [Unreleased] - 2025-07-15

### 🔧 紧急修复 - 恢复基本功能

#### 修复的关键问题
- ✅ **修复 "Unknown tag" 错误**: 解决了所有有效标签显示为 "Unknown tag" 的问题
- ✅ **恢复自动完成功能**: 修复了自动完成提示列表失效的问题
- ✅ **改进文件路径解析**: 增强了 JSON 参考数据文件的查找逻辑
- ✅ **添加回退机制**: 当 JSON 文件无法加载时，自动使用内置的基本标签和过滤器数据
- ✅ **增强错误处理**: 改进了语言服务器的错误处理和降级机制
- ✅ **修复 else/elseif 标签识别**: 解决了 `else` 和 `elseif` 标签被误报为 "unknown" 的问题（已重新修复）
- ✅ **修复动态对象属性验证**: 解决了 `props`、`settings`、`block` 等动态对象的属性被误报为 "Unknown property" 的问题
- ✅ **修复自动完成功能**: 解决了输入 `{{#` 时不显示标签列表的问题，补充了 JSON 文件中缺失的标签
- ✅ **智能语言模式切换**: 新增 HTML 文件中 SLine 语法的智能检测和自动切换功能
- ✅ **修复逻辑或运算符误识别**: 解决了 `||` 被错误识别为管道符 `|` 导致的 "Unknown filter" 误报问题
  - 问题原因：上下文分析和诊断功能使用简单的字符串匹配，无法区分逻辑或和管道符
  - 解决方案：实现了智能管道符检测算法，能够正确区分 `||` (逻辑或) 和 `|` (管道符)
  - 现在：`props.desktop_size || props.mobile_size` 不再误报 "Unknown filter 'props'" 错误

#### 新增功能
- ✨ **智能语法检测**: 自动检测 HTML 文件中的 SLine 语法模式
  - 🔍 检测标签语法：`{{#if}}`、`{{#for}}`、`{{#component}}` 等
  - 🔍 检测过滤器语法：`{{product.price | money()}}`
  - 🔍 检测对象属性：`{{product.title}}`、`{{customer.name}}` 等
  - 🔍 检测 SLine 注释：`{{!-- comment --}}`
- 🎯 **智能模式建议**: 检测到 SLine 语法时提示用户切换语言模式
- ⚡ **自动切换选项**: 可配置自动切换模式，无需手动操作
- 🛠️ **灵活配置**: 支持关闭提示或启用自动切换
- 📁 **扩展文件支持**: 插件现在同时支持 `.sline` 和 `.html` 文件扩展名

#### 技术改进
- 🔍 **多路径查找**: JSON 文件加载现在尝试多个可能的路径位置
- 🛡️ **优雅降级**: 当高级功能不可用时，自动回退到基本功能
- 📝 **更好的日志**: 增加了详细的调试信息，便于问题诊断
- ⚡ **快速恢复**: 确保扩展在任何情况下都能提供基本的语法支持

### 🚀 重大功能更新 - 智能语言服务器增强

### 新增 JSON 参考数据集成
- ✨ **完整的 SLine 语法数据库**: 集成了 tag.json、objects.json、filter.json 三个参考数据文件
- 📚 **20+ 标签支持**: 包含完整的标签定义、语法、参数和示例
- 🏷️ **15+ 对象支持**: 支持 product、cart、customer、shop 等核心对象
- 🔧 **80+ 过滤器支持**: 涵盖 money、date、image_url 等所有常用过滤器
- 🌐 **双语文档**: 支持中英文描述和文档

### 智能代码完成系统
- 🎯 **上下文感知完成**: 根据光标位置智能识别标签、过滤器、对象属性上下文
- 💡 **参数智能提示**: 为标签和过滤器提供参数名称和类型提示
- 🔍 **模糊搜索支持**: 支持前缀匹配和智能过滤
- 📝 **丰富的元数据**: 每个完成项包含详细描述、语法示例和文档链接
- ⚡ **性能优化**: 预计算完成项，提供毫秒级响应速度

### 悬停文档系统
- 📖 **即时文档**: 鼠标悬停显示完整的语法文档和示例
- 🔗 **官方文档链接**: 直接链接到官方文档页面
- ⚠️ **弃用警告**: 自动标识和警告已弃用的标签和过滤器
- 🎨 **Markdown 格式**: 美观的格式化文档显示
- 🌍 **多语言支持**: 中英文文档同时显示

### 参数签名帮助
- 📋 **实时参数提示**: 输入标签或过滤器时显示参数签名
- 🎯 **活动参数高亮**: 智能识别当前输入的参数位置
- 📚 **参数文档**: 每个参数包含类型信息和详细说明
- ⌨️ **触发字符**: 在 `(`, `,`, ` ` 时自动触发参数提示

### 智能错误诊断
- 🔍 **未知标签检测**: 自动识别拼写错误或不存在的标签
- 🎯 **智能建议**: 为错误的标签和过滤器提供相似名称建议
- 🏷️ **属性验证**: 验证对象属性是否存在，提供可用属性建议
- ⚠️ **弃用检测**: 自动检测并警告使用已弃用的语法
- 💡 **修复建议**: 提供具体的修复建议和替代方案

### 代码片段系统
- 📝 **15+ 预定义片段**: 涵盖控制流、表单、布局、数据展示等常用模式
- 🎨 **分类管理**: 按功能分类（control、form、layout、data、utility）
- 🔧 **参数占位符**: 使用 VSCode 片段格式，支持 Tab 键跳转
- 📚 **完整模板**: 包含登录表单、产品卡片、分页导航等完整模板
- 🌟 **智能插入**: 根据上下文智能插入相关代码片段

### 技术架构升级
- 🏗️ **模块化设计**: ReferenceDataManager、ContextAnalyzer、SnippetManager 独立模块
- ⚡ **异步加载**: 启动时异步加载参考数据，不阻塞编辑器
- 🔄 **实时索引**: 构建高效的查找索引，支持 O(1) 查找性能
- 🧪 **完整测试**: 包含单元测试、集成测试和性能测试
- 📊 **错误处理**: 优雅的错误处理和降级机制

### 开发体验改进
- 🎯 **精确上下文**: 支持嵌套表达式、多行表达式的精确解析
- 🔧 **调试支持**: 详细的日志记录和调试信息
- 📈 **性能监控**: 内置性能监控，确保响应速度
- 🛡️ **向后兼容**: 保持与现有功能的完全兼容
- 🌐 **国际化**: 支持中英文界面和文档

## [0.1.3] - 2025-07-10

### 🎨 语法高亮优化
- ✅ **注释颜色统一**: SLine 注释 `{{!-- --}}` 现在与 HTML 注释保持一致的绿色显示
- ✅ **视觉体验改进**: 提高了 SLine 模板文件的可读性和一致性
- ✅ **块标签颜色统一**: 修复 `{{#` 前三个字符在不同类型块中颜色不一致的问题
  - **问题原因**: 自闭合标签和一般块标签的捕获组处理方式不同导致颜色差异
  - **解决方案**: 统一所有块类型的 `{{#` 标点符号处理，使用相同的捕获组结构
  - **修复范围**: `component`, `content`, `if`, `each`, `else` 等所有块标签

### 🐛 语法检测修复
- ✅ **自闭合标签识别**: 正确识别 SLine 自闭合标签，解决 `{{#layout}}` 和 `{{#var}}` 的误报问题
- ✅ **基于实际代码分析**: 通过分析 theme-sline 项目的 302+ HTML 文件，准确识别自闭合标签模式
- ✅ **支持的自闭合标签**: `layout`, `var`, `component`, `content`, `section`, `sections`, `stylesheet`, `script`, `meta-tags`
- ✅ **智能语法检测**: 区分需要闭合的块级标签（`if`, `each`, `schema`, `capture`）和自闭合标签

### 🔧 LSP 自动完成功能修复
- ✅ **过滤器自动完成修复**: 解决输入 `|` 管道符时显示错误建议的问题
  - **问题原因**: 上下文检查顺序冲突，块助手检查会错误匹配包含 `|` 的情况
  - **解决方案**: 修改块助手正则表达式 `/\{\{#([^}]*)$/` → `/\{\{#([^}|]*)$/`，排除包含管道符的情况
  - **现在正确**: 输入 `{{#component props |` 显示过滤器函数列表而非 SLine 语法
- ✅ **过滤器检测优化**: 改进过滤器上下文识别正则表达式 `/\{\{[^}]*\|\s*([^}]*)$/`
- ✅ **支持的过滤器**: 25+ 过滤器函数包括 `money`, `date`, `upcase`, `asset_url`, `class_list` 等
- ✅ **智能上下文**: 根据输入位置准确区分块助手语法和过滤器语法

### 🆕 新增功能
- ✅ **代码片段支持**: 添加了常用 SLine 语法的代码片段
- ✅ **增强语言配置**: 改进了 SLine 特定语法的支持
- ✅ **ESLint 配置**: 添加了代码质量检查工具
- ✅ **测试框架**: 建立了完整的测试环境
- ✅ **现代化依赖**: 更新到最新版本的依赖包

### 🔧 改进优化
- ✅ **TypeScript 配置**: 使用现代 ES2022 和 Node16 模块
- ✅ **包配置优化**: 完善了 package.json 元数据
- ✅ **更好的 .gitignore**: 改进了忽略文件模式
- ✅ **激活事件**: 优化了扩展激活条件

### 🐛 修复问题
- ✅ **语言服务器激活**: 修复了激活事件配置
- ✅ **括号匹配**: 改进了 SLine 语法的括号匹配
- ✅ **LSP协议兼容性**: 修复 "textDocument/diagnostic" 未处理方法错误
- ✅ **语法高亮颜色完全统一**: 使用统一的`punctuation.handlebars`作用域，彻底解决标点符号颜色不一致问题

## [0.1.3] - 2025-07-09

### 🔧 修复自动完成显示问题

### 修复的关键问题
- ✅ **修复只显示4个提示项的问题**: 重构了完成项逻辑
- ✅ **优化触发条件**: 简化了 `{{#` 的触发逻辑
- ✅ **清理代码结构**: 移除了有问题的大数组定义
- ✅ **添加调试日志**: 便于问题诊断和调试

### 技术改进
- 🔍 **简化触发逻辑**: 移除了过于严格的条件判断
- 📝 **重构完成项数组**: 使用更清晰的数组结构
- 🐛 **修复数组语法**: 解决了导致只显示部分项目的问题
- 📊 **增强日志记录**: 添加详细的调试信息

### 现在支持的完成项 (17个)
- **控制流**: `if`, `each`, `for`, `with`, `unless`, `else`
- **SLine 指令**: `component`, `layout`, `content`, `section`, `sections`
- **区块系统**: `blocks`, `block`
- **变量操作**: `var`, `set`, `capture`
- **配置**: `schema`

## [0.1.2] - 2025-07-09

### 🚀 LSP 功能大幅扩展和优化

### 新增完整的 SLine 语法支持
- ✨ **大幅扩展关键词库**: 基于 theme-sline 项目分析，新增 30+ 关键词
- 🎨 **优化图标显示**: 为不同类型的完成项配置专门的图标和描述
- 📚 **完整的语法覆盖**: 支持所有 SLine 模板引擎语法特性

### 控制流指令 (7个)
- `if/else` - 条件语句 🔀
- `each` - 循环遍历 🔄
- `for` - for 循环 🔄
- `with` - 上下文切换 📦
- `unless` - 反向条件 🚫
- `else` - 分支语句 ↔️

### SLine 特有指令 (7个)
- `component` - 组件调用 🧩
- `layout` - 布局设置 🏗️
- `content` - 内容区域 📄
- `section` - 单个区域 📋
- `sections` - 区域组 📚
- `blocks` - 区块遍历 🧱
- `block` - 区块渲染 🧱

### 变量和数据操作 (4个)
- `var` - 变量定义 📝
- `set` - 变量设置 ✏️
- `capture` - 内容捕获 📥
- `schema` - 配置模式 ⚙️

### 过滤器函数 (25个)
- **货币**: `money`, `money_with_currency`, `money_without_currency`
- **文本**: `upper`, `lower`, `capitalize`, `truncate`, `trim`, `replace`, `split`
- **数组**: `first`, `last`, `size`, `join`
- **日期**: `date`, `format`
- **URL**: `asset_url`, `append`, `prepend`
- **HTML**: `strip_html`, `escape`
- **通用**: `default`, `t`, `json`, `class_list`
- **SLine**: `get_variants`

### 变量提示 (20个)
- **全局对象**: `shop`, `customer`, `request`, `settings`, `routes`, `localization`
- **模板上下文**: `block`, `section`, `props`, `forblock`
- **产品相关**: `product`, `products`, `collection`, `collections`, `variant`
- **内容相关**: `article`, `articles`, `blog`, `blogs`, `page`, `pages`
- **购物相关**: `cart`, `order`
- **循环变量**: `this`, `forloop`

### 用户体验改进
- 🎯 **智能图标**: 不同类型使用不同的 VSCode 图标
- 📖 **丰富描述**: 每个项目都有详细的说明和用法示例
- 🏷️ **分类标签**: 通过 detail 字段显示功能分类
- 🌟 **表情符号**: 使用表情符号增强视觉识别

## [0.1.1] - 2025-07-09

### 🔧 LSP 功能修复和改进

### 修复的问题
- ✅ **智能代码完成现在正常工作**: 修复了上下文感知的自动完成
- ✅ **错误检查功能**: 修复了语法诊断和错误提示
- ✅ **悬停信息**: 修复了基于位置的悬停帮助
- ✅ **文档选择器**: 改进了文件类型识别

### 技术改进
- 🔍 **智能触发**: 根据输入上下文提供相关的完成项
- 📝 **代码片段**: 使用 VSCode 代码片段格式提供更好的插入体验
- 🐛 **调试支持**: 添加详细的日志记录和调试配置
- 📋 **故障排除**: 创建完整的故障排除指南

### 新增功能
- **上下文感知完成**:
  - 在 `{{#` 后提示控制结构
  - 在 `{{#component "` 后提示组件名称
  - 在 `|` 后提示过滤器
- **改进的错误检查**: 更准确的语法验证
- **增强的悬停信息**: 基于光标位置的智能帮助

### 调试和测试
- 📊 **调试配置**: 添加语言服务器调试支持和 VSCode 任务
- 🧪 **测试文件**: 改进的 LSP 功能测试文件
- 📖 **故障排除指南**: 详细的问题诊断和解决方案
- 🚀 **快速启动指南**: 一步步的功能测试指南

### 开发体验改进
- ✅ **修复 F5 启动问题**: 添加正确的 VSCode 任务配置
- 🔧 **编译任务**: 统一的编译和构建流程
- 📝 **文档完善**: 详细的使用和调试文档

## [0.1.0] - 2025-07-09

### 🚀 重大功能更新 - 添加语言服务器协议(LSP)支持

### 新增 LSP 功能
- ✨ **智能代码完成**: 支持 SLine 语法的自动完成
  - Handlebars 控制结构 (`if`, `each`, `with`)
  - SLine 组件调用 (`component`, `layout`, `content`)
  - 过滤器函数 (`money`, `asset_url`, `date`)
- 🔍 **实时错误检查**: 语法诊断和错误提示
  - 未闭合的 Handlebars 标签检测
  - 未定义组件警告
  - 语法错误高亮
- 💡 **悬停信息**: 鼠标悬停显示语法帮助
- 🎯 **触发字符**: 在输入 `{`, `#`, `|`, `.` 时自动触发完成

### 技术架构
- **语言服务器**: 独立的 Node.js 进程处理语言功能
- **客户端集成**: VSCode 插件通过 JSON-RPC 与服务器通信
- **文档同步**: 实时同步文档变更和诊断
- **配置支持**: 可配置的诊断和完成选项

### 支持的 LSP 功能
- **文档同步**: 实时跟踪文件变更
- **诊断**: 错误和警告提示
- **代码完成**: 智能提示和自动完成
- **悬停信息**: 语法帮助和文档
- **配置管理**: 用户可自定义设置

### 新增命令
- `sline-highlight.restartServer`: 重启语言服务器
- `sline-highlight.helloWorld`: 测试命令

### 配置选项
- `slineLanguageServer.enableDiagnostics`: 启用/禁用诊断
- `slineLanguageServer.enableCompletion`: 启用/禁用代码完成
- `slineLanguageServer.maxNumberOfProblems`: 最大问题数量限制

### 开发体验改进
- 🔧 **调试支持**: 语言服务器调试配置
- 📝 **测试文件**: 创建 LSP 功能测试文件
- 🏗️ **构建优化**: 分离客户端和服务器编译

## [0.0.1] - 2025-07-09

### 新增功能
- ✨ 初始化 VSCode 插件项目结构
- 🎨 实现基础的 SLine 语言语法高亮
- 📝 添加语言配置文件，支持括号匹配和自动关闭
- 💬 支持单行注释 (`//`) 和多行注释 (`/* */`)
- 🔧 配置代码折叠功能
- 📦 设置项目构建和开发环境

### 技术实现
- 创建了 `package.json` 配置文件，定义插件基本信息和贡献点
- 实现了 `sline.tmLanguage.json` 语法规则文件
- 配置了 TypeScript 开发环境
- 添加了 VSCode 调试配置

### 支持的语法元素
- **关键字**: if, else, while, for, return, break, continue, function, var, let, const
- **字面量**: true, false, null, undefined  
- **字符串**: 双引号和单引号字符串，支持转义字符
- **数字**: 整数和浮点数
- **操作符**: 算术、比较、逻辑和赋值操作符
- **函数**: 函数名识别和高亮
- **注释**: 单行和多行注释

### 文件支持
- 支持 `.sline` 和 `.html` 文件扩展名

## [0.0.3] - 2025-07-09

### 重大更新
- 🔄 完全重写语法高亮规则，基于真实的 SLine 模板引擎语法
- 📚 深度分析 theme-sline 目录下的实际模板文件
- 🎯 精确匹配 Handlebars 风格的 SLine 语法

### 新增 Handlebars 风格语法支持
- **变量输出**: `{{variable}}` - 双大括号变量输出
- **不转义输出**: `{{{html_content}}}` - 三大括号不转义输出
- **块级指令**: `{{#if}}...{{/if}}`, `{{#each}}...{{/each}}`
- **组件调用**: `{{#component "name" /}}`
- **布局系统**: `{{#layout "theme" /}}`
- **内容区域**: `{{#content "section" /}}`
- **变量定义**: `{{#var name = value /}}`
- **模板注释**: `{{!-- comment --}}`

### 高级语法特性
- **过滤器系统**: `{{value | filter()}}`, `{{price | money}}`
- **Schema 配置**: `{{#schema}}...{{/schema}}`
- **条件分支**: `{{#if}}...{{#else}}...{{/if}}`
- **循环遍历**: `{{#each items}}...{{/each}}`
- **属性访问**: `{{object.property.subproperty}}`

### 支持的过滤器
- `asset_url`, `money`, `money_with_currency`
- `date`, `format`, `upper`, `lower`
- `truncate`, `default`, `class_list`
- `t` (翻译), `escape`, `strip_html`

### 技术实现
- 完全重构 `sline.tmLanguage.json` 语法规则
- 基于实际 theme-sline 项目的语法分析
- 优化语法匹配优先级和准确性
- 更新示例文件展示真实的 SLine 语法

## [0.0.2] - 2025-07-09

### 新增功能
- ✨ 添加模板引擎语法支持
- 🎨 支持 `.html` 文件中的 SLine 模板语法
- 📝 添加模板变量输出语法 `{{ variable }}`
- 🔧 添加模板控制块语法 `{% if %} {% endif %}`
- 💬 添加模板注释语法 `{# comment #}`
- 🔍 添加模板过滤器语法 `{{ variable | filter }}`

### 模板语法支持
- **变量输出**: `{{ username }}`, `{{ user.email }}`
- **控制结构**: `{% if %}`, `{% for %}`, `{% while %}` 等
- **模板注释**: `{# 这是注释 #}`
- **过滤器**: `{{ date | format('Y-m-d') }}`
- **嵌套语法**: 支持复杂的嵌套模板结构

### 技术改进
- 更新了 `sline.tmLanguage.json` 语法规则文件
- 添加了模板引擎专用的语法模式
- 创建了 HTML 模板示例文件
