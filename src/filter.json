[{"arguments": [{"description": "Payment type, required\n", "description_cn": "付款类型，必填", "name": "type", "types": ["string"]}], "deprecated": false, "examples": [], "link": "https://developer.shopline.com/docs/sline/filter/payment-type-img-url", "name": "payment_type_img_url", "summary": "Returns the URL for an SVG image of a given payment type.", "summary_cn": "返回给定支付类型的 SVG 图片的 URL。\n\n", "syntax": "```html\n{{ \"affrim\" | payment_type_img_url() }}\n```"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}, {"description": "The string to append to the end of base string.", "description_cn": "要追加到原始字符串末尾的字符串。", "name": "suffix", "types": ["string"]}], "deprecated": false, "description_cn": "", "examples": [{"raw_sline": "{{ \"Fake Two-\" | append(\"piece <PERSON><PERSON>\") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/append", "name": "append", "return_type": "string", "summary": "Appends one string to the end of another and returns the concatenated result.", "summary_cn": "将一个字符串追加到另一个字符串的末尾，返回合并后的新字符串。", "syntax": "str | append(suffix)"}, {"arguments": [{"description": "Amount, Required", "description_cn": "金额, 必填", "name": "price", "types": ["number"]}, {"description": "Currency, not required, defaults to market currency", "description_cn": "币种，非必填，默认值为市场币种", "name": "code", "types": ["string"]}], "deprecated": false, "examples": [{"description": "", "name": "default", "name_cn": "default", "raw_sline": "{{ 10000 | money()}}", "syntax": ""}, {"name": "code", "name_cn": "code", "raw_sline": "{{ 10000 | money(code=\"USD\")}}"}], "link": "https://developer.shopline.com/docs/sline/filter/money", "name": "money", "return_type": "string", "summary": "A given price is formed based on the shop's settings that do not include a currency code format.", "summary_cn": "根据商店的不包含货币code格式的设置，形成一个给定的价格。\n", "syntax": "{{ \"pour price\" | money()}}"}, {"arguments": [{"description_cn": "金额，必填", "name": "price", "types": ["number"]}, {"description_cn": "币种，非必填，默认值为市场币种", "name": "code", "types": ["string"]}], "deprecated": false, "examples": [{"name": "default", "name_cn": "default", "raw_sline": "{{10000 | money_with_currency()}}"}, {"name": "code", "name_cn": "code", "raw_sline": "{{10000 | money_with_currency(code=\"USD\")}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/money-with-currency", "name": "money_with_currency", "return_type": "string", "summary_cn": "根据商店的包含货币code格式的设置，形成一个给定的价格。返回格式化后的金额。", "syntax": "{{\"your price\" | money_with_currency()}}"}, {"arguments": [{"description_cn": "金额, 必填", "name": "price", "types": ["number"]}, {"description_cn": "币种，非必填，默认值为市场币种", "name": "code", "types": ["string"]}], "deprecated": false, "examples": [{"name": "default", "name_cn": "default", "raw_sline": "{{10000 | money_without_currency()}}", "syntax": ""}, {"name": "code", "name_cn": "code", "raw_sline": "{{10000 | money_without_currency(code=\"USD\")}}"}], "link": "https://developer.shopline.com/docs/sline/filter/money-without-currency", "name": "money_without_currency", "return_type": "string", "summary_cn": "根据商店的不包含货币code格式的设置，形成一个给定的价格，没有货币符号。返回格式化后的金额。\n\n", "syntax": "{{\"your price\" | money_without_currency()}}"}, {"arguments": [{"description": "metafield data", "description_cn": "metafield 数据", "name": "metafield", "types": ["object"]}], "deprecated": false, "description": "The following metafield types are supported:\n\n`boolean` Boolean value\n\n`date` Date\n\n`date_at_time` Date and time\n\n`json` JSON\n\n`money` Money\n\n`rich_text_field` Rich text\n\n`multi_line_text_field` Multi-line text\n\n`color` Color value\n\n`number_decimal` Floating point number\n\n`number_integer` Integer number\n\n`rating` Rating value\n\n`url` URL\n\n`weight` Weight\n\n`volume` Volume\n\n`dimension` Dimension\n\n`single_line_text_field` Single line text\n\n`collection_reference` Category collection title\n\n`page_reference` Page title\n\n`product_reference` Product title\n\n`variant_reference` Variant title\n\n`file_reference` File URL\n\n> Tip: Currently `metafield_text` only supports single-line text `single_line_text_field` Pass multiple values.", "description_cn": "支持以下几种元字段类型：\n\n`boolean` 布尔值\n\n`date` 日期\n\n`date_at_time` 日期与时间\n\n`json` JSON\n\n`money` 资金\n\n`rich_text_field` 富文本\n\n`multi_line_text_field` 多行文本\n\n`color` 颜色值\n\n`number_decimal` 浮点型数字\n\n`number_integer` 整型数字\n\n`rating` 评分值\n\n`url` URL\n\n`weight` 重量\n\n`volume` 体积\n\n`dimension` 尺寸\n\n`single_line_text_field` 单行文本\n\n`collection_reference` 分类集合标题\n\n`page_reference` 页面标题\n\n`product_reference` 商品标题\n\n`variant_reference` 变体标题\n\n`file_reference` 文件 URL\n\n> 提示：目前 `metafield_text` 仅支持单行文本 `single_line_text_field` 传入多个值。", "examples": [{"description": "To obtain meta field text data, you need to use [get_metafields filter](/docs/sline/filter/get-metafields).", "description_cn": "获取元字段文本数据需要结合 [get_metafields filter](/docs/sline/filter/get-metafields) 使用。", "name": "Basic example", "name_cn": "基础示例", "path": "/products/earring-ab001", "raw_sline": "{{#var metafield_ns = product | get_metafields(\"my_fields\") /}}\n\n{{ metafield_ns.my_key | metafield_text() }}", "source_object": "product", "syntax": "{{ variable | metafield_text() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/metafield-text", "name": "metafield_text", "return_type": ["string"], "summary": "Generates metafield text data.", "summary_cn": "生成元字段文本数据。", "syntax": "metafield | metafield_text()"}, {"arguments": [{"description": "Media object", "description_cn": "媒体对象", "name": "media", "types": ["object"]}], "deprecated": false, "description": "You can add specific parameters to the video link. The parameter names and related optional values ​​should be the same as [YouTuBe API](https://developers.google.com/youtube/player_parameters#Parameters).", "description_cn": "你可以为视频链接添加指定参数，参数名称及相关可选值需与 [YouTuBe API](https://developers.google.com/youtube/player_parameters#Parameters) 相同。", "examples": [{"description": "The generation of YouTube video player needs to be used in conjunction with `external_video_tag`.", "description_cn": "YouTuBe 视频播放器的生成，需结合 `external_video_tag` 使用。", "name": "Basic example", "name_cn": "基础示例", "path": "/products/earring-ab001", "raw_sline": "{{#for media in product.media}}\n    {{#switch media.media_type}}\n        {{#case \"external_video\" /}}\n            {{#if media.host == \"youtube\"}}\n                {{#external_video_tag media | external_video_url(autoplay=false, loop=true) /}}\n            {{/if}}\n    {{/switch}}\n{{/for}}", "source_object": "product"}], "hashs": [{"description": "Whether to play automatically. De<PERSON><PERSON> is `false`", "description_cn": "是否自动播放。默认为 `false`\n", "name": "autoplay", "types": ["boolean"]}, {"description": "Whether to show player controls. Defaults to `true`", "description_cn": "是否显示播放器控件。默认为 `true`", "name": "controls", "types": ["boolean"]}, {"description": "Whether to loop playback. De<PERSON><PERSON> is `false`", "description_cn": "是否循环播放。默认为 `false`", "name": "loop", "types": ["boolean"]}], "link": "https://developer.shopline.com/docs/sline/filter/external-video-url", "name": "external_video_url", "return_type": "string", "summary": "Returns a link to the specified external video.", "summary_cn": "返回指定外部视频的链接。", "syntax": "media | external_video_url(attribute=any)"}, {"arguments": [{"description": "data object", "description_cn": "数据对象", "name": "variable", "types": ["object"]}], "deprecated": false, "description": "You can get the image URL of the following data objects by using the `image_url` filter.\n\n[image](/docs/sline/object/image) object src\n\nImage.\n\n[article](/docs/sline/object/article) object image.src\n\nArticle.\n\n`line_item` object image.src\n\nEach line of product data in the shopping cart, checkout, or order.\n\n`variant` object image.src\n\nProduct variant.\n\n`product` object featured_image.src\n\nProduct.\n\n[collection](/docs/sline/object/collection) object featured_image.src\n\nProduct category.\n\n`video` object preview_image.src\n\nVideo.", "description_cn": "你可以通过 `image_url` filter 获取以下数据对象的图片链接。\n\n[image](/docs/sline/object/image) object src\n\n图片。\n\n[article](/docs/sline/object/article) object image.src\n\n文章。\n\n`line_item` object image.src\n\n购物车、结帐或订单中的每一行商品数据。\n\n`variant` object image.src\n\n商品变体。\n\n`product` object featured_image.src\n\n商品。\n\n[collection](/docs/sline/object/collection) object featured_image.src\n\n商品分类。\n\n`[video` object preview_image.src\n\n视频。", "examples": [{"description": "If you only specify the image width, the image height will be scaled proportionally to the original image.", "description_cn": "如果只指定图片宽度，图片高度会根据原图比例等比缩放。", "name": "Set image width", "name_cn": "设置图片宽度", "path": "/products/earring-ab001", "raw_sline": "{{ product | image_url(width=100) }}", "source_object": "product", "syntax": "variable | image_url(width=number)"}, {"description": "If you only specify the image height, the image width will be scaled proportionally to the original image.", "description_cn": "如果只指定图片高度，图片宽度会根据原图比例等比缩放。", "name": "Set image height", "name_cn": "设置图片高度", "path": "/products/earring-ab001", "raw_sline": "{{ product | image_url(height=100) }}", "source_object": "product", "syntax": "variable | image_url(height=number)"}, {"description": "The default value is 80 and the selectable range is 0 to 100.", "description_cn": "默认值为 80，可区范围为 0 ～ 100 。", "name": "Set image compression ratio", "name_cn": "设置图片压缩比", "path": "/products/earring-ab001", "raw_sline": "{{ product | image_url(quality=90) }}", "source_object": "product", "syntax": "variable | image_url(quality=number)"}], "hashs": [{"description": "Image width", "description_cn": "图片宽度", "name": "width", "types": ["number"]}, {"description": "Image height", "description_cn": "图片高度", "name": "height", "types": ["number"]}, {"description": "Image compression ratio", "description_cn": "图片压缩比", "name": "quality", "types": ["number"]}], "link": "https://developer.shopline.com/docs/sline/filter/image-url", "name": "image_url", "return_type": ["string"], "summary": "Returns the remote CDN link of the image.", "summary_cn": "返回图片的远程 CDN 链接。", "syntax": "variable | image_url(width=number, height=number, quality=number)"}, {"arguments": [{"description": "Type.", "description_cn": "产品类型。", "name": "type", "types": ["string"]}], "deprecated": false, "examples": [{"name_cn": "url_for_type", "raw_sline": "\n{{ \"type\" | url_for_type() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/url-for-type", "name": "url_for_type", "return_type": "string", "summary": "Generate a URL for a product collection page that showcases all the products of the specified product type. ", "summary_cn": "生成一个商品集合页面的网址，该页面展示指定产品类型的所有商品。", "syntax": "{{ \"type\" | url_for_type() }}"}, {"arguments": [{"description": "Vender.", "description_cn": "产品品牌。", "name": "vendor", "types": ["string"]}], "deprecated": false, "examples": [{"name_cn": "url_for_vendor", "raw_sline": "{{ \"vendor\" | url_for_vendor() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/url-for-vendor", "name": "url_for_vendor", "return_type": "string", "summary": "Generate a URL for a product collection page that displays all the products of the specified product brand. ", "summary_cn": "生成一个商品集合页面的网址，该页面展示指定产品品牌的所有商品。", "syntax": "{{ \"vendor\" | url_for_vendor() }}"}, {"arguments": [{"description": "All resource objects of a specified type. Currently, only the object types listed above are supported.", "description_cn": "某种类型的全部资源对象，目前仅支持上述列举出来的 Object 类型。", "name": "obj", "types": ["any"]}, {"description": "Resource ID or handle.", "description_cn": "资源 id 或 handle。", "name": "idOrHandle", "types": ["string"]}], "deprecated": false, "description": "\nTo be used in combination with the following objects:\n\n- [all_products](/docs/sline/object/all-products)\n- [all_collections](/docs/sline/object/all-collections)\n- [all_articles](/docs/sline/object/all-articles)\n- [all_blogs](/docs/sline/object/all-blogs)\n- [all_pages](/docs/sline/object/all-pages)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [all_products](/docs/sline/object/all-products)\n- [all_collections](/docs/sline/object/all-collections)\n- [all_articles](/docs/sline/object/all-articles)\n- [all_blogs](/docs/sline/object/all-blogs)\n- [all_pages](/docs/sline/object/all-pages)", "examples": [{"raw_sline": "{{#var myProduct = all_products | get(\"winter-wonderland-gift-box\") /}}\n{{ myProduct.title }}"}], "link": "https://developer.shopline.com/docs/sline/filter/get", "name": "get", "return_type": ["any"], "summary": "Get a single resource object using the ID or handle of a resource of a specified type.", "summary_cn": "通过某种类型资源的 id 或 handle 获取单个资源对象。", "syntax": "obj | get(idOrHandle)"}, {"arguments": [{"description": "All resource objects of the specified type.", "description_cn": "指定类型的全部资源对象。", "name": "obj", "types": ["any"]}, {"description": "Number of items per page.", "description_cn": "每页数量。", "name": "pageSize", "types": ["number"]}], "array_return_type": [], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [all_collections](/docs/sline/object/all-collections)\n- [all_pages](/docs/sline/object/all-pages)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [all_collections](/docs/sline/object/all-collections)\n- [all_pages](/docs/sline/object/all-pages)", "examples": [{"raw_sline": "{{#var paginate = all_collections | get_pagination(10) /}}\n{{#for collection in paginate.list}}\n    {{forloop.index}}: {{ collection.title }}\n{{/for}}"}], "hashs": [], "link": "https://developer.shopline.com/docs/sline/filter/get-pagination", "name": "get_pagination", "return_type": ["paginate"], "summary": "Get resource objects of a specified type via pagination.", "summary_cn": "通过分页获取指定类型的资源对象。", "syntax": "obj | get_pagination(pageSize)"}, {"arguments": [{"description": "The article object.", "description_cn": "博客文章 Object 对象。", "name": "article", "types": ["any"]}, {"description": "Number of items per page.", "description_cn": "每页数量。", "name": "pageSize", "types": ["number"]}], "array_return_type": [], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [article](/docs/sline/object/article)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [article](/docs/sline/object/article)", "examples": [{"path": "/blogs/news/blog-2", "raw_sline": "{{#var paginate = article | get_comment_pagination(10) /}}\n{{#for item in paginate.list}}\n    {{forloop.index}}: {{ item.content }}\n{{/for}}"}], "link": "https://developer.shopline.com/docs/sline/filter/get-comment-pagination", "name": "get_comment_pagination", "return_type": ["paginate"], "summary": "Get comment objects via pagination.", "summary_cn": "通过分页获取评论对象。", "syntax": "article | get_comment_pagination(pageSize)"}, {"arguments": [{"description": "The blog object.", "description_cn": "博客 Object 对象。", "name": "blog", "types": ["any"]}, {"description": "Number of items per page.", "description_cn": "每页数量。", "name": "pageSize", "types": ["number"]}], "array_return_type": [], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [blog](/docs/sline/object/blog)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [blog](/docs/sline/object/blog)", "examples": [{"path": "/blogs/news", "raw_sline": "{{#var paginate = blog | get_article_pagination(10) /}}\n{{#for item in paginate.list}}\n    {{forloop.index}}: {{ item.title }}\n{{/for}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/get-article-pagination", "name": "get_article_pagination", "return_type": ["paginate"], "summary": "Get article objects via pagination.", "summary_cn": "通过分页获取文章对象。", "syntax": "blog | get_article_pagination(pageSize)"}, {"arguments": [{"description": "The customer object.", "description_cn": "客户 Object 对象。", "name": "customer", "types": ["any"]}, {"description": "Number of items per page.", "description_cn": "每页数量。", "name": "pageSize", "types": ["number"]}], "array_return_type": [], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [customer](/docs/sline/object/customer)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [customer](/docs/sline/object/customer)", "examples": [{"raw_sline": "", "syntax": "{{#var paginate = customer | get_order_pagination(10) /}}\n{{#for item in paginate.list}}\n  {{forloop.index}}: {{ item.id }}\n{{/for}}"}], "link": "https://developer.shopline.com/docs/sline/filter/get-order-pagination", "name": "get_order_pagination", "return_type": ["paginate"], "summary": "Get order objects via pagination.", "summary_cn": "通过分页获取订单对象。", "syntax": "customer | get_order_pagination(pageSize)"}, {"arguments": [{"description": "Search object.", "description_cn": "搜索结果对象。", "name": "search", "types": ["any"]}, {"description": "Number of items per page.", "description_cn": "每页数量。", "name": "pageSize", "types": ["number"]}], "array_return_type": [], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [search](/docs/sline/object/search)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [search](/docs/sline/object/search)", "examples": [{"path": "/search?keyword=1", "raw_sline": "{{#var paginate = search | get_search_pagination(10) /}}\n{{#for item in paginate.list}}\n  {{forloop.index}}: {{ item.title }}\n{{/for}}", "source_object": "", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/get-search-pagination", "name": "get_search_pagination", "return_type": ["paginate"], "summary": "Get search result objects via pagination.", "summary_cn": "通过分页获取搜索出来的结果对象。", "syntax": "search | get_search_pagination(pageSize)"}, {"arguments": [{"description": "The specified resource object.", "description_cn": "指定的资源对象。", "name": "obj", "types": ["any"]}, {"description": "The namespace of the metafield.", "description_cn": "元字段的命名空间。", "name": "namespace", "types": ["string"]}], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [product](/docs/sline/object/product)\n- [collection](/docs/sline/object/collection)\n- [blog](/docs/sline/object/blog)\n- [article](/docs/sline/object/article)\n- [page](/docs/sline/object/page)\n- [order](/docs/sline/object/order)\n- [customer](/docs/sline/object/customer)\n- [variant](/docs/sline/object/variant)\n- [shop](/docs/sline/object/shop)\n- [company](/docs/sline/object/company)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [product](/docs/sline/object/product)\n- [collection](/docs/sline/object/collection)\n- [blog](/docs/sline/object/blog)\n- [article](/docs/sline/object/article)\n- [page](/docs/sline/object/page)\n- [order](/docs/sline/object/order)\n- [customer](/docs/sline/object/customer)\n- [variant](/docs/sline/object/variant)\n- [shop](/docs/sline/object/shop)\n- [company](/docs/sline/object/company)\n\n", "examples": [{"path": "/products/holly-jolly-surprise-box", "raw_sline": "{{#var mf = product | get_metafields(\"seo\") /}}\n{{ mf.seo_key.value }}"}], "link": "https://developer.shopline.com/docs/sline/filter/get-metafields", "name": "get_metafields", "return_type": ["metafields"], "summary": "Get the metafield object associated with a specific resource.", "summary_cn": "获取某个资源对应的元字段对象。", "syntax": "obj | get_metafields(namespace)"}, {"arguments": [{"description": "An object containing a product ID. Currently, only the object types listed above are supported.", "description_cn": "含有商品 ID 的 Object 对象，目前仅支持上述列举出来的 Object 类型。", "name": "obj", "types": ["any"]}], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [line_item](/docs/sline/object/line-item)\n- [gift_card](/docs/sline/object/gift-card)\n", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [line_item](/docs/sline/object/line-item)\n- [gift_card](/docs/sline/object/gift-card)\n", "examples": [{"raw_sline": "", "syntax": "{{#var myProduct = gift_card | get_product() /}}\n{{ myProduct.title }}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/get-product", "name": "get_product", "return_type": ["product"], "summary": "Get the specified single product object.", "summary_cn": "获取指定的单个商品对象。", "syntax": "obj | get_product()"}, {"arguments": [{"description": "The specified product object.", "description_cn": "指定商品 Object。", "name": "product", "types": ["any"]}], "array_return_type": [], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [product](/docs/sline/object/product)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [product](/docs/sline/object/product)", "examples": [{"name": "", "path": "/products/-a001-striped-off-shoulder---crop-top-collection-2023", "raw_sline": "{{#var myCollections = product | get_collections() /}}\n{{#for collection in myCollections}}\n  {{ collection.title }}\n{{/for}}"}], "link": "https://developer.shopline.com/docs/sline/filter/get-collections", "name": "get_collections", "return_type": ["array"], "summary": "Get all category objects associated with a specific product.", "summary_cn": "获取某个商品所关联的所有分类对象。", "syntax": "product | get_collections()"}, {"arguments": [{"description": "The specified product object.", "description_cn": "指定商品 Object。", "name": "product", "types": ["any"]}], "array_return_type": [], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [product](/docs/sline/object/product)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [product](/docs/sline/object/product)", "examples": [{"path": "/products/-a001-striped-off-shoulder---crop-top-collection-2023", "raw_sline": "{{#var myVariants = product | get_variants() /}}\n{{#for variant in myVariants}}\n  {{ variant.title }}\n{{/for}}"}], "link": "https://developer.shopline.com/docs/sline/filter/get-variants", "name": "get_variants", "return_type": ["array"], "summary": "Get all variant objects associated with a specific product.", "summary_cn": "获取某个商品下的所有变体对象。", "syntax": "product | get_variants()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}, {"description": "The specified prefix substring.", "description_cn": "指定的前缀子串。", "name": "prefix", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"Fake Two-piece Cardigan\" | starts_with(\"Fake\") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/starts-with", "name": "starts_with", "return_type": "boolean", "summary": "Check whether the string starts with the specified prefix.", "summary_cn": "判断字符串是否包含了指定的前缀子串。", "syntax": "str | starts_with(prefix)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}, {"description": "The specified suffix substring.", "description_cn": "指定的后缀子串。", "name": "suffix", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"Fake Two-piece Cardigan\" | ends_with(\"Cardigan\") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/ends-with", "name": "ends_with", "return_type": "boolean", "summary": "Check whether the string ends with the specified suffix.", "summary_cn": "判断字符串是否包含了指定的后缀子串。", "syntax": "str | ends_with(suffix)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"Vest and Ribbon Tie Dress\" | upcase() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/upcase", "name": "upcase", "return_type": "string", "summary": "Convert all characters in the string to uppercase.", "summary_cn": "将字符串中的所有字符转换为大写。", "syntax": "str | upcase()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}, {"description": "The number of words to retain.", "description_cn": "保留单词的数量。", "name": "limit", "types": ["number"]}], "deprecated": false, "description": "You can also provide `suffix` argument to specify a custom suffix. If you don't want to append any suffix, pass an empty string.", "description_cn": "也可以通过提供 `suffix` 参数来指定自定义后缀。如果不想要附加后缀，则可以提供一个空字符串。", "examples": [{"raw_sline": "{{ \"SHOPLINE will help more merchants sell their products.\" | truncate_words(5)}}\n\n{{ \"SHOPLINE will help more merchants sell their products.\" | truncate_words(5, suffix=\"~\") }}"}], "hashs": [{"description": "The custom suffix to append to the truncated string.", "description_cn": "保留字符串的自定义后缀。", "name": "suffix", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/filter/truncate-words", "name": "truncate_words", "return_type": ["string"], "summary": "Truncate the string based on the specified number of words. If the specified number is less than the total number of words in the string, an ellipsis (...) will be appended to the truncated string.", "summary_cn": "根据指定的单词数量截断字符串内容，如果指定的单词数量小于字符串中的单词数量，则会截断的字符串后附加上省略号后缀(...)。", "syntax": "str | truncate_words(limit, suffix=suffix)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "before: <div>{{ \"\\tFestive Cheer Gift Box\\n\" }}</div>\n\nafter: <div>{{ \"\\t Festive Cheer Gift Box \\n\" | trim_right() }}</div>"}], "link": "https://developer.shopline.com/docs/sline/filter/trim-right", "name": "trim_right", "return_type": "string", "summary": "Trims all leading whitespace characters(spaces, newline characters and tabs) from the right side of a string, returning the cleaned string.", "summary_cn": "去除字符串右侧的空白字符（包括空格、换行符和制表符），返回处理后的文本。", "syntax": "str | trim_right()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}, {"description": "The length of the string to retain.", "description_cn": "需要保留字符串的长度。", "name": "limit", "types": ["number"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"Vest and Ribbon Tie Dress\" | truncate(10) }}\n\n{{ \"Vest and Ribbon Tie Dress\" | truncate(10, suffix=\"~\") }}"}], "hashs": [{"description": "The custom suffix to append to the truncated string.", "description_cn": "保留字符串的自定义后缀。", "name": "suffix", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/filter/truncate", "name": "truncate", "return_type": "string", "summary": "Truncates the string to a specified maximum length and appends a custom suffix when truncation occurs.", "summary_cn": "根据指定的单词数量截断字符串内容，并在截断后追加可选的后缀（默认是 \"...\"）。", "syntax": "str | truncate(limit, suffix=suffix)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "before: <div>{{ \"\\tFestive Cheer Gift Box\\n\" }}</div>\n\nafter: <div>{{ \"\\t Festive Cheer Gift Box \\n\" | trim_left() }}</div>"}], "link": "https://developer.shopline.com/docs/sline/filter/trim-left", "name": "trim_left", "return_type": "string", "summary": "Trims all leading whitespace characters(spaces, newline characters and tabs) from the left side of a string, returning the cleaned string.", "summary_cn": "去除字符串左侧的空白字符（包括空格、换行符和制表符），返回处理后的文本。", "syntax": "str | trim_left()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "before: <div>{{ \"\\tFestive Cheer Gift Box\\n\" }}</div>\n\nafter: <div>{{ \"\\t Festive Cheer Gift Box \\n\" | trim() }}</div>"}], "link": "https://developer.shopline.com/docs/sline/filter/trim", "name": "trim", "return_type": "string", "summary": "Trims all leading and trailing whitespace characters(spaces, newline characters and tabs), returning the cleaned string.", "summary_cn": "去除字符串两端的空白字符（包括空格、换行符和制表符），返回处理后的文本。", "syntax": "str | trim()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"First Line;\\nSecond Line;\\nThird Line;\" | strip_newlines() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/strip-newlines", "name": "strip_newlines", "return_type": "string", "summary": "Strips all newline characters from the string and return a string without line breaks.", "summary_cn": "去除字符串中所有的换行符，返回一个不含换行的字符串。", "syntax": "str | strip_newlines()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"<h1>title</h1>\\n<div>description</div>\" | strip_html() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/strip-html", "name": "strip_html", "return_type": "string", "summary": "Strips all HTML tags from the string and return a plain text string.", "summary_cn": "去除字符串中所有的 HTML 标签，返回一个纯文本字符串。", "syntax": "str | strip_html()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}, {"description": "The separator.", "description_cn": "分割符。", "name": "separator", "types": ["string"]}], "array_return_type": ["string"], "deprecated": false, "examples": [{"raw_sline": "{{#var words = \"Festive Cheer Gift Box\" | split(\" \") /}}\n\n{{#for word in words}}\n    word: {{ word }}\n{{/for}}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/split", "name": "split", "return_type": [], "summary": "Split the string using the specified delimiter and return an array of substrings.", "summary_cn": "使用指定的分隔符对字符串进行分割，返回一个字符串数组。", "syntax": "str | split(separator)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{{ \"<p>Love & Peace</p>\" | url_param_escape() }}}"}], "link": "https://developer.shopline.com/docs/sline/filter/url-param-escape", "name": "url_param_escape", "return_type": ["string"], "summary": "Escape URL-unsafe characters in the string for use in URL parameters.", "summary_cn": "对字符串中 URL 参数不安全的字符进行转义处理。", "syntax": "str | url_param_escape()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"vicky%40qq.com\" | url_decode() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/url-decode", "name": "url_decode", "return_type": ["string"], "summary": "Convert percent-encoded characters in the string back to their corresponding URL characters.", "summary_cn": "将字符串中的 % 字符转换为 URL 字符。", "syntax": "str | url_decode()"}, {"arguments": [{"description": "The original string or array.", "description_cn": "原始字符串或数组。", "name": "list", "types": ["string", "array"]}, {"description": "Starting index of the extraction.", "description_cn": "截取起始索引位置。", "name": "start", "types": ["number"]}], "array_return_type": [], "deprecated": false, "description": "By default, only one character or array item is extracted. However, you can specify the length of the substring or subarray by providing the `len` parameter.", "description_cn": "默认情况下，只会截取一个字符或数组项。不过，也可以通过提供 `len` 参数来指定截取子字符串或子数组的长度。", "examples": [{"path": "/products/festive-cheer-gift-box", "raw_sline": "{{ product.title }}\n\n{{ product.title | slice(1, len=9) }}\n\n", "source_object": "product"}], "hashs": [{"description": "Length of extraction.", "description_cn": "截取长度。", "name": "len", "types": ["number"]}], "link": "https://developer.shopline.com/docs/sline/filter/slice", "name": "slice", "return_type": ["string", "array"], "summary": "Extract a substring or subarray starting from the specified index.", "summary_cn": "从指定索引位置开始截取子字符串或子数组。", "syntax": "list | slice(start, len=len)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "description": "Note: Spaces in the string will be converted to `+` instead of percent-encoded characters.", "description_cn": "注意：字符串中的空格会被转成 `+` 号，而不是带 % 编码的字符。", "examples": [{"description": "", "description_cn": "", "raw_sline": "{{{ \"<EMAIL>\" | url_encode() }}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/url-encode", "name": "url_encode", "return_type": ["string"], "summary": "Convert any URL-unsafe characters in the string into percent-encoded characters.", "summary_cn": "将字符串中的任何 URL 不安全的字符转换为带有 % 编码的字符。", "syntax": "str | url_encode()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "str", "types": ["string"]}], "deprecated": false, "description": "", "examples": [{"description": "", "name": "", "path": "/", "raw_sline": "{{{ \"<p>Love & Peace</p>\" | url_escape() }}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/url-escape", "name": "url_escape", "return_type": ["string"], "summary": "Escape URL-unsafe characters in the string.", "summary_cn": "对字符串中 URL 不安全的字符进行转义处理。", "syntax": "str | url_escape()"}, {"arguments": [{"description": "The filepath.", "description_cn": "文件路径。", "name": "filepath", "types": ["string"]}], "deprecated": false, "description": "- Absolute path: The resource to be referenced is at `/public/a.js`\n```text\n{{ \"a.js\" | asset_url() }}\n```\n\n\n- Relative path: If the file containing the expression is located at `sections/test/two/two.html`, and the resource to be referenced is at `sections/test/one/test.js`\n```text\n{{ \"../one/test.js\"| asset_url() }}\n```", "description_cn": "- 绝对路径：需要引用的资源路径 `/public/a.js`\n```text\n{{ \"a.js\" | asset_url() }}\n```\n\n\n- 相对路径：表达式所在的文件路径为 `sections/test/two/two.html`，需要引用的资源路径 `sections/test/one/test.js`\n```text\n{{ \"../one/test.js\"| asset_url() }}\n```", "examples": [], "link": "https://developer.shopline.com/docs/sline/filter/asset-url", "name": "asset_url", "return_type": ["string"], "summary": "Returns the CDN URL of the theme's static resources, supporting both absolute and relative paths.", "summary_cn": "返回主题静态资源的文件 CDN 地址，支持绝对路径和相对路径。", "syntax": "filepath | asset_url()"}, {"arguments": [{"description": "The filename.", "description_cn": "文件名。", "name": "filename", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{{ \"1691570437909.jpg\" | file_img_url() }}}\n\n{{{ \"1691570437909.jpg\" | file_img_url(size=\"100x\") }}}"}], "hashs": [{"description": "The size, in the format: 100x, 210x.", "description_cn": "尺寸, 格式为: 100x、210x。", "name": "size", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/filter/file-img-url", "name": "file_img_url", "return_type": ["string"], "summary": "Returns the CDN URL of an image from the Shopline admin's file page.", "summary_cn": "从 Shopline 后台的文件页面返回图像的CDN URL 。", "syntax": "filename | file_img_url(size=size)"}, {"arguments": [{"description": "The filename.", "description_cn": "文件名。", "name": "filename", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{{ \"1691570437909.jpg\" | file_url() }}}"}], "link": "https://developer.shopline.com/docs/sline/filter/file-url", "name": "file_url", "return_type": ["string"], "summary": "Returns the CDN URL of a file from the Shopline admin's file library.", "summary_cn": "从 Shopline 后台的文件库返回文件的 CDN URL 。", "syntax": "filename | file_url()"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "string", "types": ["string"]}, {"description": "Substring to be replaced.", "description_cn": "需要被替换的子串。", "name": "old", "types": ["string"]}, {"description": "New replacement substring.", "description_cn": "替换后的新子串。", "name": "new", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"a,b,c\" | replace_first(\",\", \"-\") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/replace-first", "name": "replace_first", "return_type": "string", "summary": "Replaces the first occurrence of a substring in a string.", "summary_cn": "替换字符串中第一个匹配的子串。", "syntax": "string | replace_first(old, new)"}, {"arguments": [{"description": "A font object.", "description_cn": "font对象。", "name": "font", "types": ["font"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ settings.sort_title_font | font_url() }}\n\n{{ settings.sort_title_font | font_url(format=\"woff\") }}"}], "hashs": [{"description": "Font file format (`woff2` or `woff`), default `woff2`.", "description_cn": "字体文件格式（`woff2` 或 `woff`），默认 `woff2`。", "name": "format", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/filter/font-url", "name": "font_url", "return_type": ["string"], "summary": "Returns the CDN URL for a font file in the specified format (`woff2` or `woff`).", "summary_cn": "返回传入字体对象指定格式（`woff2` 或 `woff`）的 CDN 地址。", "syntax": "font | font_url(format=format)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "string", "types": ["string"]}, {"description": "Substring to be replaced.", "description_cn": "需要被替换的子串。", "name": "old", "types": ["string"]}, {"description": "New replacement substring.", "description_cn": "替换后的新子串。", "name": "new", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ \"a,b,c\" | replace(\",\", \"-\") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/replace", "name": "replace", "return_type": "string", "summary": "Replaces all occurrences of a substring in a string.", "summary_cn": "替换字符串中的所有匹配子串。", "syntax": "string | replace(old, new)"}, {"arguments": [{"description": "A font object.", "description_cn": "font对象。", "name": "font", "types": ["font"]}, {"description": "Property to modify.", "description_cn": "要修改的属性。", "name": "key", "types": ["string"]}, {"description": "New value for the property.", "description_cn": "新的属性值。", "name": "value", "types": ["string"]}], "deprecated": false, "description": "Supported Properties & Values\n\n| **Property** | **Valid Values** | **Description** |  \n|-------------|----------------|----------------|  \n| **`style`** | `\"normal\"` | Converts the font to its regular (non-italic) variant. |  \n|  | `\"italic\"` | Converts the font to its italic variant (if available). |  \n|  | `\"oblique\"` | Converts the font to its oblique variant (Shopify fonts typically offer either italic or oblique, not both). |  \n| **`weight`** | `100`–`900` (in 100-unit steps) | Adjusts the font weight to the specified value (e.g., `400` for normal, `700` for bold). |  \n|  | `\"normal\"` | Shortcut for `weight: 400`. |  \n|  | `\"bold\"` | Shortcut for `weight: 700`. |  \n|  | `\"+/-N\"` (e.g., `\"+200\"`) | Increases/decreases the current weight by `N` (e.g., `400` → `600` with `\"+200\"`). |  \n|  | `\"lighter\"` | Reduces weight using CSS fallback rules (e.g., `700` → `400`). |  \n|  | `\"bolder\"` | Increases weight using CSS fallback rules (e.g., `400` → `700`). |  \n", "description_cn": "| **属性** | **有效值** | **说明** |  \n|---------|-----------|---------|  \n| **`style`** | `\"normal\"` | 将字体转换为常规（非斜体）变体。 |  \n|  | `\"italic\"` | 将字体转换为斜体变体（如果可用）。 |  \n|  | `\"oblique\"` | 将字体转换为倾斜体变体（Shopify 字体通常仅支持斜体或倾斜体之一）。 |  \n| **`weight`** | `100`–`900`（以100为间隔） | 调整字重至指定值（如 `400` 为常规，`700` 为加粗）。 |  \n|  | `\"normal\"` | `weight: 400` 的简写。 |  \n|  | `\"bold\"` | `weight: 700` 的简写。 |  \n|  | `\"+/-N\"`（如 `\"+200\"`） | 在当前字重上增加/减少 `N`（如 `400` → `600`）。 |  \n|  | `\"lighter\"` | 根据 CSS 规则降低字重（如 `700` → `400`）。 |  \n|  | `\"bolder\"` | 根据 CSS 规则提高字重（如 `400` → `700`）。 |  \n", "examples": [{"path": "/", "raw_sline": "Original weight: {{ settings.sort_title_font.weight }}\n\n{{#var lighterFont = settings.sort_title_font | font_modify(\"weight\", \"lighter\") /}}\n\nh2 {\n  font-weight: {{ lighterFont.weight }};\n}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/font-modify", "name": "font_modify", "return_type": ["string"], "summary": "Modifies a specific property of a font object and returns a new font object.", "summary_cn": "修改字体对象的指定属性，并返回新的字体对象。", "syntax": "font | font_modify(key, value)"}, {"arguments": [{"description": "A font object.", "description_cn": "font对象。", "name": "font", "types": ["font"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{{ settings.sort_title_font | font_face() }}}"}], "link": "https://developer.shopline.com/docs/sline/filter/font-face", "name": "font_face", "return_type": ["string"], "summary": "Generates a CSS `@font-face` declaration for the given font object.", "summary_cn": "为传入的字体对象生成 CSS `@font-face` 声明。", "syntax": "font | font_face()"}, {"arguments": [{"description": "Style picker setting.", "description_cn": "style相关控件的值。", "name": "setting", "types": ["any"]}], "deprecated": false, "description": "Supports passing either a single style picker setting or the entire settings object:\n\n- Style Picker Setting: Returns the class list corresponding to the current style setting, e.g., `{{ block.settings.layout | class_list() }}`\n\n- Settings object: Returns the class list for all style picker in the settings, e.g., `{{ block.settings | class_list() }}`", "description_cn": "支持传入单个style控件配置或整个settings对象：\n\n- 单个style配置：返回当前style控件对应的样式列表，如 `{{ block.settings.layout | class_list() }}`\n\n- settings对象：返回settings中所有style控件的样式列表，如 `{{ block.settings | class_list() }}`", "examples": [{"description": "\n```text\n<div class=\"product-detail__info {{block.settings.layout | class_list()}}\">\n  ...\n</div>\n\n<div class=\"product-card {{block.settings | class_list()}}\">\n  ...\n</div>\n{{#schema}}\n{\n  \"name\": \"Demo\",\n  \"tag\": \"\",\n  \"settings\": [\n    {\n      \"type\": \"style.layout\",\n      \"id\": \"layout\",\n      \"default\": {\n        \"flex-wrap\": \"nowrap\",\n        \"flex-direction\": \"column\",\n        \"row-gap\": \"20px\",\n        \"column-gap\": \"20px\"\n      }\n    },\n    {\n      \"type\": \"style.spacing\",\n      \"id\": \"spacing\",\n      \"default\": {\n        \"padding-top\": \"40px\",\n        \"padding-right\": \"0px\",\n        \"padding-bottom\": \"40px\",\n        \"padding-left\": \"0px\",\n        \"@media (--mobile)\": {\n          \"padding-top\": \"0px\",\n          \"padding-bottom\": \"0px\"\n        }\n      }\n    }\n  ]\n}\n```", "description_cn": "\n```text\n<div class=\"product-detail__info {{block.settings.layout | class_list()}}\">\n  ...\n</div>\n\n<div class=\"product-card {{block.settings | class_list()}}\">\n  ...\n</div>\n\n{{#schema}}\n{\n  \"name\": \"Demo\",\n  \"tag\": \"\",\n  \"settings\": [\n    {\n      \"type\": \"style.layout\",\n      \"id\": \"layout\",\n      \"default\": {\n        \"flex-wrap\": \"nowrap\",\n        \"flex-direction\": \"column\",\n        \"row-gap\": \"20px\",\n        \"column-gap\": \"20px\"\n      }\n    },\n    {\n      \"type\": \"style.spacing\",\n      \"id\": \"spacing\",\n      \"default\": {\n        \"padding-top\": \"40px\",\n        \"padding-right\": \"0px\",\n        \"padding-bottom\": \"40px\",\n        \"padding-left\": \"0px\",\n        \"@media (--mobile)\": {\n          \"padding-top\": \"0px\",\n          \"padding-bottom\": \"0px\"\n        }\n      }\n    }\n  ]\n}\n```"}], "link": "https://developer.shopline.com/docs/sline/filter/class-list", "name": "class_list", "return_type": ["string"], "summary": "Generates CSS class names based on style picker setting.", "summary_cn": "与style相关控件配合使用，输出对应的类名列表。", "syntax": "setting | class_list()"}, {"arguments": [{"description": "I18N file key.", "description_cn": "多语言文件中的翻译键。", "name": "key", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"products.general.sold_out\" | t() }}\n\n{{ \"products.product_details.inventory_low_stock_show_count\" | t(quantity=10) }}"}], "hashs": [{"description": "Replaces dynamic placeholders in multilingual text.", "description_cn": "用于替换多语言文本中的动态占位符。", "name": "...props", "types": ["any"]}], "link": "https://developer.shopline.com/docs/sline/filter/t", "name": "t", "return_type": ["string"], "summary": "Returns a string of translated text for a given translation key from a `i18n` file.", "summary_cn": "从多语言文件中返回给定翻译键的已翻译文本字符串。", "syntax": "key | t()"}, {"arguments": [{"description": "Date string in ISO8601 format.", "description_cn": "ISO8601 格式的日期字符串。", "name": "date_string", "types": ["string"]}], "deprecated": false, "description": "Built-in date format types:\n- default\n- abbreviated_date\n- basic\n- date\n- date_at_time\n- on_date\n\nCustom date formats can be defined using placeholders. Reference: https://www.strfti.me/", "description_cn": "内置日期格式化类型：\n- default\n- abbreviated_date\n- basic\n- date\n- date_at_time\n- on_date\n\n可使用占位符自定义日期输出的格式，可参考：https://www.strfti.me/", "examples": [{"path": "/", "raw_sline": "{{ \"2022-04-14 16:56:02 -0400\" | date() }}\n\n{{ \"2022-04-14 16:56:02 -0400\" | date(format=\"basic\") }}\n\n{{ \"2022-04-14 16:56:02 -0400\" | date(format=\"%B %d, %Y\")  }}"}], "hashs": [{"description": "Output format (supports built-in types or custom placeholders).", "description_cn": "输出格式（支持内置类型或自定义占位符）。", "name": "format", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/filter/date", "name": "date", "return_type": ["string"], "summary": "Formats a date string into the specified pattern.", "summary_cn": "将日期字符串格式化为指定格式。", "syntax": "date_string | date(format=format)"}, {"arguments": [{"description": "Value to serialize.", "description_cn": "需要序列化的值。", "name": "value", "types": ["any"]}], "deprecated": false, "description": "Note: `{{ ... }}` will HTML-escape the output, to output raw unescaped content, use `{{{ ... }}}`.", "description_cn": "注意：`{{ ... }}` 会对输出结果进行html转义，如需原样输出可使用 `{{{ ... }}}`。", "examples": [{"path": "/products/product-009-92883-19", "raw_sline": "{{{ product.tags | json() }}}"}], "link": "https://developer.shopline.com/docs/sline/filter/json", "name": "json", "return_type": ["string"], "summary": "Converts a value to a JSON-formatted string.", "summary_cn": "将值转换为符合JSON格式的字符串。", "syntax": "value | json()"}, {"arguments": [{"description": "Fallback value (returned if value is empty).", "description_cn": "默认值（当 value 为空时返回）。", "name": "default", "types": ["any"]}, {"description": "Value to check.", "description_cn": "要检查的值。", "name": "value", "types": ["any"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "1. {{ \"\" | default(\"<PERSON>\") }}\n\n2. {{ \"\" | default(\"<PERSON>\", allow_empty_str=true) }}\n\n3. {{ false | default(true) }}\n\n4. {{ false | default(true, allow_false=true) }}"}], "hashs": [{"description": "Treat `false` as valid (default `false`).", "description_cn": "是否视 `false` 为有效值（默认为 `false`）。", "name": "allow_false", "types": ["boolean"]}, {"description": "Treat empty string `\"\"` as valid (default `false`).", "description_cn": "是否视空字符串 `\"\"` 为有效值（默认为 `false`）。", "name": "allow_empty_str", "types": ["boolean"]}], "link": "https://developer.shopline.com/docs/sline/filter/default", "name": "default", "return_type": ["any"], "summary": "Provides a default value for `nil`, `false`, or `empty strings`.", "summary_cn": "为 `nil`、`false` 或 `空字符串` 提供默认值", "syntax": "value | default(default, allow_empty_str=bool, allow_false=bool)"}, {"arguments": [{"description": "Original string.", "description_cn": "原始字符串。", "name": "string", "types": ["string"]}, {"description": "Substring to remove.", "description_cn": "要移除的子串。", "name": "substr", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ \"hello hello world\" | remove_first(\"hello \") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/remove-first", "name": "remove_first", "return_type": "string", "summary": "Removes the first occurrence of a substring from a string.", "summary_cn": "移除字符串中第一次出现的指定子串。", "syntax": "string | remove_first(substr)"}, {"arguments": [{"description": "Number to limit.", "description_cn": "要限制的数字。", "name": "number", "types": ["number"]}, {"description": "Minimum allowed value.", "description_cn": "允许的最小值。", "name": "min", "types": ["number"]}], "deprecated": false, "description": "Returns the original number if it is greater than or equal to the minimum, otherwise returns the minimum.", "description_cn": "若输入数字大于等于最小值，返回原数字；否则返回最小值。", "examples": [{"path": "/", "raw_sline": "{{ 3 | at_least(4) }}\n{{ 5 | at_least(4) }}"}], "link": "https://developer.shopline.com/docs/sline/filter/at-least", "name": "at_least", "return_type": ["number"], "summary": "Ensures a number is not less than the specified minimum.", "summary_cn": "确保数字不小于指定的最小值。", "syntax": "number | at_least(min)"}, {"arguments": [{"description": "Number to limit", "description_cn": "要限制的数字。", "name": "number", "types": ["number"]}, {"description": "Maximum allowed value.", "description_cn": "允许的最大值。", "name": "max", "types": ["number"]}], "deprecated": false, "description": "Returns the original number if it is less than or equal to the maximum, otherwise returns the maximum.", "description_cn": "若输入数字小于等于最大值，返回原数字；否则返回最大值。", "examples": [{"path": "/", "raw_sline": "{{ 3 | at_most(4) }}\n{{ 5 | at_most(4) }}"}], "link": "https://developer.shopline.com/docs/sline/filter/at-most", "name": "at_most", "return_type": ["number"], "summary": "Limits a number to a specified maximum value.", "summary_cn": "限制数字不超过指定的最大值。", "syntax": "number | at_most(max)"}, {"arguments": [{"description": "Number to process.", "description_cn": "要处理的数字。", "name": "number", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ 1.35 | round() }}\n\n{{ 1.35 | round(fixed=1) }}"}], "hashs": [{"description": "Decimal places to keep (default: 0).", "description_cn": "保留的小数位数，默认为0。", "name": "fixed", "types": ["number"]}], "link": "https://developer.shopline.com/docs/sline/filter/round", "name": "round", "return_type": ["number"], "summary": "Rounds a number to specified decimal places.", "summary_cn": "将数字四舍五入到指定小数位。", "syntax": "number | round(fixed=fixed)"}, {"arguments": [{"description": "Number to process.", "description_cn": "要处理的数字。", "name": "number", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ 4.3 | floor() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/floor", "name": "floor", "return_type": ["number"], "summary": "Rounds a number down to the nearest integer.", "summary_cn": "将数字向下取整到最接近的整数。", "syntax": "number | floor()"}, {"arguments": [{"description": "Number to process.", "description_cn": "要处理的数字。", "name": "number", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ 4.3 | ceil() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/ceil", "name": "ceil", "return_type": ["number"], "summary": "Rounds a number up to the nearest integer.", "summary_cn": "将数字向上取整到最接近的整数。", "syntax": "number | ceil()"}, {"arguments": [{"description": "Number to compute absolute value.", "description_cn": "要计算绝对值的数字。", "name": "number", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ -1 | abs() }}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/abs", "name": "abs", "return_type": ["number"], "summary": "Computes the absolute value of a number.", "summary_cn": "计算数字的绝对值。", "syntax": "number | abs()"}, {"arguments": [{"description": "Dividend.", "description_cn": "被除数。", "name": "number", "types": ["number"]}, {"description": "Divisor (non-zero number).", "description_cn": "除数（非零数字）。", "name": "divisor", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ 10 | modulo(3) }}"}], "link": "https://developer.shopline.com/docs/sline/filter/modulo", "name": "modulo", "return_type": ["number"], "summary": "Computes the remainder of division between two numbers.", "summary_cn": "计算两数相除的余数。", "syntax": "number | modulo(divisor)"}, {"arguments": [{"description": "Number to be divided.", "description_cn": "被除数。", "name": "number", "types": ["number"]}, {"description": "Number to divide by (non-zero).", "description_cn": " 除数（不可为零）。", "name": "divisor", "types": ["number"]}], "deprecated": false, "description": "Returns floored integer when both operands are integers, float when either operand is float. Use `integer` parameter to force specific return type.", "description_cn": "当两数均为整数时，默认返回向下取整的整数；若任一数为浮点数，则返回浮点数结果。可通过 `integer` 参数强制指定返回类型。", "examples": [{"path": "/", "raw_sline": "{{ 10 | divided_by(4) }}\n\n{{ 10 | divided_by(4, integer=false) }}\n\n{{ 10 | divided_by(3.1) }}\n\n{{ 10 | divided_by(3.1, integer=true) }}"}], "hashs": [{"description": "Force integer result.", "description_cn": "强制返回整数结果。", "name": "integer", "types": ["boolean"]}], "link": "https://developer.shopline.com/docs/sline/filter/divided-by", "name": "divided_by", "return_type": ["number"], "summary": "Performs numeric division.", "summary_cn": "数字除法运算。", "syntax": "number | divided_by(divisor, integer=integer)"}, {"arguments": [{"description": "Number to be multiplied.", "description_cn": "被乘数。", "name": "number", "types": ["number"]}, {"description": "Number to multiply by.", "description_cn": "乘数。", "name": "multiplier", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ 2 | times(5) }}"}], "link": "https://developer.shopline.com/docs/sline/filter/times", "name": "times", "return_type": ["number"], "summary": "Performs numeric multiplication.", "summary_cn": "数字乘法运算。", "syntax": "number | times(multiplier)"}, {"arguments": [{"description": "Number to subtract from.", "description_cn": "被减数。", "name": "number", "types": ["number"]}, {"description": "Number to subtract.", "description_cn": "减数。", "name": "subtrahend", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ 5 | minus(3) }}"}], "link": "https://developer.shopline.com/docs/sline/filter/minus", "name": "minus", "return_type": ["number"], "summary": "Performs numeric subtraction.", "summary_cn": "数字减法运算。", "syntax": "number | minus(subtrahend)"}, {"arguments": [{"description": "Number to be added to.", "description_cn": "被加数。", "name": "number", "types": ["number"]}, {"description": "Number to add.", "description_cn": "加数。", "name": "addend", "types": ["number"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ 1 | plus(1) }}"}], "link": "https://developer.shopline.com/docs/sline/filter/plus", "name": "plus", "return_type": ["number"], "summary": "Performs numeric addition.", "summary_cn": "数字加法运算。", "syntax": "number | plus(addend)"}, {"arguments": [{"description": "The original array.", "description_cn": "原数组。", "name": "array", "types": ["array"]}], "array_return_type": [], "deprecated": false, "examples": [{"name": "string array", "name_cn": "字符串数组", "path": "/", "raw_sline": "{{#var arr = \"c,a,b\" | split(\",\") /}}\nOriginal Array: {{{ arr | json() }}}\nSorted Array: {{{ arr | sort() | json() }}}\n", "syntax": ""}, {"name": "Object arrays", "name_cn": "对象数组", "path": "/collections/dress", "raw_sline": "{{#var paginate = collection | get_product_pagination(5) /}}\n\nAll Products:\n{{#for item in paginate.list}}\n    - title:{{item.title}}\n    - price: {{item.price}}\n{{/for}}\n\n\n{{#var sortedProducts = paginate.list | sort(by=\"price\") /}}\n\nSorted Products:\n{{#for item in sortedProducts}}\n    - title:{{item.title}}\n    - price: {{item.price}}\n{{/for}}"}], "hashs": [{"description": "Property key for sorting object arrays.", "description_cn": "对象数组排序时使用的属性键名。", "name": "by", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/filter/sort", "name": "sort", "return_type": ["array"], "summary": "Sorts an array.", "summary_cn": "对数组进行排序。", "syntax": "array | sort(by=key)\n"}, {"arguments": [{"description": "The original string.", "description_cn": "原字符串。", "name": "string", "types": ["string"]}, {"description": "The substring to remove.", "description_cn": "需要移除的子串。", "name": "substr", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ \"a-b-c\" | remove(\"-\") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/remove", "name": "remove", "return_type": "string", "summary": "Removes all occurrences of a substring from a string.", "summary_cn": "移除字符串中所有匹配的子串。", "syntax": "string | remove(substr)"}, {"arguments": [{"description": "The original array.", "description_cn": "原数组。", "name": "array", "types": ["array"]}], "array_return_type": [], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{#var arr = \"a,b,c\" | split(\",\") /}}\nOriginal Array: {{{ arr | json() }}}\nReversed Array: {{{ arr | reverse() | json() }}}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/reverse", "name": "reverse", "return_type": ["array"], "summary": "Reverses the order of elements in an array and returns a new array.", "summary_cn": "反转数组中元素的顺序，返回新数组。", "syntax": "array | reverse()"}, {"arguments": [{"description": "The original array.", "description_cn": "原数组。", "name": "array", "types": ["array"]}], "array_return_type": [], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{#var arr = \"a,b,a,c\" | split(\",\") /}}\nOriginal Array: {{{ arr | json() }}}\nUniq Array: {{{ arr | uniq() | json() }}}"}], "link": "https://developer.shopline.com/docs/sline/filter/uniq", "name": "uniq", "return_type": ["array"], "summary": "Removes duplicate elements from an array, returning a new array with unique values.", "summary_cn": "去除数组中的重复元素，返回唯一值组成的新数组。", "syntax": "array | uniq()"}, {"arguments": [{"description": "The original string.", "description_cn": "原始字符串。", "name": "string", "types": ["string"]}, {"description": "The string to prepend.", "description_cn": "要插入到开头的字符串。", "name": "prefix", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/", "raw_sline": "{{ \"world\" | prepend(\"hello \") }}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/prepend", "name": "prepend", "return_type": "string", "summary": "Inserts specified content at the beginning of a string.", "summary_cn": "在字符串开头插入指定内容。", "syntax": "string | prepend(prefix)"}, {"arguments": [{"description": "The original array, elements should be objects.", "description_cn": "原数组，数组元素应为对象。", "name": "array", "types": ["array"]}, {"description": "Property key to be checked in each object.", "description_cn": "需要检测的对象属性键名。", "name": "key", "types": ["string"]}], "array_return_type": [], "deprecated": false, "examples": [{"path": "/collections/dress", "raw_sline": "{{#var paginate = collection|get_product_pagination(5) /}}\n\nAll Products:\n{{#for item in paginate.list}}\n    - vendor: {{item.vendor}}\n    - title:{{item.title}}\n{{/for}}\n\n\n{{#var filterProducts = paginate.list|where(\"vendor\", equals=\"Brand 3\") /}}\n\nFilter Products:\n{{#for item in filterProducts}}\n    - vendor: {{item.vendor}}\n    - title:{{item.title}}\n{{/for}}", "source_object": "", "syntax": ""}, {"description_cn": "您可以过滤具有真值的属性值的项目，这要求您仅提供属性名称。", "name": "Filter properties with truthy values", "name_cn": "过滤具有真值的属性", "path": "/collections/dress", "raw_sline": "{{#var paginate = collection|get_product_pagination(5) /}}\n\nAll Products:\n{{#for item in paginate.list}}\n    - vendor: {{item.vendor}}\n    - title:{{item.title}}\n{{/for}}\n\n\n{{#var filterProducts = paginate.list|where(\"vendor\") /}}\n\nFilter Products:\n{{#for item in filterProducts}}\n    - vendor: {{item.vendor}}\n    - title:{{item.title}}\n{{/for}}"}], "hashs": [{"description": "Reference value for exact matching mode, Activates truthy-check mode when omitted.", "description_cn": "启用精确匹配模式时作为比对基准的值，未提供时自动切换至真值检测模式。", "name": "equals", "types": ["any"]}], "link": "https://developer.shopline.com/docs/sline/filter/where", "name": "where", "return_type": ["array"], "summary": "Filters an array based on object property values, returning a new array with matching elements. Supports both exact matching and truthy-value detection modes.", "summary_cn": "基于对象属性值对数组进行筛选，返回符合条件的新数组。支持精确匹配或真值检测两种模式。", "syntax": "array | where(key, equals=value)"}, {"arguments": [{"description": "The array to be extracted.", "description_cn": "被提取的数组。", "name": "array", "types": ["array"]}, {"description": "The properties of the objects in the array.", "description_cn": "数组中对象的属性。", "name": "key", "types": ["string"]}], "array_return_type": [], "deprecated": false, "examples": [{"path": "/products/product-009-92883-19", "raw_sline": "{{ product.images | map(\"src\") }}", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/filter/map", "name": "map", "return_type": ["array"], "summary": "Creates an array of values ​​based on specific properties of the items in the array.", "summary_cn": "根据数组中项目的特定属性创建值数组。", "syntax": "array | map(key)"}, {"arguments": [{"description": "Decide whether to use singular or plural numbers.", "description_cn": "决定使用单数还是复数的数字。", "name": "number", "types": ["number"]}, {"description": "A singular string.", "description_cn": "单数形式的字符串。", "name": "singular", "types": ["string"]}, {"description": "String containing the plural form.", "description_cn": "复数形式的字符串。", "name": "plural", "types": ["string"]}], "array_return_type": [], "deprecated": false, "examples": [{"raw_sline": "{{ 1 | pluralize(\"singular\", \"plural\") }}\n{{ 2 | pluralize(\"singular\", \"plural\") }}"}], "link": "https://developer.shopline.com/docs/sline/filter/pluralize", "name": "pluralize", "return_type": ["string"], "summary": "Outputs the singular or plural form of a string, depending on the given number.", "summary_cn": "根据给定的数字，输出字符串的单数形式或复数形式。", "syntax": "number | pluralize(singular, plural)"}, {"arguments": [{"description": "The array to be converted.", "description_cn": "被转换的数组。", "name": "array", "types": ["array"]}, {"description": "The string to concatenate between each element.", "description_cn": "每个元素之间连接的字符串。", "name": "separator", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{#var arr = \"one,two,three\" | split(\",\") /}}\n\n{{ arr | join(\"__\") }}\n"}], "hashs": [], "link": "https://developer.shopline.com/docs/sline/filter/join", "name": "join", "return_type": ["string"], "summary": "Combines all elements in an array into a single string using the specified separator.", "summary_cn": "将一个数组中的所有元素使用指定分隔符合并成一个字符串。", "syntax": "array | join(separator)"}, {"arguments": [{"description": "The destination array to merge.", "description_cn": "合并的目标数组。", "name": "array1", "types": ["array"]}, {"description": "The array to be merged.", "description_cn": "被合并的数组。", "name": "array2", "types": ["array"]}], "array_return_type": ["any"], "deprecated": false, "examples": [{"raw_sline": "{{#var arr1 = \"one,two,three\" | split(\",\") /}}\n{{#var arr2 = \"four,five\" | split(\",\") /}}\n\n{{ arr1 | concat(arr2) }}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/concat", "name": "concat", "return_type": ["array"], "summary": "Merges two arrays and returns a new array.", "summary_cn": "合并两个数组，返回一个新数组。", "syntax": "array1 | concat(array2)"}, {"arguments": [{"description": "Get the length of the variable.", "description_cn": "获取长度的变量。", "name": "variable", "types": ["array", "string"]}], "deprecated": false, "examples": [{"raw_sline": "{{#var arr = \"one,two,three\" | split(\",\") /}}\n\n{{ arr | size() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/size", "name": "size", "return_type": ["number"], "summary": "Returns the size of a string or array.\n\nThe size of a string is the number of characters the string contains. The size of an array is the number of items in the array.", "summary_cn": "返回字符串或数组的大小。\n\n字符串的大小是字符串包含的字符数。数组的大小是数组中的项数。", "syntax": "variable | size()"}, {"arguments": [{"description": "The target array.\n\n", "description_cn": "目标数组。\n\n", "name": "array", "types": ["array"]}], "deprecated": false, "examples": [{"raw_sline": "{{#var arr = \"one,two,three\" | split(\",\") /}}\n\n{{ arr | last() }}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/last", "name": "last", "return_type": ["any"], "summary": "Returns the last item in an array.", "summary_cn": "返回一个数组中的最后一个项目。", "syntax": "array | last()"}, {"arguments": [{"description": "The target array.", "description_cn": "目标数组。", "name": "array", "types": ["array"]}], "deprecated": false, "examples": [{"path": "", "raw_sline": "{{#var arr = \"one,two,three\" | split(\",\") /}}\n\n{{ arr | first() }}", "source_object": ""}], "link": "https://developer.shopline.com/docs/sline/filter/first", "name": "first", "return_type": ["any"], "summary": "Returns the first element in an array.", "summary_cn": "返回数组中的第一个元素。", "syntax": "array | first()"}, {"arguments": [{"description": "The variable to check.", "description_cn": "检查的变量。", "name": "variable", "types": ["string", "array"]}, {"description": "The value to check.", "description_cn": "检查的值。", "name": "value", "types": ["string", "boolean", "number"]}], "deprecated": false, "examples": [{"raw_sline": "{{#var str = \"hello word!\" /}}\n\n{{ str | contains(\"hello\") }}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/contains", "name": "contains", "return_type": ["boolean"], "summary": "Determines whether a string contains a substring or an array contains a subelement.", "summary_cn": "判断字符串包含子串或数组包含子元素.", "syntax": "variable | contains(value)"}, {"arguments": [{"description": "The value to be converted.", "description_cn": "被转换的值。", "name": "string", "types": ["string"]}], "deprecated": false, "examples": [{"raw_sline": "{{ \"camelize_value\" | camelize() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/camelize", "name": "camelize", "return_type": ["string"], "summary": "Convert a string to camelCase.", "summary_cn": "将字符串转换为驼峰式命名。", "syntax": "string | camelize()"}, {"arguments": [{"description": "The value to be converted.", "description_cn": "被转换的值。", "name": "string", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/products/product-009-92883-19", "raw_sline": "{{ product.brand | capitalize() }}", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/filter/capitalize", "name": "capitalize", "return_type": ["string"], "summary": "Capitalize the first letter of the first word in a string.", "summary_cn": "将字符串中的第一个单词首字母大写。", "syntax": "string | capitalize()"}, {"arguments": [{"description": "The value converted to lower case.", "description_cn": "被转换为小写的值。", "name": "string", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/products/product-009-92883-19", "raw_sline": "{{ product.title | downcase() }}", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/filter/downcase", "name": "downcase", "return_type": ["string"], "summary": "Converts all characters in a string to lowercase.", "summary_cn": "将字符串中的所有字符转换为小写。", "syntax": "string | downcase()"}, {"arguments": [{"description": "The string to be replaced.", "description_cn": "需要被替换的字符串。", "name": "string", "types": ["string"]}], "deprecated": false, "examples": [{"path": "/products/product-009-92883-19", "raw_sline": "{{{ product.description | escape() }}}", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/filter/escape", "name": "escape", "return_type": ["string"], "summary": "Converts special characters in HTML (e.g. `&`, `<>`, etc.) to their corresponding escape sequences. Characters in the string that do not have a corresponding escape sequence are left unchanged.", "summary_cn": "将 HTML 中的特殊字符（例如 `&`、`<>` 等）转换为相应的转义序列。字符串中没有对应转义序列的字符将保持不变。", "syntax": "string | escape()"}, {"arguments": [{"description": "The variable that needs to be converted to a handle.", "description_cn": "需要转换为handle的变量。", "name": "string", "types": ["string"]}], "deprecated": false, "description": "Conversion rules:\n- Convert all alphabetic characters in a string to lowercase\n- Replace all matching non-word characters with spaces\n- Remove whitespace characters from the beginning and end of a string\n- Replace consecutive whitespace characters (spaces, tabs, etc.) within a string with a single hyphen `-`", "description_cn": "转换规则：\n- 将字符串中的所有字母字符转换为小写\n- 将所有匹配的非单词字符替换为空格\n- 去除字符串首尾的空白字符\n- 将字符串内部的连续空白字符（空格、制表符等）替换为单个连字符 `-`", "examples": [{"raw_sline": "{{ \"100% M & Ms\" | handleize() }}"}], "link": "https://developer.shopline.com/docs/sline/filter/handleize", "name": "handleize", "return_type": ["string"], "summary": "Convert a string to a valid handle.", "summary_cn": "将字符串转换为合法的handle。", "syntax": "string | handleize()"}, {"arguments": [{"description": "The variable in which the newline characters need to be replaced.", "description_cn": "需要替换换行符的变量。", "name": "string", "types": ["string"]}], "deprecated": false, "description": "The returned HTML tags need to be rendered using `{{{ }}}` to prevent the br tag from being escaped.", "description_cn": "返回的html标签，需要使用 `{{{ }}}`  渲染，避免br标签被转义。", "examples": [{"path": "/products/product-009-92883-19", "raw_sline": "{{{ product.description | newline_to_br() }}}", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/filter/newline-to-br", "name": "newline_to_br", "return_type": ["string"], "summary": "Replaces line breaks (\\n) in a string with HTML line break tags (\\<br>).", "summary_cn": "将字符串中的换行符（\\n）替换为HTML换行标签（\\<br>）。", "syntax": "string | newline_to_br()"}, {"arguments": [{"description": "Style picker dynamically configures values.", "description_cn": "style picker 动态配置值。", "name": "style_setting", "types": ["any"]}, {"description": "The corresponding css configuration property name.", "description_cn": "对应css配置的属性名。", "name": "key_name", "types": ["string"]}], "deprecated": false, "description": "", "description_cn": "", "examples": [{"description": "Get the default row-gap value.", "description_cn": "获取默认的row-gap值。", "syntax": "<div style=\"--info-row-gap: {{section.settings.layout | css_var(`row-gap`)}};\">\n  content\n</div>\n\n\n{{#schema}}\n{\n  \"name\": \"Demo\",\n  \"tag\": \"\",\n  \"settings\": [\n    {\n      \"type\": \"style.layout\",\n      \"id\": \"layout\",\n      \"label\": \"layout\",\n      \"default\": {\n        \"flex-wrap\": \"nowrap\",\n        \"flex-direction\": \"column\",\n        \"row-gap\": \"20px\",\n        \"column-gap\": \"20px\"\n      }\n    }\n  ]\n}\n{{/schema}}\n"}], "link": "https://developer.shopline.com/docs/sline/filter/css-var", "name": "css_var", "return_type": ["string"], "summary": "Used with style picker to output the CSS variable name corresponding to the configuration.", "summary_cn": "与style picker配合使用，输出配置对应的css变量名.", "syntax": "style_setting | css_var(key_name)"}, {"arguments": [{"description": "The collection object.", "description_cn": "商品分类 Object 对象。", "name": "collection", "types": ["collection"]}, {"description": "Number of items per page.", "description_cn": "每页数量。", "name": "pageSize", "types": ["number"]}], "deprecated": false, "description": "To be used in combination with the following objects:\n\n- [collection](/docs/sline/object/collection)", "description_cn": "适用于跟以下 Object 结合使用：\n\n- [collection](/docs/sline/object/collection)", "examples": [{"description": "", "name": "Get the list of products under the product category", "name_cn": "获取商品分类下的商品列表", "path": "/collections/bottom", "raw_sline": "{{#var paginate = collection | get_product_pagination(10) /}}\n{{#for item in paginate.list}}\n  {{forloop.index}}: {{ item.title }}\n{{/for}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/filter/get-product-pagination", "name": "get_product_pagination", "return_type": ["paginate"], "summary": "Get product objects via pagination.\n\n", "summary_cn": "通过分页获取商品对象。", "syntax": "collection | get_product_pagination(pageSize)"}]