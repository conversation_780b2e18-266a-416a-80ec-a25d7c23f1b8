// SLine 语言客户端实现
import * as path from 'path';
import { workspace, ExtensionContext } from 'vscode';

import {
    LanguageClient,
    LanguageClientOptions,
    ServerOptions,
    TransportKind
} from 'vscode-languageclient/node';

let client: LanguageClient;

export function activate(context: ExtensionContext) {
    // 语言服务器模块路径
    const serverModule = context.asAbsolutePath(
        path.join('dist', 'server', 'server.js')
    );
    
    // 调试选项
    const debugOptions = { execArgv: ['--nolazy', '--inspect=6009'] };

    // 服务器选项
    const serverOptions: ServerOptions = {
        run: { module: serverModule, transport: TransportKind.ipc },
        debug: {
            module: serverModule,
            transport: TransportKind.ipc,
            options: debugOptions
        }
    };

    // 客户端选项
    const clientOptions: LanguageClientOptions = {
        // 注册服务器支持的文档类型
        documentSelector: [
            { scheme: 'file', language: 'sline' },
            { scheme: 'file', pattern: '**/*.sline' },
            { scheme: 'file', pattern: '**/*.html' }
        ],
        synchronize: {
            // 监听工作区中 .sline 和 .html 文件的变化
            fileEvents: workspace.createFileSystemWatcher('**/*.{sline,html}')
        },
        // 初始化选项
        initializationOptions: {
            enableDiagnostics: true,
            enableCompletion: true,
            maxNumberOfProblems: 1000
        }
    };

    // 创建语言客户端并启动
    client = new LanguageClient(
        'slineLanguageServer',
        'SLine Language Server',
        serverOptions,
        clientOptions
    );

    // 启动客户端，这也会启动服务器
    console.log('Starting SLine Language Client...');
    client.start().then(() => {
        console.log('SLine Language Client started successfully!');
    }).catch(error => {
        console.error('Failed to start SLine Language Client:', error);
    });
}

export function deactivate(): Thenable<void> | undefined {
    if (!client) {
        return undefined;
    }
    return client.stop();
}
