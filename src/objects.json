[{"deprecated": false, "examples": [], "link": "https://developer.shopline.com/docs/sline/object/blog", "name": "blog", "properties": [{"_internal_": {}, "array_return_type": "string", "deprecated": false, "description": "This includes tags of articles that aren't in the current pagination view.", "description_cn": "这包括不在当前分页视图中的文章标签。", "name": "all_tags", "return_type": "array", "summary": "All of the tags on the articles in the blog collection.", "summary_cn": "文章集合中所有的标签。"}, {"_internal_": {}, "array_return_type": "article", "deprecated": false, "name": "articles", "return_type": "array", "summary": "The articles in the blog collection.", "summary_cn": "博客集合中的文章。"}, {"_internal_": {}, "deprecated": false, "name": "articles_count", "return_type": "number", "summary": "The total number of articles in the blog collection. This total doesn't include hidden articles.", "summary_cn": "博客集合中的文章总数。此总数不包括隐藏的文章。"}, {"_internal_": {}, "deprecated": false, "name": "comments_enabled", "return_type": "boolean", "summary": "Returns `true` if comments are enabled for the blog collection. Returns `false` if not", "summary_cn": "如果博客集合启用了评论，则返回 `true`，如果不是，则返回 `false`。"}, {"_internal_": {}, "deprecated": false, "name": "handle", "return_type": "string", "summary": "The [handle](/docs/sline/object/handle) of the blog collection.", "summary_cn": "博客集合的 [handle](/docs/sline/object/handle)。"}, {"_internal_": {}, "deprecated": false, "name": "id", "return_type": "string", "summary": "The ID of the blog collection.", "summary_cn": "博客集合的 ID。"}, {"_internal_": {}, "deprecated": false, "name": "moderated", "return_type": "boolean", "summary": "Returns `true` if the blog collection is set to moderate comments. Returns `false` if not.", "summary_cn": "如果博客集合设置为'允许评论，需经过审核'，则返回 `true`。如果不是，则返回 `false`。"}, {"_internal_": {}, "array_return_type": "string", "deprecated": false, "description": "Unlike `all_tags`, this property only returns tags of articles that are in the filtered view.", "description_cn": "与 `all_tags` 不同，此属性仅返回当前页面的文章标签。", "name": "tags", "return_type": "array", "summary": "A list of all of the tags on all of the articles in the blog collection.", "summary_cn": "博客集合中所有文章的所有标签的列表。"}, {"_internal_": {}, "deprecated": false, "name": "title", "return_type": "string", "summary": "The title of the blog collection.", "summary_cn": "博客集合的标题。"}, {"_internal_": {}, "deprecated": false, "name": "url", "return_type": "string", "summary": "The relative URL of the blog collection.", "summary_cn": "博客集合的相对 URL。"}], "summary": "Information about a specific [blog](https://help.shopline.com/hc/articles/4409354423833) in the store.", "summary_cn": "商店中的一篇的 [博客](https://help.shopline.com/hc/articles/4409354423833) 信息。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/article", "name": "article", "properties": [{"_internal_": {}, "array_return_type": "comment", "deprecated": false, "name": "author", "summary": "The full name of the author of the article.", "summary_cn": "文章作者的全名。"}, {"_internal_": {}, "array_return_type": "comment", "deprecated": false, "name": "comments", "return_type": "array", "summary": "The published comments for the article.", "summary_cn": "发布文章的评论。"}, {"_internal_": {}, "deprecated": false, "name": "comments_count", "return_type": "number", "summary": "The number of published comments for the article.", "summary_cn": "发表的文章评论的数量。"}, {"_internal_": {}, "deprecated": false, "name": "comments_enabled", "return_type": "boolean", "summary": "Returns true if comments are enabled. Returns false if not.", "summary_cn": "如果启用评论则返回 true,否则返回 false。"}, {"_internal_": {}, "deprecated": false, "name": "content", "return_type": "string", "summary": "The content of the article.", "summary_cn": "文章的内容。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"Tip\"}\nUse the [date helper](/docs/sline/filter/date) to format the timestamp.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n可以使用[date helper](/docs/sline/filter/date)去格式化这个时间戳。\n:::", "name": "created_at", "return_type": "string", "summary": "A timestamp for when the article was created.", "summary_cn": "创建文章的时间,是一个时间戳。\n\n"}, {"_internal_": {}, "deprecated": false, "name": "excerpt", "return_type": "string", "summary": "The excerpt of the article.", "summary_cn": "文章的摘要。"}, {"_internal_": {}, "deprecated": false, "name": "excerpt_or_content", "return_type": "string", "summary": "Returns the article [excerpt](#article.excerpt) if it exists. Returns the article [content](#article.content) if no excerpt exists.", "summary_cn": "文章摘要存在则返回 [excerpt](#article.excerpt) 字段，如果摘要字段不存在，则返回文章内容 [content](#article.content) 字段。"}, {"_internal_": {}, "deprecated": false, "name": "handle", "return_type": "string", "summary": "The [handle](/docs/sline/object/handle) of the article.", "summary_cn": "文章的 [handle](/docs/sline/object/handle)。"}, {"_internal_": {}, "deprecated": false, "name": "id", "return_type": "string", "summary": "The ID of the article.", "summary_cn": "文章的 ID。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "name": "image", "return_type": "image", "summary": "The featured [image](/docs/sline/object/image) for the article.", "summary_cn": "文章的封面 [图片](/docs/sline/object/image)。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"Tip\"}\nUse the [date helper](/docs/sline/filter/date) to format the timestamp.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n可以使用[date helper](/docs/sline/filter/date)去格式化这个时间戳。\n:::", "name": "published_at", "return_type": "string", "summary": "A timestamp for when the article was published.", "summary_cn": "文章发布的时间，是一个时间戳。"}, {"_internal_": {}, "deprecated": false, "name": "title", "return_type": "string", "summary": "The title of the article.", "summary_cn": "文章的标题。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"Tip\"}\nUse the [date helper](/docs/sline/filter/date) to format the timestamp.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n可以使用[date helper](/docs/sline/filter/date)去格式化这个时间戳。\n:::", "name": "updated_at", "return_type": "string", "summary": "A timestamp for when the article was updated.", "summary_cn": "文章更新的时间，是一个时间戳。"}, {"_internal_": {}, "deprecated": false, "name": "url", "summary": "The article is at the URL link address of this store.", "summary_cn": "文章再此店铺的URL链接地址。"}, {"_internal_": {}, "deprecated": false, "name": "moderated", "return_type": "boolean", "summary": "Whether to allow comments, valid enumeration values include:\n\ntrue: allow comments\n\nfalse: do not allow comments", "summary_cn": "是否允许评论，有效枚举值包括:\n\ntrue：允许评论\n\nfalse：不允许评论"}, {"_internal_": {}, "array_return_type": "string", "deprecated": false, "name": "tags", "return_type": "array", "summary": "All tags applied to the current post.", "summary_cn": "当前文章应用的所有标签。"}, {"_internal_": {}, "deprecated": false, "name": "template_suffix", "return_type": "string", "summary": "The name of the template applied to the current article, for example, article.xxx.json, then xxx is returned. If the article uses the default template, nil is returned.", "summary_cn": "当前文章应用的页面模板名称，例如 article.xxx.json，则返回 xxx。如果文章使用默认模板，则返回 null。"}], "summary": "An article, or blog post, in a blog.", "summary_cn": "一篇文章或者博客。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/product", "name": "product", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "available", "return_type": "boolean", "summary": "Returns `true` if at least one of the variants of the product is available. Returns `false` if not.", "summary_cn": "如果该商品的变体里面至少一个是可售卖的，返回 `true`，否则返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "collections", "return_type": "array", "summary": "The collections that the product belongs to.", "summary_cn": "该商品所属的分类集合。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "compare_at_price", "return_type": "number", "summary": "The lowest compare at price of any variants of the product in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.", "summary_cn": "该商品的款式最低的比较价格。\n\n该值以顾客当地（显示）的货币来输出。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "compare_at_price_max", "return_type": "number", "summary": "The highest compare at price of any variants of the product in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.", "summary_cn": "该商品的款式最高的比较价格。\n\n该值以顾客当地（显示）的货币来输出。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "compare_at_price_min", "return_type": "number", "summary": "The lowest compare at price of any variants of the product in the currency's subunit. This is the same as `product.compare_at_price`.\n\nThe value is output in the customer's local (presentment) currency.", "summary_cn": "该商品的款式最低的比较价格。与 `product.compare_at_price` 一致。\n\n该值以顾客当地（显示）的货币来输出。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "compare_at_price_varies", "return_type": "boolean", "summary": "Returns `true` if the variant compare at prices of the product vary. Returns `false` if not.", "summary_cn": "如果该商品的款式的比较价格存在差异返回 `true`，否则返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "created_at", "return_type": "string", "summary": "A timestamp for when the product was created.", "summary_cn": "该商品创建时的时间戳。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "description", "return_type": "string", "summary": "The description of the product.", "summary_cn": "该商品的描述。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "featured_image", "return_type": "image", "summary": "The first (featured) image attached to the product.", "summary_cn": "该商品的封面图片。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "featured_media", "return_type": "media", "summary": "The first (featured) media attached to the product.", "summary_cn": "该商品的第一个媒体。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "first_available_variant", "return_type": "variant", "summary": "The first available variant of the product.", "summary_cn": "该商品第一个可售卖的款式。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "gift_card", "return_type": "boolean", "summary": "Returns `true` if the product is a gift card. Returns `false` if not.", "summary_cn": "如果该商品是一个礼品卡返回 `true`，否则返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "handle", "return_type": "string", "summary": "The handle of the product.", "summary_cn": "该商品的 handle。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "has_only_default_variant", "return_type": "boolean", "summary": "Returns `true` if the product doesn't have any options. Returns `false` if not.", "summary_cn": "如果该商品没有其他的选项返回 `true`，否则返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The ID of the product.", "summary_cn": "该商品的 ID。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "images", "return_type": "array", "summary": "The images attached to the product.", "summary_cn": "该商品的图片。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "media", "return_type": "array", "summary": "The media attached to the product, sorted by the date it was added to the product.", "summary_cn": "该商品的媒体，根据添加时间来进行排序。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "metafields", "return_type": "metafields", "summary": "The metafields applied to the product.", "summary_cn": "该商品的元字段。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "options", "return_type": "array", "summary": "The option names of the product.", "summary_cn": "该商品的规格名字。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "options_by_name", "return_type": "string", "summary": "Allows you to access a specific product option by its name.", "summary_cn": "允许根据规格的名字获取一个指定的商品规格。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "options_with_values", "return_type": "array", "summary": "The options on the product.", "summary_cn": "该商品的规格列表。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "price", "return_type": "number", "summary": "The lowest price of any variants of the product in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.", "summary_cn": "该商品的款式最低的价格。\n\n该值以顾客当地（显示）的货币来输出。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "price_max", "return_type": "number", "summary": "The highest price of any variants of the product in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.", "summary_cn": "该商品的款式最高的价格。\n\n该值以顾客当地（显示）的货币来输出。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "price_min", "return_type": "number", "summary": "The lowest price of any variants of the product in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.", "summary_cn": "该商品的款式最低的价格。与 `product.price` 一致。\n\n该值以顾客当地（显示）的货币来输出。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "published_at", "return_type": "string", "summary": "A timestamp for when the product was published.", "summary_cn": "该商品发布时的时间戳。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "requires_selling_plan", "return_type": "string", "summary": "Returns `true` if all of the variants of the product require a selling plan. Returns `false` if not.", "summary_cn": "如果该商品所有的款式都需要销售计划返回 `true`。否则返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "selected_or_first_available_variant", "return_type": "variant", "summary": "The currently selected or first available variant of the product.", "summary_cn": "当前选择的或者第一个可售卖的商品的款式。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "selected_variant", "return_type": "variant", "summary": "The currently selected variant of the product.\n\nIf no variant is currently selected, then `null` is returned.", "summary_cn": "当前选择的商品的款式。\n\n如果没有款式被选中，否则返回 `null`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "selling_plan_groups", "return_type": "array", "summary": "The selling plan groups that the variants of the product are included in.", "summary_cn": "包含了商品的款式的销售计划。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "tags", "return_type": "array", "summary": "The tags of the product.", "summary_cn": "该商品的tags。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "template_suffix", "return_type": "string", "summary": "The name of the custom template of the product.\n\nThe name doesn't include the `product`. prefix, or the file extension (`.json` or `.html`).\n\nIf a custom template isn't assigned to the product, then `null` is returned.", "summary_cn": "该商品的自定义模板的名字。\n\n名字中不会包含 `product.`前缀，或者文件的后缀(`.json` or `.html`)。\n\n如果商品没有被分配自定义模板，会返回 `null`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "title", "return_type": "string", "summary": "The title of the product.", "summary_cn": "该商品的标题。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "type", "return_type": "string", "summary": "The type of the product.", "summary_cn": "该商品的类型。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url", "return_type": "string", "summary": "The relative URL of the product.", "summary_cn": "该商品的相对 URL 地址。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "variants", "return_type": "array", "summary": "The variants of the product.", "summary_cn": "该商品的款式列表。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "vendor", "return_type": "array", "summary": "The brand of the product.", "summary_cn": "该商品的品牌。"}], "summary": "A product in the store.", "summary_cn": "商店里面的一件商品。"}, {"deprecated": false, "description": ":::tip{title=\"TIP\"}\nUse the `format_address` filter to display addresses according to their locale.\n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 `format_address` 过滤器按其区域设置输出地址。\n:::", "link": "https://developer.shopline.com/docs/sline/object/address", "name": "address", "properties": [{"deprecated": false, "name": "address1", "return_type": "string", "summary": "Address's first line.", "summary_cn": "地址的第一行。"}, {"deprecated": false, "description": "Returns an empty string if not provided.", "description_cn": "如果没有指定第二行，则返回空字符串。", "name": "address2", "return_type": "string", "summary": "Address's second line.", "summary_cn": "地址的第二行。"}, {"deprecated": false, "name": "city", "return_type": "string", "summary": "Address's city.", "summary_cn": "地址的城市。"}, {"deprecated": false, "description": "Returns an empty string if not provided.", "description_cn": "如果没有指定公司，则返回空字符串。", "name": "company", "return_type": "string", "summary": "The company related to the address.", "summary_cn": "地址的公司。"}, {"deprecated": false, "name": "country", "return_type": "country", "summary": "Address's country.", "summary_cn": "地址的国家。"}, {"deprecated": false, "name": "country_code", "return_type": "string", "summary": "Address's country in [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) format.", "summary_cn": "地址的国家，采用 [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) 格式。"}, {"deprecated": false, "name": "first_name", "return_type": "string", "summary": "The first name of the customer.", "summary_cn": "客户的名。"}, {"deprecated": false, "name": "id", "return_type": "string", "summary": "Address's identifier.", "summary_cn": "地址的 ID。"}, {"deprecated": false, "name": "last_name", "return_type": "string", "summary": "The last name of the customer.", "summary_cn": "客户的姓氏。"}, {"deprecated": false, "description": "Returns an empty string if not provided.", "description_cn": "如果没有指定电话号码，则返回空字符串。", "name": "phone", "return_type": "string", "summary": "Address's contact number.", "summary_cn": "地址的电话号码。"}, {"deprecated": false, "name": "province", "return_type": "string", "summary": "Address's state.", "summary_cn": "地址的省份。"}, {"deprecated": false, "name": "street", "return_type": "string", "summary": "The combination of the first and second lines of the address.", "summary_cn": "地址的第一行和第二行的组合。"}, {"deprecated": false, "name": "summary", "return_type": "string", "summary": "An overview of the address, including the following attributes:\n- First and second lines\n- City\n- Province/State\n- Country\n", "summary_cn": "地址的摘要，包括以下属性：\n- 第一行和第二行\n- 城市\n- 省份\n- 国家或地区"}, {"deprecated": false, "description": ":::tip{title=\"NOTE\"}\nThis is only applicable to customer addresses.\n:::", "description_cn": ":::tip{title=\"注意\"}\n这仅适用于客户地址。\n:::", "name": "url", "return_type": "string", "summary": "Address's relative link.", "summary_cn": "地址的相对 URL。"}], "summary": "Address, such as customer address or order delivery address.", "summary_cn": "地址，例如客户地址或订单配送地址。"}, {"deprecated": false, "description": "Use 'additional_checkout_buttons' to check if these payment providers are available, and 'content_for_additional_checkout_buttons' to display the checkout buttons associated with them. View Quick checkout for more on how to use these objects.", "description_cn": "使用 `additional_checkout_buttons` 来检查是否存在这些支付提供商，通过使用 `content_for_additional_checkout_buttons` 来显示这些支付提供商关联的结账按钮。请参考快速结账以了解有关如何使用这些对象的更多信息。", "examples": [{"name": "additional_checkout_buttons", "name_cn": "additional_checkout_buttons", "raw_sline": "{{#if additional_checkout_buttons}}\n    {{{content_for_additional_checkout_buttons}}}\n{{/if}}"}], "link": "https://developer.shopline.com/docs/sline/object/additional-checkout-buttons", "name": "additional_checkout_buttons", "summary": "Returns 'true' if the store uses any offsite payment providers, such as PayPal Express Checkout\n", "summary_cn": "如果店铺有任何具有离站结账功能的付款提供商，例如 PayPal Express Checkout，则会返回 `true`\n"}, {"deprecated": false, "description": "Blocks is reusable content module that make up a template. The `block object` will only be allowed in html files in the `blocks` folder.", "description_cn": "Block 是构成模板的可重复使用的内容模块。`block object` 只会在 `blocks` 文件夹下的 html 文件中允许被使用。", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/block", "name": "block", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "This ID is generated dynamically by Shopline.", "description_cn": "这个 ID 由 Shopline 动态生成。", "name": "id", "return_type": "string", "summary": "The ID of the block.", "summary_cn": "block 的 ID。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "For information on access settings, see Access Settings. To discover which input settings are applicable to attributes in the type settings, see Input Settings.", "description_cn": "要了解如何访问设置，请参阅访问设置。要了解哪些输入设置可以应用于type设置中的属性，请参阅输入设置。", "name": "settings", "return_type": "any", "summary": "The settings of the block.", "summary_cn": "block的设置。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "The JavaScript API of the theme editor uses data attributes to identify blocks and listen to events. block.shopline_attributes does not return any value outside the theme editor.", "description_cn": "主题编辑器的JavaScript API 使用数据属性来识别块和侦听事件。block.shopline_attributes在主题编辑器之外不返回任何值。", "name": "shopline_attributes", "return_type": "string", "summary": "The data attributes for the block for use in the theme editor.", "summary_cn": "在主题编辑器中使用的block的数据属性。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "This type is a free-form string defined in the schema of the block. You can use type as an identifier. For instance, you can display different markers depending on the block type.", "description_cn": "该类型是在block的 schema 中定义的自由格式字符串。您可以使用类型作为标识符。例如，您可能会根据块类型显示不同的标记。", "name": "type", "return_type": "string", "summary": "The type of the block.", "summary_cn": "block 的类型。"}, {"_internal_": {}, "deprecated": false, "name": "index0", "return_type": "number", "summary": "The 0-based index of the current block.\n", "summary_cn": "当前 block 基于 0 开始的索引."}, {"_internal_": {}, "deprecated": false, "name": "index", "return_type": "number", "summary": "The 1-based index of the current block.\n", "summary_cn": "当前 block 基于 1 开始的索引."}, {"_internal_": {}, "deprecated": false, "name": "blocks_size", "return_type": "number", "summary": "The number of sub-blocks under the current block.\n", "summary_cn": "当前 block 下的子 block 的数量。"}], "summary": "Block's content and settings.", "summary_cn": "Block 的内容和设置。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/cart", "name": "cart", "properties": [{"_internal_": {}, "array_return_type": "discount_application", "deprecated": false, "name": "cart_level_discount_applications", "return_type": "array", "summary": "The cart-specific discount applications for the cart.", "summary_cn": "购物车的特定于购物车的折扣应用程序。"}, {"_internal_": {}, "array_return_type": "discount_application", "deprecated": false, "name": "discount_applications", "return_type": "array", "summary": "The discount applications for the cart.", "summary_cn": "购物车的折扣申请。"}, {"_internal_": {}, "deprecated": false, "name": "empty", "return_type": "boolean", "summary": "Returns `true` if there are no items in the cart. Return's `false` if there are.", "summary_cn": "返回 `true` 则购物车中没有商品。返回 `false` 则有。"}, {"_internal_": {}, "deprecated": false, "name": "item_count", "return_type": "number", "summary": "The number of items in the cart.", "summary_cn": "购物车中的商品数量。"}, {"_internal_": {}, "deprecated": false, "name": "items", "return_type": "line_item", "summary": "The line items in the cart.", "summary_cn": "购物车中的商品行。"}, {"_internal_": {}, "deprecated": false, "description": "The value is output in the customer's local (presentment) currency.\n\n:::tip{title=\"TIP\"} \nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": "\n该值以客户的本地（出示）货币输出。\n\n:::tip{title=\"提示\"} \n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 输出格式化的金额。 \n:::", "name": "items_subtotal_price", "return_type": "number", "summary": "The total price of all of the items in the cart in the currency's subunit, after any line item discounts. This doesn't include taxes (unless taxes are included in the prices), cart discounts, or shipping costs.", "summary_cn": "在任何订单项折扣后，购物车中所有项目在货币子单位中的总价。这不包括税费（除非税费包含在价格中）、购物车折扣或运费。"}, {"_internal_": {}, "deprecated": false, "description": "The value is output in the customer's local (presentment) currency.\n\n:::tip{title=\"TIP\"} \nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": "该值以客户的本地（出示）货币输出。\n\n:::tip{title=\"提示\"} \n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 输出格式化的金额。 \n:::", "name": "original_total_price", "return_type": "number", "summary": "The total price of all of the items in the cart in the currency's subunit, before discounts have been applied.", "summary_cn": "在应用折扣之前，以货币子单位表示的购物车中所有商品的总价。"}, {"_internal_": {}, "deprecated": false, "name": "requires_shipping", "return_type": "boolean", "summary": "Returns `true` if any of the products in the cart require shipping. Returns `false` if not.", "summary_cn": "如果购物车中的任何产品需要运输，则返回 `true`。如果不是，则返回 `false`。"}, {"_internal_": {}, "deprecated": false, "description": "The value is output in the customer's local (presentment) currency.\n\n:::tip{title=\"TIP\"} \nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": "该值以客户的本地（出示）货币输出。\n\n:::tip{title=\"提示\"} \n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 输出格式化的金额。 \n:::", "name": "total_discount", "return_type": "number", "summary": "The total amount of all discounts (the amount saved) for the cart in the currency's subunit.", "summary_cn": "以货币子单位表示的购物车的所有折扣总额（节省的金额）。"}, {"_internal_": {}, "deprecated": false, "description": "The value is output in the customer's local (presentment) currency.\n\n:::tip{title=\"TIP\"} \nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": "该值以客户的本地（出示）货币输出。\n\n:::tip{title=\"提示\"} \n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 输出格式化的金额。 \n:::", "name": "total_price", "return_type": "number", "summary": "The total price of all of the items in the cart in the currency's subunit, after discounts have been applied.", "summary_cn": "应用折扣后，以货币子单位表示的购物车中所有商品的总价。"}, {"_internal_": {}, "deprecated": false, "name": "total_weight", "return_type": "number", "summary": "The total weight of all of the items in the cart in grams.", "summary_cn": "购物车中所有商品的总重量（以克为单位）。"}], "summary": "A customer’s cart.", "summary_cn": "客户的购物车。"}, {"deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/checkout", "name": "checkout", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "applied_gift_cards", "return_type": "array", "summary": "The gift cards applied to the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "attributes", "return_type": "", "summary": "Additional attributes entered by the customer with the cart.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "billing_address", "return_type": "address", "summary": "The billing address entered at checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "buyer_accepts_marketing", "return_type": "boolean", "summary": "Returns true if the customer checks the email marketing subscription checkbox. Returns false if not.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "currency", "return_type": "string", "summary": "The ISO code of the currency of the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "customer", "return_type": "customer", "summary": "The customer associated with the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "discounts_amount", "return_type": "number", "summary": "The total amount of the discounts applied to the checkout in the currency's subunit.\\nThe value is output in the customer's local (presentment) currency.TipUse money_with_currency helper to output a formatted amount.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "discounts_savings", "return_type": "number", "summary": "The total amount of the discounts applied to the checkout in the currency's subunit, as a negative value.\\nThe value is output in the customer's local (presentment) currency.TipUse money_with_currency helper to output a formatted amount.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "email", "return_type": "string", "summary": "The email associated with the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "gift_cards_amount", "return_type": "number", "summary": "The amount of the checkout price paid in gift cards.\\nThe value is output in the customer's local (presentment) currency.TipUse money_with_currency helper to output a formatted amount.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The ID of the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "line_items", "return_type": "array", "summary": "The line items of the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "line_items_subtotal_price", "return_type": "number", "summary": "The sum of the prices of all of the line items of the checkout in the currency's subunit, after any line item discounts have been applied.\\nThe value is output in the customer's local (presentment) currency.TipUse money_with_currency helper to output a formatted amount.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "note", "return_type": "string", "summary": "Additional information entered by the customer with the cart.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "shipping_address", "return_type": "address", "summary": "The shipping address of the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "shipping_method", "return_type": "shipping_method", "summary": "The shipping method of the checkout.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "shipping_price", "return_type": "number", "summary": "The shipping price of the checkout in the currency's subunit.\\nThe value is output in the customer's local (presentment) currency.TipUse money_with_currency helper to output a formatted amount.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "tax_price", "return_type": "number", "summary": "The total tax amount of the checkout in the currency's subunit.\\nThe value is output in the customer's local (presentment) currency.TipUse money_with_currency helper to output a formatted amount.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "total_price", "return_type": "number", "summary": "The total price of the checkout in the currency's subunit.\\nThe value is output in the customer's local (presentment) currency.TipUse money_with_currency helper to output a formatted amount.", "summary_cn": ""}], "summary": "A customer's checkout.", "summary_cn": ""}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/collection", "name": "collection", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "all_products_count", "return_type": "number", "summary": "The total number of products in a collection.\n\nThis includes products that have been filtered out of the current view.\n\nTip: To display the number of products in a filtered collection, use `collection.products_count`.", "summary_cn": "商品分类中的产品总数。\n\n这包括已从当前视图中过滤掉的产品。\n\n提示：要显示当前分类过滤后的产品数量，请使用  `collection.products_count`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "all_tags", "return_type": "number", "summary": "All of the tags applied to the products in the collection.\n\nThis includes tags for products that have been filtered out of the current view. A maximum of 1,000 tags can be returned.\n\nTip: To display the tags that are currently applied, use `collection.tags`.", "summary_cn": "应用于商品分类中商品的所有标签。\n\n这包括已从当前视图中过滤掉的产品标签。最多可返回 1,000 个标签。\n\n提示：要显示当前应用的标签，请使用 `collection.tags`."}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "all_types", "return_type": "array", "summary": "All of the product types in a collection.", "summary_cn": "商品分类中的所有产品类型。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "all_vendors", "return_type": "array", "summary": "All of the product vendors in a collection.", "summary_cn": "商品分类中的所有产品供应商。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "current_type", "return_type": "string", "summary": "The product type on a product type collection page.\n\nYou can query for products of a certain type at the `/collections/types` URL with a query parameter in the format of `?q=[type]`, where `[type]` is your desired product type.\n\nTip: The query value is case-insensitive, so `shirts` is equivalent to `Shirts` or `SHIRTS`.", "summary_cn": "当前商品分类页的产品类型\n\n您可以在 `/collections/types` URL添加 `?q=[type]` 参数查询特定类型的产品，其中 `[type]` 是您想要的产品类型。\n\n提示：参数值大小写不敏感，因此 `shirts` 等同于 `Shirts` 或 `SHIRTS`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "current_vendor", "return_type": "string", "summary": "The vendor name on a vendor collection page.\n\nYou can query for products from a certain vendor at the `/collections/vendors` URL with a query parameter in the format of `?q=[vendor]`, where `[vendor]` is your desired product vendor.\n\nTip: The query value is case-insensitive, so `apparelco` is equivalent to `ApparelCo` or `APPARELCO`.", "summary_cn": "当前商品分类页的供应商。\n\n您可以在 `/collections/vendors` URL添加 `?q=[vendor]` 参数查询特定类型的产品，其中 `[vendor] `是您想要的产品供应商。\n\n提示：参数值大小写不敏感，因此 `apparelco` 等同于 `ApparelCo` 或 `APPARELCO`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "default_sort_by", "return_type": "string from a set of values", "summary": "The default sort order of the collection.\n\nThis is set on the collection's page in the SHOPLINE admin.", "summary_cn": "商品分类的默认排序顺序。\n\n这是在 SHOPLINE 后台的商品分类页面上设置的。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "description", "return_type": "string", "summary": "The description of the collection.", "summary_cn": "商品分类的描述。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "featured_image", "return_type": "image", "summary": "The featured image for the collection.\n\nThe default is the collection cover image. If this image isn't available, then SHOPLINE falls back to the featured image of the first product in the collection. If the first product in the collection doesn't have a featured image, then `null` is returned.", "summary_cn": "分类的图片。\n\n默认使用分类封面图。如果此图片不可用，使用分类中第一个产品的封面图。如果分类中的第一个产品没有封面图，则返回 `null`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "filters", "return_type": "array", "summary": "The storefront filters that have been set up on the collection.\n\nOnly filters relevant to the current collection are returned. Filters will be empty for collections that contain over 5000 products.", "summary_cn": "已在商品分类上设置好的店铺前端过滤器。\n\n仅会返回与当前商品分类相关的过滤器。对于包含超过 5000 件商品的商品分类，过滤器将为空。 "}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "handle", "return_type": "string", "summary": "The handle of the collection.", "summary_cn": "商品分类的 handle。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The ID of the collection.", "summary_cn": "商品分类的 ID。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "image", "return_type": "image", "summary": "The cover image for the collection.\n\nThis cover image is added on the collection's page in the SHOPLINE admin.", "summary_cn": "商品分类的封面图片。\n\n这是在 SHOPLINE 后台的商品分类页面上设置的。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "products", "return_type": "array", "summary": "All of the products in the collection.\n\nTip: Use the `paginate helper` to choose how many products to show per page, up to a limit of 50.", "summary_cn": "商品分类中的所有产品。\n\n提示：使用 `paginate helper` 帮助每页显示多少产品, 最多50个。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "products_count", "return_type": "number", "summary": "The total number of products in the current view of the collection.", "summary_cn": "当前商品分类视图中的产品总数。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "published_at", "return_type": "string", "summary": "A timestamp for when the collection was published.\n\nTip: Use the `date helper` to format the timestamp.", "summary_cn": "商品分类发布时间。\n\n提示：使用 `date helper` 格式化时间。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "sort_by", "return_type": "string", "summary": "The sort order applied to the collection by the `sort_by` URL parameter.\n\nIf there's no sort_by URL parameter, then the value is `null`.", "summary_cn": "通过 `sort_by` URL 参数应用于商品分类的排序顺序。\n\n如果没有 `sort_by` URL 参数，则值为 null。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "sort_options", "return_type": "array", "summary": "The available sorting options for the collection.", "summary_cn": "商品分类可用排序选项。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "tags", "return_type": "array", "summary": "The tags that are currently applied to the collection.\n\nThis doesn't include tags for products that have been filtered out of the current view. Returns `empty array` if no tags have been applied, or all products with tags have been filtered out of the current view.", "summary_cn": "当前应用于商品分类的标签。\n\n这不包括已从当前视图中过滤掉的产品标签。如果没有应用任何标签，或者所有带有标签的产品都已从当前视图中过滤掉，则返回 `empty array`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "title", "return_type": "string", "summary": "The title of the collection.", "summary_cn": "商品分类的标题。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url", "return_type": "string", "summary": "The relative URL of the collection.", "summary_cn": "商品分类的相对 URL。"}], "summary": "A collection in a store.", "summary_cn": "店铺的商品分类。"}, {"deprecated": false, "description": "**Iterate over the collections**\n\nYou can iterate over `collections` to build a collection list.", "description_cn": "**遍历 collections**\n\n您可以遍历 `collections` 以构建分类的列表。", "examples": [{"description": "", "description_cn": "", "name": "", "name_cn": "", "path": "/collections-all", "raw_sline": "{{#for collection in collections }}\n    {{#link_to collection.url}}\n        {{collection.title}}\n    {{/link_to}}}\n{{/for}}", "source_object": "collections"}], "link": "https://developer.shopline.com/docs/sline/object/collections", "name": "collections", "properties": [], "summary": "All of the collections on a store.", "summary_cn": "商店的全部商品分类。"}, {"deprecated": false, "description": "Can be used to set different color styles for different components of your website.", "description_cn": "可以用于为网站的不同组件设置不同的颜色样式。", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/color", "name": "color", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "alpha", "return_type": "number", "summary": "The alpha component of the color, which is a decimal number between 0.0 and 1.0.", "summary_cn": "颜色的 alpha 组件，取值范围为 0.0 到 1.0 之间的十进制数。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "bule", "return_type": "number", "summary": "The blue component of the color, which is a number between 0 and 255.", "summary_cn": "颜色的蓝色分量，取值范围为 0 到 255 之间的数值。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "green", "return_type": "number", "summary": "The green component of the color, which is a number between 0 and 255.", "summary_cn": "颜色的绿色分量，取值范围为 0 到 255 之间的数值。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "hue", "return_type": "number", "summary": "The hue component of the color, which is a number between 0 and 360.", "summary_cn": "颜色的色调分量，取值范围为 0 到 360 之间的数值。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "lightness", "return_type": "number", "summary": "The lightness component of the color, which is a number between 0 and 100.", "summary_cn": "颜色的亮度分量，取值范围为 0 到 100 之间的数值。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "red", "return_type": "number", "summary": "The red component of the color, which is a number between 0 and 255.", "summary_cn": "颜色的红色分量，取值范围为 0 到 255 之间的数值。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "rgb", "return_type": "string", "summary": "The red, green, and blue values of the color, represented as a space-separated string.", "summary_cn": "颜色的红色、绿色和蓝色值，以空格分隔的字符串表示。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "rgba", "return_type": "string", "summary": "The red, green, blue, and alpha values of the color, represented as a space-separated string, with a slash before the alpha channel.", "summary_cn": "颜色的红色、绿色、蓝色和 alpha 通道值，以空格分隔的字符串表示，在 alpha 通道之前加斜杠。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "saturation", "return_type": "number", "summary": "The saturation component of the color, which is a number between 0 and 100.", "summary_cn": "颜色的饱和度分量，取值范围为 0 到 100 之间的数值。"}, {"_internal_": {}, "deprecated": false, "name": "hex", "return_type": "string", "summary": "The hexadecimal color code.\n", "summary_cn": "颜色的十六进制色码。"}], "summary": "A color from a color setting.", "summary_cn": "颜色设置中的颜色。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/comment", "name": "comment", "properties": [{"_internal_": {}, "deprecated": false, "name": "author", "return_type": "string", "summary": "The full name of the author of the comment.", "summary_cn": "评论的作者全名。"}, {"_internal_": {}, "deprecated": false, "name": "content", "return_type": "string", "summary": "The content of the comment.", "summary_cn": "评论的内容。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"Tip\"}\nUse the [date helper](/docs/sline/filter/date) to format the timestamp.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n可以使用 [date helper](/docs/sline/filter/date) 去格式化这个时间戳。\n:::", "name": "created_at", "return_type": "string", "summary": "A timestamp for when the comment was created.\n\n", "summary_cn": "创建评论的时间，是一个时间戳。 \n\n"}, {"_internal_": {}, "deprecated": false, "name": "email", "summary": "The email of he author of the comment.", "summary_cn": "评论作者的电子邮件。"}, {"_internal_": {}, "deprecated": false, "name": "id", "return_type": "string", "summary": "The ID of the comment.", "summary_cn": "评论的 ID。"}, {"_internal_": {}, "deprecated": false, "name": "status", "return_type": "string", "summary": "The status of the comment. Always returns APPROVED.\n\nOutside of the html context, the status of a comment can vary based on spam detection and whether blog comments are moderated. However, only comments with a status of APPROVED are included in the article.comments array.", "summary_cn": "评论的状态。 始终返回 APPROVED。\n\n在 html 上下文之外，评论的状态可能会根据输入的邮件检测和博客评论是否经过审核而有所不同。 但是，只有状态为APPROVED的评论才会包含在 article.comments 数组中。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"Tip\"}\nUse the [date helper](/docs/sline/filter/date) to format the timestamp.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n可以使用 [date helper](/docs/sline/filter/date) 去格式化这个时间戳。\n:::", "name": "updated_at", "return_type": "string", "summary": "A timestamp for when the status of the comment was last updated. \n\n", "summary_cn": "评论状态最后更新时间，是一个时间戳。 \n\n"}, {"_internal_": {}, "deprecated": false, "name": "url", "summary": "The relative URL of the article that the comment is associated with, with  [comment.id](#id) appended.", "summary_cn": "对应文章评论的相对 url，是一个锚链接，例如 [comment.id](#id)\n"}], "summary": "An article comment.", "summary_cn": "一篇文章评论。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/company", "name": "company", "properties": [{"_internal_": {}, "array_return_type": "company_location", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "available_locations", "return_type": "array", "summary": "The current list of company locations accessible to the customer.", "summary_cn": "当前客户可访问的公司地址列表。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "Company ID.", "summary_cn": "公司ID。"}, {"_internal_": {}, "array_return_type": "metafield", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "To learn about how to create metafields, refer to [Guide to Using Metafields Feature](https://help.shopline.com/hc/en-001/articles/7095355017113-Guide-to-Using-Metafields-Feature).", "description_cn": "想要了解如何创建元字段，可以参考[元字段功能使用指南](https://help.shopline.com/hc/zh-cn/articles/7095355017113-%E5%85%83%E5%AD%97%E6%AE%B5-Metafields-%E5%8A%9F%E8%83%BD%E4%BD%BF%E7%94%A8%E6%8C%87%E5%8D%97)。", "name": "metafields", "return_type": "array", "summary": "The [metafields](https://developer.shopline.com/docsv2/ec20/3cv5d7wpfgr6a8z5/n5rgeucmx1qhhpqw) applicable to the company.", "summary_cn": "适用于公司的 [元字段](https://developer.shopline.com/docsv2/ec20/3cv5d7wpfgr6a8z5/n5rgeucmx1qhhpqw)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "name", "return_type": "string", "summary": "Company name.", "summary_cn": "公司名称。"}], "summary": "The company information used by the [customer](/docs/sline/object/customer) when placing an order.\n\nTo learn about B2B functionality, please refer to the [B2B Company](https://help.shopline.com/hc/en-001/articles/**************-Creating-and-Managing-B2B-Company-Profiles).", "summary_cn": "[客户](/docs/sline/object/customer)下单时使用的公司信息。\n\n要了解B2B功能，请参考[B2B公司](https://help.shopline.com/hc/zh-cn/articles/**************-B2B%E5%85%AC%E5%8F%B8)。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/company-location", "name": "company_location", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "company", "return_type": "company", "summary": "The company associated with the location.", "summary_cn": "地点关联的公司。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "current", "return_type": "boolean", "summary": "Returns `true` if the location is currently selected; otherwise, returns `false`.\n", "summary_cn": "如果当前选择了该地点，则返回 `true`。否则返回 `false`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "Location ID.", "summary_cn": "地点ID"}, {"_internal_": {}, "array_return_type": "metafield", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "To learn about how to create metafields, refer to [Guide to Using Metafields Feature](https://help.shopline.com/hc/en-001/articles/7095355017113-Guide-to-Using-Metafields-Feature).", "description_cn": "想要了解如何创建元字段，可以参考[元字段功能使用指南](https://help.shopline.com/hc/zh-cn/articles/7095355017113-%E5%85%83%E5%AD%97%E6%AE%B5-Metafields-%E5%8A%9F%E8%83%BD%E4%BD%BF%E7%94%A8%E6%8C%87%E5%8D%97)。", "name": "metafields", "return_type": "array", "summary": "The [metafields](https://developer.shopline.com/docsv2/ec20/hdwvcvrgnfcjf4gq/zvcjjshz2hsx9da5) applicable to the company location.", "summary_cn": "适用于公司地点的 [元字段](https://developer.shopline.com/docsv2/ec20/3cv5d7wpfgr6a8z5/n5rgeucmx1qhhpqw)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "name", "return_type": "string", "summary": "Location name.", "summary_cn": "地点名称。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "shipping_address", "return_type": "address", "summary": "Shipping address of the location.", "summary_cn": "地点的收货地址。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "tax_registration_id", "return_type": "number", "summary": "Tax ID of the location.", "summary_cn": "地点的税号。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url_to_set_as_current", "return_type": "string", "summary": "The URL to set the location as the customer's current location.", "summary_cn": "将地点设置为客户当前地点的 URL。"}], "summary": "The [company](/docs/sline/object/company) location used by the [customer](/docs/sline/object/customer) when placing an order.\n\nTo learn about B2B functionality, please refer to the [B2B Company](https://help.shopline.com/hc/en-001/articles/**************-Creating-and-Managing-B2B-Company-Profiles).", "summary_cn": "[客户](/docs/sline/object/customer)下单时使用的[公司](/docs/sline/object/company)地点。\n\n要了解B2B功能, 请参考 [B2B公司](https://help.shopline.com/hc/zh-cn/articles/**************-B2B%E5%85%AC%E5%8F%B8)。"}, {"deprecated": false, "description": "Use `additional_checkout_buttons` to confirm the presence of these payment providers, and use `content_for_additional_checkout_buttons` to display the corresponding checkout buttons. For more details on using these objects, please refer to Accelerated checkout.", "description_cn": "使用 `additional_checkout_buttons` 来检查是否存在这些支付提供商，通过使用 `content_for_additional_checkout_buttons` 来显示这些支付提供商关联的结账按钮。请参考快速结账以了解有关如何使用这些对象的更多信息。", "examples": [{"name": "content_for_additional_checkout_buttons", "name_cn": "content_for_additional_checkout_buttons", "raw_sline": "{{#if additional_checkout_buttons}}\n    {{{content_for_additional_checkout_buttons}}}\n{{/if}}"}], "link": "https://developer.shopline.com/docs/sline/object/content-for-additional-checkout-buttons", "name": "content_for_additional_checkout_buttons", "summary": "Return checkout buttons for any enabled payment providers with offsite checkout capabilities.\n", "summary_cn": "返回任何启用的具有离站结账功能的支付提供商的结账按钮，并可进行站外结账。"}, {"deprecated": false, "description": "Each `<option>` element includes a data-provinces attribute containing a JSON-encoded array of the country’s or region’s sub-regions. If some country has no sub-regions, its `data-provinces` attribute is set to an empty array.\n\n:::tip{title=\"提示\"}\nUse [all_country_option_tags](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/pu2w7jr857z5fwta) to retrieve all countries and regions within the store's shipping zone.\n:::", "description_cn": "每个 `<option>` 都设置了一个名为 `data-provinces` 的属性，包含一个 JSON 编码的该国家或地区的子区域数组。如果一个国家没有任何子区域，那么它的 `data-provinces` 属性将设置为空数组。\n\n:::tip{title=\"提示\"}\n要返回商店运费区域内包含的所有国家和地区，请使用 [all_country_option_tags](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/pu2w7jr857z5fwta)。\n:::", "examples": [{"description": "You can enclose the `country_option_tags` object within a `<select>` element to create a country options selector.", "description_cn": "您可以将 `country_option_tags` 对象包装在 `<select>` 标签中，以构建一个国家选项选择器。", "name": "country_option_tags", "name_cn": "country_option_tags", "raw_sline": "```html\n<select name=\"country\">\n  {{{ country_option_tags }}}\n</select>\n```"}], "link": "https://developer.shopline.com/docs/sline/object/country-option-tags", "name": "country_option_tags", "summary": "On the Shopline admin shipping page, generate an `<option>` element for each country and region within the shipping zone.", "summary_cn": "在 Shopline 管理后台的运费页面，为包含在运费区域内的每个国家和地区创建一个 `<option>` 标签。"}, {"deprecated": false, "description": "To learn how to use country objects in your theme to provide localization options, [refer to Supporting Multiple Currencies and Languages](https://developer.shopline.com/zh-hans-cn/doc/hgr7vj9rpaah1a6m/tjdvrhr15awqbpc6).", "description_cn": "要了解如何使用国家对象在您的主题中提供本地化选项，[请参考支持多种货币和语言](https://developer.shopline.com/zh-hans-cn/doc/hgr7vj9rpaah1a6m/tjdvrhr15awqbpc6)。\n", "link": "https://developer.shopline.com/docs/sline/object/country", "name": "country", "properties": [{"deprecated": false, "name": "currency", "return_type": "currency", "summary": "The currency used by the country.", "summary_cn": "该国家使用的货币。"}, {"deprecated": false, "name": "iso_code", "return_type": "string", "summary": "The ISO code of the country or region, in [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) format.", "summary_cn": "国家或地区的ISO代码，采用 [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) 格式。"}, {"deprecated": false, "name": "name", "summary": "The name of the country.", "summary_cn": "国家的名称。"}], "summary": "Countries or regions that the store's localization options support.", "summary_cn": "商店本地化选项支持的国家或地区。"}, {"deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/currency", "name": "currency", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "iso_code", "return_type": "string", "summary": "The ISO code of the currency.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "name", "return_type": "string", "summary": "The name of the currency.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "symbol", "return_type": "string", "summary": "The symbol of the currency.", "summary_cn": ""}], "summary": "Information about a currency, like the ISO code and symbol.", "summary_cn": ""}, {"deprecated": false, "description": "You can add tags to articles and products. Article tags can be used to filter a blog page to show only articles with specific tags. Similarly, product tags can be used to filter a collection page to show only products with specific tags.", "description_cn": "你可以给文章和产品添加标签。文章标签可用于过滤博客页面，仅显示带有特定标签的文章。同样，产品标签可以用于过滤集合页面，仅显示带有特定标签的产品。", "examples": [{"name": "Generate links to product collections with filtering behavior by tag", "name_cn": "按照标签生成带有过滤行为的商品集合链接", "path": "/collections/bottom/3c,dress", "raw_sline": "{{#for tag in current_tags}}\n  {{#link_to_tag tag}}\n    {{tag}}\n  {{/link_to_tag}}\n{{/for}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/object/current-tags", "name": "current_tags", "properties": [], "summary": "The tags applied to the current page.", "summary_cn": "当前页面应用的标签。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/customer", "name": "customer", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "accepts_email_marketing", "return_type": "boolean", "summary": "Whether the customer has subscribed to marketing emails.", "summary_cn": "客户是否订阅了电子邮件。`true` 表示已订阅，`false` 表示未订阅"}, {"_internal_": {}, "deprecated": false, "name": "accepts_sms_marketing", "return_type": "boolean", "summary": "Whether the customer has subscribed to marketing SMS.", "summary_cn": "客户是否订阅了短信。`true` 表示已订阅，`false` 表示未订阅"}, {"_internal_": {}, "array_return_type": "address", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "Return all personal shipping addresses of the customer, the customer address can only add up to 50 items.", "description_cn": "返回客户所有个人收货地址，最多只能添加50条。", "name": "addresses", "return_type": "array", "summary": "All personal shipping addresses of the customer.", "summary_cn": "客户的所有个人收货地址。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "addresses_count", "return_type": "number", "summary": "The number of personal shipping addresses for the customer. The range is from 0 to 50.", "summary_cn": "客户的个人收货地址数量。范围是0-50"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "b2b", "return_type": "boolean", "summary": "If the customer is a B2B customer, return `true`; otherwise, return `false`.\n\nTo learn about B2B functionality, please refer to the [B2B Company](https://help.shopline.com/hc/en-001/articles/**************).", "summary_cn": "客户是B2B客户返回 `true`，否则返回 `false`。\n\n要了解B2B功能, 请参考 [B2B公司](https://help.shopline.com/hc/zh-cn/articles/**************)。"}, {"_internal_": {}, "array_return_type": "company_location", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "company_available_locations", "return_type": "array", "summary": "The list of company addresses accessible to the customer.\n\nTo learn about B2B functionality, please refer to the [B2B Company](https://help.shopline.com/hc/en-001/articles/**************).", "summary_cn": "客户可访问的公司地址列表。\n\n要了解B2B功能, 请参考 [B2B公司](https://help.shopline.com/hc/zh-cn/articles/**************)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "current_company", "return_type": "company", "summary": "The company information used by the customer when placing an order.\n\nTo learn about B2B functionality, please refer to the [B2B Company](https://help.shopline.com/hc/en-001/articles/**************).", "summary_cn": "客户下单时使用的公司信息。\n\n要了解B2B功能, 请参考 [B2B公司](https://help.shopline.com/hc/zh-cn/articles/**************)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "current_location", "return_type": "company_location", "summary": "The company location used by the customer when placing an order.\n\nTo learn about B2B functionality, please refer to the [B2B Company](https://help.shopline.com/hc/en-001/articles/**************).", "summary_cn": "客户下单时使用的公司地址。\n\n要了解B2B功能, 请参考 [B2B公司](https://help.shopline.com/hc/zh-cn/articles/**************)。"}, {"_internal_": {}, "deprecated": false, "name": "cancelling_account", "return_type": "boolean", "summary": "Returns `true` if customer has applied to delete the account. Returns `false` if not.", "summary_cn": "如果客户申请了删除账号返回 `true`，否则返回 `false`。"}, {"_internal_": {}, "deprecated": false, "name": "cancelling_time", "return_type": "string", "summary": "The time at which the customer account will be deleted.", "summary_cn": "客户账号将被删除的时间戳。可以使用 [data helper](/docsv2/ec20/hdwvcvrgnfcjf4gq/amwhp6y863f6nfrt) 转换为你需要的格式。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "default_address", "return_type": "address", "summary": "The default personal shipping address of the customer.", "summary_cn": "客户的默认个人收货地址。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "email", "return_type": "string", "summary": "The customer's email address. Returns `null` when empty.", "summary_cn": "客户的电子邮箱。为空时返回 `null`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "first_name", "return_type": "string", "summary": "The customer's first name.", "summary_cn": "客户的名字。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "last_name", "return_type": "string", "summary": "The customer's last name.", "summary_cn": "客户的姓氏。"}, {"_internal_": {}, "deprecated": false, "name": "login_sources", "return_type": "untyped", "summary": "Third-party account information of the customer.\n\n- **login_source** string: third party channels. Valid values are:\n    - `facebook`\n    - `google`\n    - `tiktok`\n    - `apple`\n    - `line`\n- **nickname** string: third party channel nickname\n", "summary_cn": "客户绑定的第三方账号信息。\n\n- **login_source** string：第三方渠道。有效枚举值包含：\n    - `facebook`\n    - `google`\n    - `tiktok`\n    - `apple`\n    - `line`\n- **nickname** string：第三方渠道昵称。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "has_account", "return_type": "boolean", "summary": "Returns `true` if the email associated with the customer is bound to a [customer account](https://help.shopline.com/hc/en-001/articles/**************-Understanding-the-Customer-Account-Versions); otherwise, returns `false`.\n      \nCustomers can check out without creating an account in the store. If the customer does not have a store account, `customer.has_account` is `false` at checkout.\n      \nDuring checkout, if the customer has a store account and enters the email associated with the account, `customer.has_account` is true. The email is associated with the account regardless of whether the customer is logged into it.", "summary_cn": "如果与客户关联的电子邮件与[客户帐户](https://help.shopline.com/hc/zh-cn/articles/**************-%E5%AE%A2%E6%88%B7%E8%B4%A6%E6%88%B7\n)绑定，则返回 `true`。 如果没有则返回 `false`。\n\n顾客无需在商店开设账户即可完成结账。 如果客户没有商店帐户，则结帐时 `customer.has_account` 为 `false`。\n\n在结帐过程中，如果客户拥有商店帐户并输入与帐户关联的电子邮件，则 `customer.has_account` 为 `true`。 无论客户是否登录其帐户，电子邮件都会与该帐户相关联。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The customer's ID.", "summary_cn": "客户的ID。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "name", "return_type": "string", "summary": "The customer's full name.", "summary_cn": "客户的名字。"}, {"_internal_": {}, "array_return_type": "order", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "Default display the latest 10 records, can adjust the page size through the `paginate` helper.", "description_cn": "默认显示最新10条数据，可以通过 `paginate` helper 调整分页大小。", "name": "orders", "return_type": "array", "summary": "All orders of the customer.", "summary_cn": "客户的订单列表。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "orders_count", "return_type": "number", "summary": "The total number of orders for the customer.", "summary_cn": "客户的订单总数。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "phone", "return_type": "string", "summary": "The customer's phone number. Returns `null` when empty.", "summary_cn": "客户的手机号。为空时返回 `null`。"}, {"_internal_": {}, "deprecated": false, "name": "phone_dialing_code", "return_type": "string", "summary": "The customer's mobile area code.", "summary_cn": "客户的手机区号。"}, {"_internal_": {}, "array_return_type": "string", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "tags", "return_type": "array", "summary": "The customer's tags.", "summary_cn": "客户的标签列表"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "tax_exempt", "return_type": "boolean", "summary": "Returns `true` if tax-exempt, otherwise returns `false`.", "summary_cn": "如果客户免税返回 `true`，否则返回 `false`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "total_spent", "return_type": "number", "summary": "The total expenditure amount of all current orders for the customer. Can be formatted using the [money filter](/docs/sline/filter/money).", "summary_cn": "客户当前所有订单的总花费金额。可以配合 [money filter](/docs/sline/filter/money) 格式化货币。"}, {"_internal_": {}, "deprecated": true, "deprecation_reason": "Deprecated because the name didn't make it clear that the customer has subscribed to marketing emails or SMS.\n        \nHas been replaced by `customer.accepts_email_marketing` and `customer.accepts_sms_marketing`.", "deprecation_reason_cn": "属性名不能明确表明客户接受了电子邮件订阅还是短信订阅。\n        \n使用 `customer.accepts_email_marketing` 和 `customer.accepts_sms_marketing` 替代。", "name": "accepts_marketing", "summary": "Whether the `customer` accepts marketing.", "summary_cn": "客户是否接受市场营销。"}], "summary": "The [customer](https://help.shopline.com/hc/en-001/articles/**************-Managing-Customers) currently accessing the store. When the customer has logged into their account, the `customer` object can be accessed globally.It's also defined in the following context:\n\n- The template [customers/account](/docs/online-store-3-0-themes/theme-structure/templates)\n- The template [customers/addresses](/docs/online-store-3-0-themes/theme-structure/templates)\n- The template [customers/order](/docs/online-store-3-0-themes/theme-structure/templates)\n\n\nIn addition to the above scenario, if the customer is not logged into their account, the `customer` object returns `null`.\n\n#### Check if the `customer` object exists\nWhen using the `customer` object in a template or object where its existence is not certain, it is advisable to first check if the customer object exists.\n\n\n```js\n{{#if customer }}\n  Hello, {{ customer.first_name }}!\n{{/if}}\n```\n", "summary_cn": "当前商店的 <a target=\"__blank\" href=\"https://help.shopline.com/hc/zh-cn/articles/**************-%E7%AE%A1%E7%90%86%E5%AE%A2%E6%88%B7\">客户</a>。当客户已登录帐户时，可以在全局范围内直接访问 `customer` 对象。它还在以下上下文中定义：\n          \n- 模板 [customers/account](/docs/online-store-3-0-themes/theme-structure/templates)\n- 模板 [customers/addresses](/docs/online-store-3-0-themes/theme-structure/templates)\n- 模板 [customers/order](/docs/online-store-3-0-themes/theme-structure/templates)\n\n在上述情况之外，如果客户未登录账号，则 `customer` 对象返回 `null`。\n\n#### 检查 `customer` 对象是否存在\n在未确定存在 `customer` 对象的模板或对象中使用 `customer` 对象时，应该先判断 `customer` 对象是否存在。\n\n```js\n{{#if customer }}\n  Hello, {{ customer.first_name }}!\n{{/if}}\n```"}, {"deprecated": false, "description": "To learn how to display discounts in the theme, see [Discounts](https://developer.shopline.com/zh-hans-cn/doc/hgr7vj9rpaah1a6m/8jnk748g2d5jb1bm).", "description_cn": "了解如何在主题中显示折扣，请参阅 [Discounts](https://developer.shopline.com/zh-hans-cn/doc/hgr7vj9rpaah1a6m/8jnk748g2d5jb1bm).", "link": "https://developer.shopline.com/docs/sline/object/discount-allocation", "name": "discount_allocation", "properties": [{"deprecated": false, "description": ":::tip{title=\"TIP\"}\nUse [money helper](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7) to output a formatted amount.\n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 [货币格式化hepler](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7) 格式化金额。\n:::", "name": "amount", "return_type": "number", "summary": "The discount amount of the products.", "summary_cn": "商品打折的金额。"}, {"deprecated": false, "name": "discount_application", "return_type": "discount_application", "summary": "Details of the applied discount.", "summary_cn": "应用的折扣详情信息。"}], "summary": "Information on how discounts affect products.", "summary_cn": "有关折扣如何影响商品的信息。"}, {"deprecated": false, "description": "To learn how to display discounts in the theme, see [Discounts](https://developer.shopline.com/zh-hans-cn/doc/hgr7vj9rpaah1a6m/8jnk748g2d5jb1bm).", "description_cn": "了解如何在主题中显示折扣，请参阅 [Discounts](https://developer.shopline.com/zh-hans-cn/doc/hgr7vj9rpaah1a6m/8jnk748g2d5jb1bm).", "link": "https://developer.shopline.com/docs/sline/object/discount-application", "name": "discount_application", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "description": ":::tip{title=\"NOTE\"}\nThe type of discount depends on the `target_type` field.\n:::\n\n| Possible values | Description |\n|-------|-------|\n| all | Applies to all product lines or shipping costs. |\n| entitled | Applies to specific product lines or shipping costs based on certain criteria. |", "description_cn": ":::tip{title=\"提示\"}\n选择的折扣类型是适用于行项目还是物流线路取决于 `target_type` 字段。\n:::\n\n| 可能的值 | 描述 |\n|-------|-------|\n| all | 作用于所有商品行的优惠或者运费项的优惠。 |\n| entitled | 作用于基于某些标准的一组特定商品行的优惠或运费项优惠。 |", "name": "target_selection", "return_type": "string", "summary": "The type of discount applied to line items or shipping lines.\n", "summary_cn": "行项目或者是物流线路所选择的折扣类型。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "description": "| Possible values |\n|-------|\n| line_item |\n| shipping_line |", "description_cn": "| 可能的值 |\n|-------|\n| line_item |\n| shipping_line |", "name": "target_type", "return_type": "string", "summary": "The item type the discount applies to.", "summary_cn": "折扣适用的项目类型。"}, {"_internal_": {}, "deprecated": false, "name": "title", "return_type": "string", "summary": "The name of the discount for display.", "summary_cn": "用于展示的折扣名称。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"}\nUse [money helper](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7) to output a formatted amount.\n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 [货币格式化hepler](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7) 格式化金额。\n:::", "name": "total_allocated_amount", "return_type": "number", "summary": "The total discount amount.", "summary_cn": "折扣总额。"}, {"_internal_": {}, "deprecated": false, "description": "How this value is interpreted depends on the [value type](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/tfa9qtj515mc5erh?version=v20250301#value_type) field. The table below outlines the meaning of each value type:\n\n| Value type | Value |\n|-------|-------|\n| fixed_amount | The discount amount. |\n| percentage | The discount percentage. |\n\n:::tip{title=\"TIP\"}\nUse [money helper](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7) to output a formatted amount.\n:::", "description_cn": "如何解释此值取决于此折扣的 [value_type](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/tfa9qtj515mc5erh?version=v20250301#value_type) 字段。下表概述了每个值类型的值所代表的含义：\n\n| value_type字段值 | 值含义 |\n|-------|-------|\n| fixed_amount | The discount amount. |\n| percentage | The discount percentage. |\n\n:::tip{title=\"提示\"}\n使用 [货币格式化hepler](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7) 格式化金额。\n:::", "name": "value", "return_type": "string", "summary": "The discount value.", "summary_cn": "此折扣的优惠值。"}, {"_internal_": {}, "deprecated": false, "description": "| Possible values |\n|-------|\n| fixed_amount |\n| percentage |", "description_cn": "| 可能的值 |\n|-------|\n| fixed_amount |\n| percentage |", "name": "value_type", "return_type": "string", "summary": "The type of discount value.", "summary_cn": "折扣的值类型。"}, {"_internal_": {"domain": "4"}, "deprecated": false, "description": "- 0: Flash Sale\n- 1: Time-Limited Promotion\n- 2: Bundle Offer\n- 3: Discount Code\n- 4: One-Page Store\n- 5: Free Gift\n- 6: Pre-Order\n- 7: Add-On Item\n- 8: Gift Box\n- 9: Product Upsell Campaign", "description_cn": "- 0：满减满折、免邮\n- 1：限时促销\n- 2：捆绑组合\n- 3：优惠码\n- 4：一页商店\n- 5：赠品活动\n- 6：预售\n- 7：加购品\n- 8：礼品盒\n- 9：商品追售活动", "name": "activity_type", "return_type": "number", "summary": "The type of promotion applied to the product line.", "summary_cn": "商品行享受的活动类型。"}, {"_internal_": {}, "deprecated": false, "description": "- -1：No discount\n- 1：Amount reduction\n- 2：Discount\n- 3：Free shipping fee\n- 4：Marked-up item\n- 5：Preferential price for any N items\n- 6：Preferential price for the combination of A and B\n- 7：Gift\n- 8：Fixed selling price\n- 9：The lowest-priced item is free\n- 11：Discount for the Nth item\n- 12：Buy x and get y free\n- 13：Customized\n- 14：M (Monetary Unit) for N Items", "description_cn": "- -1：无优惠\n- 1：减金额\n- 2：打折\n- 3：免运费\n- 4：加价品\n- 5：任意N件优惠价\n- 6：A+B组合优惠价\n- 7：赠品\n- 8：固定售价\n- 9：最低价商品免费\n- 11：第N件打折\n- 12：买x送y\n- 13：自定义\n- 14：N件M元", "name": "benefit_type", "return_type": "number", "summary": "The type of discount applied to the product line.", "summary_cn": "商品行享受的优惠类型。"}], "summary": "Information about discounts.", "summary_cn": "有关折扣的信息。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/filter", "name": "filter", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "active_values", "return_type": "array", "summary": "Values of currently active filters.\n\nThis array can only have values for `boolean` and `list` type filters.", "summary_cn": "当前处于活动状态的过滤器的值。\n\n该数组只能具有用于 `boolean` 和 `list` 类型过滤器的值。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "false_value", "return_type": "string", "summary": "The `false` filter value.\n\nReturns a value only for `boolean` type filters. Returns `null` for other types.", "summary_cn": "`false` 过滤器值。\n\n仅为 `boolean` 类型过滤器返回一个值。为其他类型返回 `null`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "inactive_values", "return_type": "array", "summary": "Values of currently inactive filters.\n\nThis array can only have values for `boolean` and `list` type filters.", "summary_cn": "当前不活动的过滤器值。\n\n该数组只能具有 `boolean` 和 `list` 类型过滤器的值。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "label", "return_type": "string", "summary": "Customer-facing label of the filter.", "summary_cn": "过滤器面向客户的标签。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "max_value", "return_type": "string", "summary": "The highest filter value.\n\nOnly returns a value for `price_range` type filters. Returns `null` for other types.", "summary_cn": "最高过滤值。\n\n仅为 `price_range` 类型过滤器返回一个值。为其他类型返回 `null`。"}, {"deprecated": false, "name": "min_value", "return_type": "string", "summary": "The lowest filter value.\n\nOnly returns a value for `price_range` type filters. Returns `null` for other types.", "summary_cn": "最低过滤值。\n\n仅为 `price_range` 类型过滤器返回一个值。为其他类型返回 `null`。"}, {"deprecated": false, "description_cn": "", "name": "param_name", "return_type": "string", "summary": "URL parameter for the filter. For instance, `filter.v.availability`", "summary_cn": "过滤器的 URL 参数。例如，`filter.v.availability`"}, {"deprecated": false, "name": "range_max", "return_type": "string", "summary": "The highest product price in the search results or collection.\n\nOnly returns a value for `price_range` type filters. Returns `null` for other types.", "summary_cn": "分类或搜索结果中的最高产品价格。\n\n仅为 `price_range` 类型过滤器返回一个值。为其他类型返回 `null`。"}, {"deprecated": false, "name": "true_value", "return_type": "string", "summary": "The `true` filter value.\n\nReturns a value only for `boolean` type filters. Returns `null` for other types.", "summary_cn": "`true` 过滤值。\n\n仅为 `boolean` 类型过滤器返回一个值。为其他类型返回 `null`。"}, {"deprecated": false, "name": "type", "return_type": "string", "summary": "The type of the filter.\n\nPossible values:\n - boolean\n - list\n - price_range", "summary_cn": "过滤器的类型。\n\n可能的值：\n - boolean\n - list\n - price_range"}, {"deprecated": false, "name": "url_to_remove", "return_type": "string", "summary": "The current page URL with the filter's URL parameters removed.", "summary_cn": "删除了与过滤器相关的 URL 参数的当前页面 URL。"}, {"deprecated": false, "name": "values", "return_type": "array", "summary": "\nValues of the filter.\n\nThis array can only have values for `boolean` and `list` type filters.", "summary_cn": "过滤器的值。\n\n该数组只能具有 `boolean` 和 `list` 类型过滤器的值。"}], "summary": "Storefront filters.", "summary_cn": "店面过滤器。"}, {"deprecated": false, "description": "To learn about supporting filters in your theme, refer to Support [storefront filtering](https://developer.shopline.com/doc/hgr7vj9rpaah1a6m/vjd23her4ud8u3a7?version=v20250601).\n\n  ", "description_cn": "要了解在您的主题中支持过滤器，请参阅支持[店面过滤](https://developer.shopline.com/zh-hans-cn/doc/hgr7vj9rpaah1a6m/vjd23her4ud8u3a7/)。", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/filter-value", "name": "filter_value", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "active", "return_type": "boolean", "summary": "Returns `true` if the value is currently active. Returns `false` if not.\n\nCan only return `true` for filters of type `boolean` or `list`.", "summary_cn": "如果该值当前处于活动状态，则返回 `true`。如果不是，则返回 `false`。\n\n只能类型为 `boolean` 或 `list` 的过滤器返回 `true`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "label", "return_type": "string", "summary": "The customer-facing label for the filter value. For example, `Red` or `Rouge`.\n\nReturns a value only for `boolean` and `list` type filters. Returns `null` for `price_range` type filters.", "summary_cn": "过滤器值的面向客户的标签。例如，`Black` 或者 `Large`.\n\n仅为 `boolean` 和 `list` 类型过滤器返回一个值。为 `price_range` 类型过滤器返回 `null`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "param_name", "return_type": "string", "summary": "The URL parameter for the filter.\n\nFor example, `filter.v.availability`.\n\nFilters of type `price_range` include an extra component depending on whether the filter value is for the filter's `min_value` or `max_value`. The following table outlines the URL parameter for each:\n\n| Value type | URL parameter | \n\n| min_value | filter.v.price.gte |\n\n| max_value | filter.v.price.lte |\n", "summary_cn": "过滤器的 URL 参数。\n\n例如，`filter.v.availability`.\n\nprice_range 类型的过滤器包括一个额外的组件，具体取决于过滤器值是用于过滤器的 `min_value` 还是 `max_value`。下表概述了每个的 URL 参数：\n\n| Value type | URL parameter | \n\n| min_value | filter.v.price.gte |\n\n| max_value | filter.v.price.lte |"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url_to_add", "return_type": "string", "summary": "The current page URL with the filter value parameter added.", "summary_cn": "添加了过滤器值参数的当前页面 URL。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url_to_remove", "return_type": "string", "summary": "The current page URL with the URL parameter related to the filter removed.", "summary_cn": "删除了与过滤器相关的 URL 参数的当前页面 URL。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "value", "return_type": "string", "summary": "The value.", "summary_cn": "值。"}], "summary": "A specific value of a filter.", "summary_cn": "过滤器的特定值。\n\n"}, {"deprecated": false, "description": "Only exists in the [for](docs/sline/tag/for) loop.", "description_cn": "只有在 [for](docs/sline/tag/for) 循环中存在。", "examples": [{"raw_sline": "{{#var arr = \"one,two,three\" | split(\",\") /}}\n\n{{#for item in arr}}\n - {{ forloop.index }} {{ item }}\n{{/for}}"}, {"name": "Nested loops", "name_cn": "嵌套循环", "raw_sline": "{{#var arr1 = \"one,two,three\" | split(\",\") /}}\n{{#var arr2 = \"four,five,six\" | split(\",\") /}}\n\n{{#for item1 in arr1}}\n - parent: {{ forloop.index }} {{ item1 }}\n {{#for item2 in arr2}}\n     - nested: {{ forloop.index }} {{ item2 }} parent: {{forloop.parentloop.index}}\n {{/for}}\n{{/for}}"}], "link": "https://developer.shopline.com/docs/sline/object/forloop", "name": "forloop", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "first", "return_type": "boolean", "summary": "Returns true if the current iteration is the first. Returns false if not.", "summary_cn": "如果当前迭代是第一次，则返回 true。如果不是，则返回 false。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "index", "return_type": "number", "summary": "The 1-based index of the current iteration.", "summary_cn": "当前迭代的从 1 开始的索引。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "index0", "return_type": "number", "summary": "The 0-based index of the current iteration.", "summary_cn": "当前迭代的从 0 开始的索引。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "last", "return_type": "number", "summary": "Returns true if the current iteration is the last. Returns false if not.", "summary_cn": "如果当前迭代是最后一次，则返回 true。如果不是，则返回 false。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "length", "return_type": "number", "summary": "The total number of iterations in the loop.", "summary_cn": "循环中的总迭代次数。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "parentloop", "return_type": "forloop", "summary": "The parent forloop object.If the current for loop isn't nested inside another for loop, then null is returned.Use the parentloop property.", "summary_cn": "父 forloop 对象。如果当前 for 循环没有嵌套在另一个 for 循环中，则返回 null。使用 parentloop 属性。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "rindex", "return_type": "number", "summary": "The 1-based index of the current iteration, in reverse order.", "summary_cn": "当前迭代的从 1 开始的索引，按相反顺序排列。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "rindex0", "return_type": "number", "summary": "The 0-based index of the current iteration, in reverse order.", "summary_cn": "当前迭代的从 0 开始的索引，按相反顺序排列。"}], "summary": "Information about a parent for loop.", "summary_cn": "有关父级 for 循环的信息。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/fulfillment", "name": "fulfillment", "properties": [{"deprecated": false, "description": ":::tip{title=\"TIP\"}\nUtilize the date helper to format the timestamp.\n:::", "description_cn": ":::tip{title=\"提示\"}\n使用日期helper来格式化时间戳。\n:::", "name": "created_at", "return_type": "string", "summary": "Timestamp of the fulfillment creation.", "summary_cn": "履约创建时的时间戳。"}, {"array_return_type": "line_item", "deprecated": false, "name": "fulfillment_line_items", "return_type": "array", "summary": "Items involved in the fulfillment.", "summary_cn": "履约中的商品行。"}, {"deprecated": false, "name": "item_count", "return_type": "number", "summary": "Quantity of items in the fulfillment.", "summary_cn": "履约中的商品行数量。"}, {"deprecated": false, "name": "tracking_company", "return_type": "string", "summary": "Name of the fulfillment service.", "summary_cn": "履约服务的名称。"}, {"deprecated": false, "description": "If no tracking number exists, a null value is returned.", "description_cn": "如果没有跟踪号码，则返回空值。", "name": "tracking_url", "return_type": "string", "summary": "URL of the tracking number for the fulfillment.", "summary_cn": "履约的跟踪号码的URL。"}, {"deprecated": false, "description": "If no tracking number exists, a null value is returned.", "description_cn": "如果没有追踪编号，那么返回的是空值。", "name": "tracking_number", "return_type": "string", "summary": "The fulfillment's tracking number.", "summary_cn": "执行的追踪编号。"}], "summary": "Fulfillment of an order includes details of the items fulfilled and shipment tracking information.", "summary_cn": "一个订单的履约，包括被履约的商品行和发货跟踪等信息。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/gift-card", "name": "gift_card", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "balance", "return_type": "number", "summary": "The remaining balance of the gift card in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.\n\nTip: Use `money helpers` to output a formatted amount.", "summary_cn": "礼品卡剩余余额，以货币的子单位表示。\n\n该值以客户的本地（呈现）货币输出。\n\n提示：使用 `money helpers` 输出格式化的金额。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "currency", "return_type": "string", "summary": "The ISO code of the currency that the gift card was issued in.", "summary_cn": "礼品卡发行所用货币的 ISO 代码。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "customer_id", "return_type": "string", "summary": "The customer ID associated with the gift card.", "summary_cn": "与礼品卡关联的客户 ID。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "enabled", "return_type": "boolean", "summary": "Returns `true` if the gift card is enabled. Returns `false` if not.", "summary_cn": "如果礼品卡已启用，返回 `true`。如果没有，返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "expired", "return_type": "boolean", "summary": "Returns `true` if the gift card is expired. Returns `false` if not.", "summary_cn": "如果礼品卡已过期，返回 `true`。如果没有，返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "initial_value", "return_type": "number", "summary": "The initial balance of the gift card in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.\n\nTip: Use `money helpers` to output a formatted amount.", "summary_cn": "礼品卡的初始余额，以货币的子单位表示。\n\n该值以客户的本地（呈现）货币输出。\n\n提示：使用 `money helpers` 输出格式化的金额。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "last_four_characters", "return_type": "string", "summary": "The last 4 characters of the code used to redeem the gift card.", "summary_cn": "用于兑换礼品卡的代码的最后4个字符。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "product", "return_type": "product", "summary": "The product associated with the gift card.", "summary_cn": "与礼品卡关联的产品。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url", "return_type": "string", "summary": "The URL to view the gift card.\n\nTip: The page at this URL is rendered through the `gift_card.html` template of the theme.", "summary_cn": "查看礼品卡的URL。\n\n提示：此URL的页面通过主题的 `gift_card.html` 模板渲染。"}], "summary": "A gift card that's been issued to a customer or a recipient.", "summary_cn": "已经发放给客户或接收者的礼品卡。"}, {"deprecated": false, "description": "Visit the [SHOPLINE Help Center](https://help.shopline.com/hc/en-001) to learn about the image formats supported by SHOPLINE.", "description_cn": "了解 SHOPLINE 支持的图片格式，请访问[SHOPLINE帮助中心](https://help.shopline.com/hc/zh-cn)。", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/image", "name": "image", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "alt", "return_type": "string", "summary": "The alt text of the image.", "summary_cn": "图片的 alt 文本。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "aspect_ratio", "return_type": "number", "summary": "The aspect ratio of the image as a decimal.", "summary_cn": "图片的长宽比，以小数表示。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "attached_to_variant?", "return_type": "boolean", "summary": "Returns true if the image is associated with a variant. ", "summary_cn": "如果该 image 与 variant 关联，则返回 true。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "height", "return_type": "number", "summary": "The height of the image in pixels.", "summary_cn": "图片的高度，单位是像素。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "If you reference the id property for preview images of generic_file or media objects, then null is returned.", "description_cn": "如果您引用 id 属性generic_file或media对象的预览图片，则 null 返回。", "name": "id", "return_type": "number", "summary": "The ID of the image.", "summary_cn": "图片的ID。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "media_type", "return_type": "string", "summary": "The media type of the image. Always returns image.", "summary_cn": "图片的媒体类型。总是返回 image。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "position", "return_type": "number", "summary": "The position of the image in the `product.images` or `product.media` array.", "summary_cn": "图片在 `product.images` 或 `product.media` 数组中的位置。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "The preview_image property is only available for images accessed through the following sources:\n\n- [product.featured_media](/docs/sline/object/product)\n- [product.media](/docs/sline/object/product)\n\nIf you reference this property on an image from another source, then null is returned.", "description_cn": "preview_image属性仅适用于通过以下来源访问的图片：\n\n- [product.featured_media](/docs/sline/object/product)\n- [product.media](/docs/sline/object/product)\n\n如果你在另一个来源的 image 上引用这个属性，那么将返回 null。", "name": "preview_image", "return_type": "image", "summary": "A preview image for the image.", "summary_cn": "图片的预览图。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "The product_id property is only available for images associated with a product. If you reference this property on an image from another source, then 'null' is returned.", "description_cn": "product_id 属性只适用于与产品相关的图片。如果你在其他来源的图片上引用这个属性，那么会返回 null。", "name": "product_id", "return_type": "string", "summary": "The ID of the product that the image is associated with.", "summary_cn": "图片所关联的产品的ID。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "src", "return_type": "string", "summary": "The relative URL of the image.", "summary_cn": "图片的相对路径URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "width", "return_type": "number", "summary": "The width of the image in pixels.", "summary_cn": "图片的宽度，单位是像素。"}], "summary": "The image object, such as a collection or product image.", "summary_cn": "图片对象，如商品或系列图片 object."}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/line-item", "name": "line_item", "properties": [{"_internal_": {}, "array_return_type": "discount_allocation", "deprecated": false, "name": "discount_allocations", "return_type": "array", "summary": "The discount allocations that apply to the line item.", "summary_cn": "商品行的折扣分配。"}, {"_internal_": {}, "deprecated": false, "description": "The value is equal to `line_item.final_price` multiplied by `line_item.quantity`.\n\n:::tip{title=\"TIP\"} \nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": "此值等于 `line_item.final_price` 与 `line_item.quantity` 的乘积。\n\n:::tip{title=\"提示\"}\n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 格式化金额。 \n:::", "name": "final_line_price", "return_type": "string", "summary": "The combined price, of all of the items in the line item. This includes any line-level discounts.", "summary_cn": "\n商品行的价格合计。这包括了所有商品行级别折扣优惠。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"}\nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 格式化金额。 \n:::", "name": "final_price", "return_type": "string", "summary": "The price of the line item. This includes any line-level discounts.", "summary_cn": "商品行的商品单价。这包括了所有商品行级别折扣优惠。"}, {"_internal_": {}, "array_return_type": "fulfillment", "deprecated": false, "name": "fulfillments", "return_type": "array", "summary": "The fulfillment of the line item.", "summary_cn": "商品行的履约信息。"}, {"_internal_": {}, "deprecated": false, "name": "gift_card", "return_type": "boolean", "summary": "Returns `true` if the product associated with the line item is a gift card. Returns `false` if not.", "summary_cn": "如果与商品行关联的产品支持礼品卡，则返回 `true`。如果不是，则返回 `false`。"}, {"_internal_": {}, "deprecated": false, "description": "The image can come from one of the following sources:\n- The image of the variant associated with the line item.\n- The featured image of the product associated with the line item, if there's no variant image.", "description_cn": "图片可以来自以下资源：\n- 与商品行关联的商品 SKU 图片。\n- 与商品行关联的商品特色图片（如果没有商品 SKU 图片）。", "name": "image", "return_type": "image", "summary": "The image of the line item.", "summary_cn": "商品行的图片。"}, {"_internal_": {}, "deprecated": false, "description": "Line item keys are unique identifiers that consist of the following components separated by a colon:\n- The ID of the variant associated with the line item.\n- A hash of the properties of the line item, even if there are no properties.", "description_cn": "商品行的 key 是唯一标识符，由以下用冒号分隔的组件组成：\n- 与商品行关联的商品 SKU ID。 \n- 商品行的哈希值。", "name": "key", "return_type": "string", "summary": "The key of the line item.", "summary_cn": "商品行的 key。"}, {"_internal_": {}, "array_return_type": "discount_allocations", "deprecated": false, "name": "line_level_discount_allocations", "return_type": "array", "summary": "The discount allocations that apply directly to the line item.", "summary_cn": "直接应用于商品行的折扣。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"} \nUse [money helper](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7/) to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 [货币格式化hepler](https://developer.shopline.com/zh-hans-cn/doc/hdwvcvrgnfcjf4gq/b92qcan3w2wgzbe7?version=v20250601) 格式化金额。 \n:::", "name": "line_level_total_discount", "return_type": "number", "summary": "The total amount of any discounts applied to the line item.", "summary_cn": "商品行的折扣总额。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"NOTE\"} \nThe array is never empty because variants with no options still have a default option. Because of this, you should use `line_item.product.has_only_default_variant` to check whether there's any information to output. \n:::", "description_cn": ":::tip{title=\"提示\"}\n此数组永远不为空，因为尽管商品变体没有选项也会存在一个默认选项。基于此，你应该使用 `line_item.product.has_only_default_variant` 判断是否展示这些内容。 \n:::", "name": "options_with_values", "summary": "\nThe name and value pairs for each option of the variant associated with the line item.", "summary_cn": "商品行关联的变体的每个选项的名称和值。"}, {"_internal_": {}, "deprecated": false, "description": "The value is equal to `line_item.original_price` multiplied by `line_item.quantity`.\n\n:::tip{title=\"TIP\"} \nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": "此值等于 `line_item.original_price` 与 `line_item.quantity` 的乘积。\n\n:::tip{title=\"提示\"} \n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 格式化金额。 \n:::", "name": "original_line_price", "return_type": "string", "summary": "The combined price of all of the items in a line item, before any discounts have been applied.", "summary_cn": "应用折扣前的商品行的价格合计。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"} \nUse [money filters](https://developer.shopline.com/docs/sline/filter/money-with-currency) to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"提示\"} \n使用 [货币过滤器](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/money-with-currency) 格式化金额。 \n:::", "name": "original_price", "return_type": "string", "summary": "The price of the line item, before discounts have been applied.", "summary_cn": "应用折扣前的商品行单价。"}, {"_internal_": {}, "deprecated": false, "name": "product_id", "return_type": "string", "summary": "\nThe [ID](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/tfzgv1cp3z3wzk1s/#id) of the line item's product.", "summary_cn": "商品行的[商品 ID](https://developer.shopline.com/zh-hans-cn/doc/hdwvcvrgnfcjf4gq/tfzgv1cp3z3wzk1s?version=v20250601)。"}, {"_internal_": {}, "deprecated": false, "description": "You can add, or allow customers to add, custom information to a line item with line item properties. \n\nLine item properties consist of a name and value pair. They can be captured with the following methods:\n- A custom input inside a product form\n- The AJAX Cart API", "description_cn": "您可以手动添加或者让客户在商品行中添加自定义信息。\n\n商品行属性由名称和值对组成. 可以使用以下方法捕获它们：\n- 在商品表单中添加自定义的 input\n- 购物车 AJAX API", "name": "properties", "summary": "The properties of the line item.", "summary_cn": "商品行的属性。"}, {"_internal_": {}, "deprecated": false, "name": "quantity", "return_type": "number", "summary": "The quantity of the line item.", "summary_cn": "商品行的数量。"}, {"_internal_": {}, "deprecated": false, "name": "requires_shipping", "return_type": "boolean", "summary": "Returns `true` if the variant associated with the line item requires shipping. Returns `false` if not.", "summary_cn": "如果与商品行关联的变体需要运费，则返回 `true`。如果不是，则返回 `false`。"}, {"_internal_": {}, "deprecated": false, "name": "selling_plan_allocation", "return_type": "selling_plan_allocation", "summary": "The selling plan allocation of the line item. If the line item doesn't have a selling plan allocation, then `null` is returned.", "summary_cn": "商品行关联的销售计划。如果没有则返回 `null`。"}, {"_internal_": {}, "deprecated": false, "name": "sku", "return_type": "string", "summary": "The [sku](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/gjjjhvusthxd5ucy/#sku) of the variant associated with the line item.", "summary_cn": "与商品行关联的变体的 [sku](https://developer.shopline.com/zh-hans-cn/doc/hdwvcvrgnfcjf4gq/gjjjhvusthxd5ucy?version=v20250601)。"}, {"_internal_": {}, "deprecated": false, "name": "successfully_fulfilled_quantity", "return_type": "number", "summary": "The number of items from the line item that have been successfully fulfilled.", "summary_cn": "\n商品行中已成功履约数量。"}, {"_internal_": {}, "deprecated": false, "name": "taxable", "return_type": "boolean", "summary": "Returns true if taxes should be charged on the line item. Returns false if not.", "summary_cn": "如果商品需要税费则返回 true，否则返回 false。"}, {"_internal_": {}, "deprecated": false, "name": "url", "return_type": "string", "summary": "The relative URL of the variant associated with the line item.", "summary_cn": "与商品行关联的商品的相对 URL。"}, {"_internal_": {}, "deprecated": false, "name": "url_to_remove", "return_type": "string", "summary": "A URL to remove the line item from the cart.", "summary_cn": "用于从购物车中删除商品行的 URL。"}, {"_internal_": {}, "deprecated": false, "name": "variant_id", "return_type": "string", "summary": "\nThe [ID](https://developer.shopline.com/doc/hdwvcvrgnfcjf4gq/gjjjhvusthxd5ucy?version=v20250601) of the line item's variant.", "summary_cn": "商品行关联的[商品变体 ID](https://developer.shopline.com/zh-hans-cn/doc/hdwvcvrgnfcjf4gq/gjjjhvusthxd5ucy?version=v20250601)。"}, {"_internal_": {}, "deprecated": false, "name": "vendor", "return_type": "string", "summary": "The vendor of the variant associated with the line item.", "summary_cn": "与商品行关联的商品供应商。"}], "summary": "\nA line in a cart, checkout, or order. Each line item represents a product variant.", "summary_cn": "购物车、结帐或订单中的每一行商品数据详情。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/link", "name": "link", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": ":::tip{title=\"Tip\"}\nThe link.active property is useful for designing the appearance of the currently selected menu navigation. For instance, if a customer is viewing an article in the \"Tag\" blog, the \"Tag\" link would be highlighted in the menu.\n:::", "description_cn": ":::tip{title=\"Tip\"}\nlink.active属性对用于在查看当前选中菜单导航时的设计很有用。例如，如果客户正在查看“Tag”博客的文章，则“Tag”链接会在菜单中突出显示。\n:::", "name": "active", "return_type": "boolean", "summary": "Returns true if the link is active. Returns false if not.A link is considered to be active if the current URL path matches, or contains, the link's url. For example, if the current URL path is /blog/potion-notions/new-potions-for-spring, then the following link URLs would be considered active:\n- /blog/potion-notions/new-potions-for-spring\n- /blog/potion-notions\n", "summary_cn": "如果链接是活动状态，返回true。否则返回 false。\n\n如果当前的URL路径与链接的url相匹配，则该链接被认为是激活的。例如，如果当前的URL路径是 /blog/tag/cutlery 。下面的链接URL将被认为是激活的：\n- /blog/tag/cutlery\n- /blog/tag"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "child_active", "return_type": "boolean", "summary": "Returns true if a link's child link is active. Returns false if not.A link is considered to be active if the current URL path matches, or contains, the URL of the link.For example, if the current URL path is /blog/potion-notions/new-potions-for-spring, then the following link URLs would be considered active:\n- /blog/potion-notions/new-potions-for-spring\n- /blog/potion-notions\n", "summary_cn": "如果链接的子链接处于活动状态，返回 true。否则返回 false。\n\n如果当前的URL路径匹配或包含链接的url，则该链接被认为是激活的的。\n\n例如，如果当前 URL 路径是/blog/tag/cutlery，则以下链接 URL 将被视为激活的：\n\n- /blog/tag/cutlery\n- /blog/tag"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": ":::tip{title=\"Tip\"}\nIgnore parameters when matching URLs. Product URLs within a collection context using the within helper are treated the same as standard product URLs.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n在匹配URL时忽略参数，使用within这个helper的分类上下文商品URL和标准的商品URL被视为相同。\n:::", "name": "child_current", "return_type": "boolean", "summary": "Returns true if the current URL path aligns with any of the link's child URLs. Otherwise, it returns false.", "summary_cn": "如果当前的URL路径与链接的子链接URL相匹配，则返回 true。否则返回 false。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": ":::tip{title=\"Tip\"}\nIgnore parameters when matching URLs. Product URLs within a collection context using the within helper are treated the same as standard product URLs.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n在匹配URL时忽略参数，使用within这个helper的分类上下文商品URL和标准的商品URL被视为相同。\n:::", "name": "current", "return_type": "boolean", "summary": "Returns true if the current URL path corresponds to the link's URL. Otherwise, it returns false.", "summary_cn": "如果当前的URL路径与链接的URL匹配，返回true。否则返回false。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "handle", "return_type": "string", "summary": "The [handle](docs/sline/object/handle) of the link.", "summary_cn": "链接的 [handle](docs/sline/object/handle) 。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "levels", "return_type": "number", "summary": "The quantity of nested levels beneath the link.", "summary_cn": "链接下的嵌套层数量."}, {"_internal_": {}, "array_return_type": "link", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "links", "return_type": "array", "summary": "The child links of the link.", "summary_cn": "链接的子链接。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "title", "return_type": "string", "summary": "The title of the link.", "summary_cn": "链接的标题。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "Valid property values include:\n- article_link:The link directs to an article.\n- blog_link:The link directs to a blog collection.\n- catalog_link:The link directs to all product collections.\n- collection_link:The link directs to a product collection.\n- collections_link:The link directs to a collection list page. [Collection list page](https://help.shopline.com/hc/articles/14395898015513-%E5%95%86%E5%93%81%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86%E4%B8%8E%E8%AE%BE%E8%AE%A1).\n- frontpage_link:The link directs to the homepage.\n- http_link:The link directs to an external webpage.\n- page_link:The link directs to a [custom page](https://help.shopline.com/hc/articles/************-%E8%87%AA%E5%AE%9A%E4%B9%89%E9%A1%B5%E9%9D%A2).\n- policy_link:The link directs to a [policy page](https://help.shopline.com/hc/articles/4406084105369-%E6%B7%BB%E5%8A%A0%E5%95%86%E5%BA%97%E6%94%BF%E7%AD%96).\n- product_link:The link directs to a product page.\n- search_link:The link directs to a search page.", "description_cn": "有效属性值包括:\n- article_link:该链接指向一篇文章。\n- blog_link:该链接指向一个博客集合。\n- catalog_link:该链接指向所有商品集合\n- collection_link:链接指向一个商品分类。\n- collections_link:链接指向一个[商品分类集合](https://help.shopline.com/hc/articles/14395898015513-%E5%95%86%E5%93%81%E5%88%86%E7%B1%BB%E7%AE%A1%E7%90%86%E4%B8%8E%E8%AE%BE%E8%AE%A1)。\n- frontpage_link:链接指向主页。\n- http_link:该链接指向一个外部网页。\n- page_link:链接指向一个[自定义页面](https://help.shopline.com/hc/articles/************-%E8%87%AA%E5%AE%9A%E4%B9%89%E9%A1%B5%E9%9D%A2)。\n- policy_link:链接指向一个[政策页面](https://help.shopline.com/hc/articles/4406084105369-%E6%B7%BB%E5%8A%A0%E5%95%86%E5%BA%97%E6%94%BF%E7%AD%96)。\n- product_link:链接指向商品页面。\n- search_link:链接指向搜索页面。", "name": "type", "return_type": "string from a set of values", "summary": "The type of the link.", "summary_cn": "链接的类型。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url", "return_type": "string", "summary": "The URL of the link.", "summary_cn": "链接的 URL。"}], "summary": "[Menu navigation](https://help.shopline.com/hc/articles/4402190682137-%E8%8F%9C%E5%8D%95%E5%AF%BC%E8%88%AA%E8%AE%BE%E5%AE%9A) links.", "summary_cn": "[菜单导航](https://help.shopline.com/hc/articles/4402190682137-%E8%8F%9C%E5%8D%95%E5%AF%BC%E8%88%AA%E8%AE%BE%E5%AE%9A)中的链接。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/linklist", "name": "linklist", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "handle", "return_type": "string", "summary": "The [handle](/docs/sline/object/handle) of the menu navigation link.", "summary_cn": "菜单导航链接的 [handle](/docs/sline/object/handle)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": ":::tip{title=\"Tip\"}\nThere can be up to 3 levels.\n:::", "description_cn": ":::tip{title=\"Tip\"}\n最多有 3 个级别。\n:::", "name": "levels", "return_type": "number", "summary": "The quantity of hierarchical levels in the menu.", "summary_cn": "菜单中的嵌套级别数。"}, {"_internal_": {}, "array_return_type": "link", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "links", "return_type": "array", "summary": "The sub-menus in the menu navigation can be nested up to 3 levels.", "summary_cn": "菜单导航中的子级菜单，可嵌套3层。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "title", "return_type": "string", "summary": "The title of the menu.", "summary_cn": "菜单的标题。"}, {"_internal_": {}, "deprecated": false, "name": "id", "return_type": "string", "summary": "The unique ID of the navigation.", "summary_cn": "导航唯一标识 ID。"}, {"_internal_": {}, "deprecated": false, "name": "url", "return_type": "string", "summary": "Resource jump link.", "summary_cn": "资源跳转链接。"}], "summary": "[Menu navigation](https://help.shopline.com/hc/articles/4402190682137-%E8%8F%9C%E5%8D%95%E5%AF%BC%E8%88%AA) links.", "summary_cn": "[菜单导航](https://help.shopline.com/hc/articles/4402190682137-%E8%8F%9C%E5%8D%95%E5%AF%BC%E8%88%AA) 的链接。"}, {"deprecated": false, "description": "Can be used in all templates, using `all_products` object directly will not output data, you need to use it with [get filter](/docs/sline/filter/get).", "description_cn": "可以在全部模版内使用，直接使用 `all_products` object 不会输出数据，需要结合 [get filter](/docs/sline/filter/get) 使用。", "examples": [{"description": "Get [product object](/docs/sline/object/product) by specifying the product handle", "description_cn": "通过指定商品 Handle 获取 [product object](/docs/sline/object/product)", "name": "Get the specified product", "name_cn": "获取指定商品", "raw_sline": "{{#var product = all_products | get(\"earring-ab001\") /}}\n{{#link_to product.url}}\n  {{product.title}}\n{{/link_to}}"}], "link": "https://developer.shopline.com/docs/sline/object/all-products", "name": "all_products", "properties": [], "summary": "All products in the store.", "summary_cn": "店铺的全部商品。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/order", "name": "order", "properties": [{"_internal_": {}, "deprecated": false, "name": "attributes", "summary": "The attributes on the order. If there are no attributes on the order, then `null` is returned.", "summary_cn": "订单上的属性。 如果订单上没有属性，则返回 `null`。"}, {"_internal_": {}, "deprecated": false, "name": "billing_address", "return_type": "address", "summary": "The billing address of the order.", "summary_cn": "订单的账单地址。"}, {"_internal_": {}, "deprecated": false, "name": "cancel_reason", "return_type": "string", "summary": "The reason that the order was cancelled.", "summary_cn": "订单被取消的原因。"}, {"_internal_": {}, "deprecated": false, "name": "cancelled", "return_type": "boolean", "summary": "Returns `true` if the order was cancelled. Returns `false` if not.", "summary_cn": "如果订单被取消，则返回 `true`。如果不是，则返回 `false`。"}, {"_internal_": {}, "deprecated": false, "name": "cancelled_at", "return_type": "string", "summary": "A timestamp of the order cancellation time.", "summary_cn": "订单取消时间的时间戳。"}, {"_internal_": {}, "deprecated": false, "name": "created_at", "return_type": "string", "summary": "A timestamp of the order creation time.", "summary_cn": "订单创建时间的时间戳。"}, {"_internal_": {}, "deprecated": false, "name": "customer_url", "return_type": "string", "summary": "The URL for the customer to view the order in their account.", "summary_cn": "客户在其帐户中查看订单的 URL。"}, {"_internal_": {}, "deprecated": false, "name": "email", "return_type": "string", "summary": "The email that's associated with the order. If no email is associated with the order, then `null` is returned.", "summary_cn": "与订单关联的电子邮件。 如果没有电子邮件与订单关联，则返回 `null`。"}, {"_internal_": {}, "deprecated": false, "name": "financial_status", "return_type": "number", "summary": "The order's financial status.", "summary_cn": "订单的财务状况。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"} \nUse this property to output the financial status on the storefront.\n:::", "description_cn": ":::tip{title=\"提示\"} \n使用此属性输出商店的财务信息。 \n:::", "name": "financial_status_label", "return_type": "string", "summary": "The localized version of the financial status of the order.", "summary_cn": "订单财务状况的本地化版本。"}, {"_internal_": {}, "deprecated": false, "name": "fulfillment_status", "return_type": "number", "summary": "The fulfillment status of the order.", "summary_cn": "订单的履约状态。"}, {"_internal_": {}, "deprecated": false, "name": "fulfillment_status_label", "return_type": "string", "summary": "The localized version of the fulfillment status of the order.", "summary_cn": "订单履约状态的本地化版本。"}, {"_internal_": {}, "deprecated": false, "name": "id", "return_type": "string", "summary": "The ID of the order.", "summary_cn": "订单 ID。"}, {"_internal_": {}, "deprecated": false, "name": "item_count", "return_type": "number", "summary": "The number of items in the order.", "summary_cn": "订单中的商品数量。"}, {"_internal_": {}, "array_return_type": "line_item", "deprecated": false, "name": "line_items", "return_type": "array", "summary": "The line items in the order.", "summary_cn": "订单中的商品信息数组。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"} \nUse `money_with_currency` helper to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"提示\"} \n使用 `money_with_currency` helper 输出格式化的金额。 \n:::", "name": "line_items_subtotal_price", "return_type": "number", "summary": "The sum of the prices of all of the line items in the order in the currency's subunit, after any line item discounts have been applied. The value is output in the customer's local (presentment) currency.", "summary_cn": "在应用任何订单项折扣后，订单中所有订单项的价格总和。 该值以客户的本地货币格式输出。"}, {"_internal_": {}, "deprecated": false, "name": "name", "return_type": "string", "summary": "The name of the order.", "summary_cn": "订单名称。"}, {"_internal_": {}, "deprecated": false, "name": "note", "return_type": "string", "summary": "The note on the order. If there's no note on the order, then `null` is returned.", "summary_cn": "订单上的注释。 如果订单上没有备注，则返回 `null`。"}, {"_internal_": {}, "deprecated": false, "name": "order_number", "return_type": "string", "summary": "The integer string representation of the order name.", "summary_cn": "订单名称的整数字符串表示形式。"}, {"_internal_": {}, "deprecated": false, "name": "order_status_url", "return_type": "string", "summary": "The URL for the [order status page](https://help.shopline.com/hc/en-001/articles/14266668346137-Orders-Overview) for the order.", "summary_cn": "订单的 [订单状态页面](https://help.shopline.com/hc/zh-cn/articles/14266668346137-%E8%AE%A2%E5%8D%95%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D) 的 URL。"}, {"_internal_": {}, "deprecated": false, "name": "shipping_address", "return_type": "address", "summary": "The shipping address of the order.", "summary_cn": "订单的送货地址。"}, {"_internal_": {}, "array_return_type": "shipping_method", "deprecated": false, "name": "shipping_methods", "return_type": "array", "summary": "The shipping address of the order.", "summary_cn": "订单的送货方式。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"} \nUse `money_with_currency` helper to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 `money_with_currency` helper 输出格式化的金额。 \n:::", "name": "shipping_price", "return_type": "number", "summary": "The shipping price of the order in the currency's subunit. The value is output in the customer's local (presentment) currency.", "summary_cn": "订单运费。该值以客户的本地货币格式输出。"}, {"_internal_": {}, "array_return_type": "string", "deprecated": false, "name": "tags", "return_type": "array", "summary": "The tags on the order. The tags are returned in alphabetical order.", "summary_cn": "订单上的标签列表。 标签按字母顺序返回。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"}\nUse `money_with_currency` helper to output a formatted amount. \n:::", "description_cn": "\n:::tip{title=\"提示\"} \n使用 `money_with_currency` helper 输出格式化的金额。 \n:::", "name": "tax_price", "return_type": "number", "summary": "The total amount of taxes applied to the order in the currency's subunit. The value is output in the customer's local (presentment) currency.", "summary_cn": "订单的总税额。 该值以客户的本地货币格式输出。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"} \nUse `money_with_currency` helper to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 `money_with_currency` helper 输出格式化的金额。 \n:::", "name": "total_discounts", "return_type": "number", "summary": "The total amount of all discounts applied to the order in the currency's subunit. The value is output in the customer's local (presentment) currency.", "summary_cn": "订单的所有折扣的总金额。该值以客户的本地货币格式输出。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"TIP\"} \nUse `money_with_currency` helper to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"提示\"} \n使用 `money_with_currency` helper 输出格式化的金额。 \n:::", "name": "total_net_amount", "return_type": "number", "summary": "The net amount of the order in the currency's subunit. The value is output in the customer's local (presentment) currency.", "summary_cn": "订单净额。该值以客户的本地货币格式输出。"}, {"_internal_": {}, "deprecated": false, "description": ":::tip{title=\"NOTE\"} \nThe total price is calculated before refunds are applied. Use `order.total_net_amount` to output the total minus any refunds. \n:::\n\nThe value is output in the customer's local (presentment) currency.\n\n:::tip{title=\"TIP\"} \nUse `money_with_currency` helper to output a formatted amount. \n:::", "description_cn": ":::tip{title=\"备注\"} \n总价在退款前计算。使用 `order.total_net_amount` 输出总额减去任何退款。 \n:::\n\n该值以客户的本地货币格式输出。\n\n:::tip{title=\"提示\"} \n使用 `money_with_currency` helper 输出格式化的金额。 \n:::", "name": "total_price", "return_type": "number", "summary": "The total price of the order in the currency's subunit.", "summary_cn": "订单总价。该值以客户的本地货币格式输出。"}], "summary": "An [order](https://help.shopline.com/hc/en-001/articles/14266668346137-Orders-Overview).", "summary_cn": "一个 [订单](https://help.shopline.com/hc/zh-cn/articles/14266668346137-%E8%AE%A2%E5%8D%95%E5%8A%9F%E8%83%BD%E4%BB%8B%E7%BB%8D)。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/page", "name": "page", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "content", "return_type": "string", "summary": "The content of the page.", "summary_cn": "页面的内容。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "handle", "return_type": "string", "summary": "The [handle](/docs/sline/object/handle) of the page.", "summary_cn": "页面的 [handle](/docs/sline/object/handle)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The ID of the page.", "summary_cn": "页面的 ID。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": ":::tip{title=\"Tip\"}\nUse the [date filter](/docs/sline/filter/date) to format the timestamp.\n:::\n\n", "description_cn": ":::tip{title=\"Tip\"}\n可以使用 [date helper](/docs/sline/filter/date) 去格式化这个时间戳。\n:::\n", "name": "published_at", "return_type": "string", "summary": "A timestamp for when the page was published.", "summary_cn": "页面的发布时间，一个时间戳。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "template_suffix", "return_type": "string", "summary": "The page template name of the current custom page application, for example, page.xxx.json, then xxx is returned. If the default template is used, null is returned.", "summary_cn": "当前自定义页面应用的页面模板名称，例如 page.xxx.json，则返回 xxx。如果使用默认模板，则返回 null。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "title", "return_type": "string", "summary": "The title of the page.", "summary_cn": "页面的标题。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url", "return_type": "string", "summary": "The relative URL of the page.", "summary_cn": "页面的url。"}, {"_internal_": {}, "deprecated": false, "name": "author", "return_type": "string", "summary": "Author account ID.", "summary_cn": "作者账号 ID。"}], "summary": "A [page](https://help.shopline.com/hc/articles/************-%E8%87%AA%E5%AE%9A%E4%B9%89%E9%A1%B5%E9%9D%A2) on a store.", "summary_cn": "一个店铺的 [自定义页面](https://help.shopline.com/hc/articles/************-%E8%87%AA%E5%AE%9A%E4%B9%89%E9%A1%B5%E9%9D%A2)。"}, {"deprecated": false, "description": "Product and collection pages, and blog post descriptions will be used. For all other pages, or pages without descriptions, the descriptions in the store's SEO settings will be used. The `page_description` object can be used to provide a brief description of a page for search engine listings and social media previews.", "description_cn": "产品和集合页面，以及博客帖子的描述将被使用。对于所有其他页面，或者没有描述的页面，将使用店铺 SEO 设置里的描述。`page_description` 对象可用于为搜索引擎列表和社交媒体预览提供简短的页面描述。\n\n", "examples": [{"description": "Create different Open Graph `og:description` meta tags for different pages, specifying the description information for the site page to be displayed when the site is shared on social media.", "description_cn": "为不同页面创建不同的 Open Graph `og:description` 元标签，当站点被分享到社交媒体时，指定展示站点页面的描述信息。", "name": "Specify the description to display when the site is shared on social media", "name_cn": "指定站点分享到社交媒体时显示的描述", "raw_sline": "<meta property=\"og:description\" content=\"{{page_description}}\" />"}], "link": "https://developer.shopline.com/docs/sline/object/page-description", "name": "page_description", "properties": [], "summary": "The description used to display in search engine listings and in the social media preview of the current page.", "summary_cn": "用于在搜索引擎列表和当前页面的社交媒体预览中显示的描述。"}, {"deprecated": false, "description": "Featured images will be used for product and collection pages, and blog posts. For all other pages, or pages without a featured image, the social share image will be used, or the store logo if no social share image is available. ", "description_cn": "产品和集合页面，以及博客帖子的特色图片将被使用。对于所有其他页面，或者没有特色图片的页面，将使用社交分享图片，如果没有社交分享图片的话则使用店铺 logo。", "examples": [{"description": "Create different Open Graph `og:image` meta tags for different pages, and specify a certain image to show a site page thumbnail when the site is shared on social media.", "description_cn": "为不同页面创建不同的 Open Graph `og:image` 元标签，当站点被分享到社交媒体时，指定某张图片展示站点页面缩略图。", "name": "Specify the image that is displayed when the site is shared on social media", "name_cn": "指定站点分享到社交媒体时显示的图片", "raw_sline": "{{#if page_image.src}}\n  <meta property=\"og:image\" content=\"{{page_image.src}}\" />\n  <meta property=\"og:image:secure_url\" content=\"{{page_image.src}}\" />\n  <meta property=\"og:image:width\" content=\"{{page_image.width}}\" />\n  <meta property=\"og:image:height\" content=\"{{page_image.height}}\" />\n{{/if}}\n"}], "link": "https://developer.shopline.com/docs/sline/object/page-image", "name": "page_image", "properties": [], "summary": "An image to be shown in search engine listings and social media previews for the current page.", "summary_cn": "用于在搜索引擎列表和当前页面的社交媒体预览中显示的图片。"}, {"deprecated": false, "description": "The `page_title` object can be used to specify the title of a page for search engine listings and social media previews, and will output the name of the store on pages without a resource handle.", "description_cn": "`page_title` 对象可以用来为搜索引擎列表和社交媒体预览指定页面的标题，在没有资源 handle 的页面会输出店铺名称。\n", "examples": [{"description": "Tell social media what page title to display by setting the `og:title` field of the `meta` tag.", "description_cn": "通过设置 `meta` 标签的 `og:title` 字段，告诉 社交媒体应展示什么页面标题。", "name": "Display different website titles for different pages on social media", "name_cn": "在社交媒体上为不同页面展示不同的网站标题", "raw_sline": "<meta property=\"og:title\" content=\"{{page_title}}\" />", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/object/page-title", "name": "page_title", "properties": [], "summary": "The title of the current page.", "summary_cn": "当前页面的标题。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/policy", "name": "policy", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The ID of the policy.", "summary_cn": "政策的 ID。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "body", "return_type": "string", "summary": "The content of the policy.", "summary_cn": "政策的内容。"}, {"deprecated": false, "name": "title", "summary": "The title of the policy page.", "summary_cn": "政策页的标题。"}, {"deprecated": false, "name": "url", "return_type": "string", "summary": "The path to the policy page.", "summary_cn": "政策页的路径。"}], "summary": "[Store policies](https://help.shopline.com/hc/articles/4406084105369-%E6%B7%BB%E5%8A%A0%E5%95%86%E5%BA%97%E6%94%BF%E7%AD%96), such as privacy policy or return policy.", "summary_cn": "[商店政策](https://help.shopline.com/hc/articles/4406084105369-%E6%B7%BB%E5%8A%A0%E5%95%86%E5%BA%97%E6%94%BF%E7%AD%96)，例如隐私政策或退货政策。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/powered-by-link", "name": "powered_by_link", "properties": [], "summary": "Generates an HTML link element that directs to the localized version of shopline.com according to the store's regional settings.", "summary_cn": "根据商店的地区设置，创建一个 HTML 链接元素，链接到 shopline.com 的本地化版本。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/product-option", "name": "product_option", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "name", "return_type": "string", "summary": "The name of the product option.", "summary_cn": "商品规格的名称。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "position", "return_type": "number", "summary": "The product option's position within the `product.options_with_values` array.", "summary_cn": "商品规格在 `product.options_with_values` 数组里面的索引。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "values", "return_type": "array", "summary": "The possible values for the product option.", "summary_cn": "商品规格中可能包含的值。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "selected_value", "return_type": "string", "summary": "The currently selected product option value.", "summary_cn": "当前选择的商品规格值。"}], "summary": "A product option, such as size or color.", "summary_cn": "商品规格，例如尺寸或颜色。"}, {"deprecated": false, "description": "Product recommendations become more accurate over time as new orders and product data become available. To learn more about how product recommendations are generated, refer to Product recommendations.", "description_cn": "随着新订单和产品数据的可用性增加，产品推荐会随着时间变得更准确。要了解更多关于产品推荐如何生成的信息，请参阅产品推荐。", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/recommendations", "name": "recommendations", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "performed", "return_type": "boolean", "summary": "Returns `true` when being referenced inside a section that's been rendered using the Product Recommendations API and the Section Rendering API. Returns `false` if not.", "summary_cn": "当在使用产品推荐 API 和区段渲染 API 渲染的区段内被引用时，返回 `true`。否则返回 `false`。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "The recommended products.", "description_cn": "", "name": "products", "return_type": "array", "summary": "The recommended products.", "summary_cn": "推荐的产品。"}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "products_count", "return_type": "number", "summary": "The number of recommended products.", "summary_cn": "推荐产品的数量。"}], "summary": "Product recommendations for a specific product based on sales data, product descriptions, and collection relationships.", "summary_cn": "基于销售数据、产品描述和集合关系为特定产品提供的产品推荐。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [{"description": "The `request.design_mode` is used to determine if you are currently in the Admin side of the online store's design, which is used to program the component's display behavior in different access modes.", "description_cn": "通过 `request.design_mode` 来判断当前是否处于 Admin 端的在线商店装修设计，以此来编写组件在不同访问模式下的显示行为。", "name": "Determine if user access is in editor mode", "name_cn": "判断用户访问是否在编辑器模式", "path": "", "raw_sline": "{{#if request.design_mode}}\nNow in online store design mode\n{{#else/}}\nNow in front-end user access mode\n{{/if}}", "source_object": ""}], "link": "https://developer.shopline.com/docs/sline/object/request", "name": "request", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "You can use `request.design_mode` to control theme behavior depending on whether the theme is being viewed in the editor. For example, you can prevent session data from being tracked by tracking scripts in the theme editor.", "description_cn": "您可以使用 `request.design_mode` 来控制主题的行为，具体取决于主题在编辑器中的查看情况。例如，您可以阻止在主题编辑器中跟踪脚本来跟踪会话数据。", "name": "design_mode", "return_type": "boolean", "summary": "Returns `true` if the request is being made from within the theme editor. Returns `false` if not.", "summary_cn": "如果请求是在主题编辑器中进行的，则返回 `true`。否则返回 `false`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "host", "return_type": "string", "summary": "The domain that the request is hosted on.", "summary_cn": "请求所在的域名。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "origin", "return_type": "string", "summary": "The protocol and host of the request.", "summary_cn": "请求的协议和主机。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "page_type", "return_type": "string", "summary": "The type of page being requested.", "summary_cn": "请求页面的类型。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "path", "return_type": "string", "summary": "The path of the request.\n\n", "summary_cn": "请求的页面路径。"}, {"_internal_": {}, "deprecated": false, "name": "document_direction", "return_type": "string", "summary": "Page document content orientation.\n", "summary_cn": "页面文档内容方向。"}], "summary": "Information about the current URL and associated pages can be used to output information about the current user's request to access the site.", "summary_cn": "当前URL和关联页面的信息，可以用来输出当前用户访问网站的请求信息。"}, {"deprecated": false, "description": "Using the `routes` object instead of hardcoding URLs helps ensure that your theme supports multiple languages, as well as any possible changes in URL format.", "description_cn": "使用 `routes` 对象而不是硬编码 URL 有助于确保您的主题支持多语言，以及适应未来URL格式任何可能的更改。", "examples": [{"description": "The `a` tag for the cart page can be generated by using `routes.cart_url`.", "description_cn": "可以通过使用 `routes.cart_url` 来生成购物车页的 `a` 标签。", "name": "Jump to cart page", "name_cn": "跳转到购物车页", "raw_sline": "{{#link_to routes.cart_url}}\n  cart page\n{{/link_to}}"}], "link": "https://developer.shopline.com/docs/sline/object/routes", "name": "routes", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "account_address_new_url", "return_type": "string", "summary": "The account address creation page URL.", "summary_cn": "用户地址创建页面URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "account_login_url", "return_type": "string", "summary": "The account login page URL.", "summary_cn": "用户登录页面URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "account_logout_url", "return_type": "string", "summary": "The URL to log a customer out of their account. Redirects to account login page when enabled.", "summary_cn": "用于让客户登出其帐户的URL.访问后将重定向到登陆页面。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "account_order_list_url", "return_type": "string", "summary": "The account order list page URL.", "summary_cn": "用户订单列表页面URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "account_recover_url", "return_type": "string", "summary": "The password recovery page URL.", "summary_cn": "用户重置密码页面URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "account_register_url", "return_type": "string", "summary": "The account registration page URL.", "summary_cn": "用户注册页面URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "account_url", "return_type": "string", "summary": "The account page URL.", "summary_cn": "用户个人中心页面URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "all_products_collection_url", "return_type": "string", "summary": "The all-products collection page URL.", "summary_cn": "所有商品集合页面。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "cart_add_url", "return_type": "string", "summary": "The URL for the cart add.", "summary_cn": "购物车加购 API URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "cart_change_url", "return_type": "string", "summary": "The URL for the cart change.", "summary_cn": "购物车修改API URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "cart_update_url", "return_type": "string", "summary": "The URL for the cart update.", "summary_cn": "购物车更新API URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "cart_url", "return_type": "string", "summary": "The cart page URL.", "summary_cn": "购物车页 URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "collections_url", "return_type": "string", "summary": "The collection list page URL.", "summary_cn": "分类列表页 URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "root_url", "return_type": "string", "summary": "The index (home page) URL.", "summary_cn": "主页URL。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "search_url", "return_type": "string", "summary": "The search page URL.", "summary_cn": "搜索页 URL。"}], "summary": "Allows you to generate standard URLs for the storefront.", "summary_cn": "允许您在主题代码中使用标准 URL。"}, {"deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "To learn about storefront search and how to include it in your theme, refer to Storefront search.", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/search", "name": "search", "properties": [{"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "filters", "return_type": "array", "summary": "The filters that have been set up on the search page.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "performed", "return_type": "boolean", "summary": "Returns true if a search was successfully performed. Returns false if not.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "results", "return_type": "", "summary": "The search result items.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "results_count", "return_type": "number", "summary": "The number of results.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "sort_by", "return_type": "", "summary": "The sort order of the search results. This is determined by the sort_by URL parameter.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "sort_options", "return_type": "string", "summary": "The available sorting options for the search results.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "terms", "return_type": "string", "summary": "The entered search terms.", "summary_cn": ""}, {"array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "types", "return_type": "array", "summary": "The object types that the search was performed on.", "summary_cn": ""}], "summary": "Information about a storefront search query.", "summary_cn": ""}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/section", "name": "section", "properties": [{"_internal_": {}, "deprecated": false, "description": "The ID for sections included through JSON templates are dynamically generated by Shopline.\n\nThe ID for static sections is the section file name without the `.html` extension. For example, a `rich-text.html` section has an ID of `rich-text`.", "description_cn": "通过JSON模板包含的部分的ID是由Shopline动态生成的。\n\n此ID可以标识静态 section 文件 `.html`，举个例子，`rich-text.html` 的 ID 为 `rich-text`.", "name": "id", "return_type": "string", "summary": "The ID of the section.", "summary_cn": "Section的ID"}, {"_internal_": {}, "deprecated": false, "name": "index", "return_type": "number", "summary": "The 1-based index of the current section within its location.", "summary_cn": "当前部分在其位置内的从 1 开始的索引。"}, {"_internal_": {}, "deprecated": false, "name": "index0", "return_type": "number", "summary": "The 0-based index of the current section within its location.\n", "summary_cn": "当前部分在其位置内的基于 0 的索引。"}, {"_internal_": {}, "deprecated": false, "description": "The type for sections is the section file name without the `.html` extension. For example, a `rich-text.html` section has an type of `rich-text`.", "description_cn": "此ID可以标识 section 文件 `.html`，举个例子，`rich-text.html` 的 type 为 `rich-text`.", "name": "type", "return_type": "string", "summary": "The type of the section.", "summary_cn": "section 的类型。"}, {"_internal_": {}, "deprecated": false, "name": "blocks_size", "return_type": "number", "summary": "The number of sub-blocks under the current section.\n", "summary_cn": "当前 section 下的子 block 的数量。"}, {"_internal_": {}, "deprecated": false, "name": "settings", "return_type": "untyped", "summary": "The settings of the section.", "summary_cn": "section 的 settings 配置。"}], "summary": "The properties and settings of section can only be used in html files under the `/sections` folder to read the configuration of `section`.", "summary_cn": "section 的属性和设置，只能在 `/sections` 文件夹下的 html 文件使用该局部 object，以方便读取 `section` 的配置。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [{"description": "By determining the globally configured `cart_add_type` to execute different cart opening methods, it is convenient for the same theme code to meet different interaction requirements.", "description_cn": "通过判断全局配置的 `cart_add_type` 来执行不同的购物车打开方式，方便同个主题代码满足不同的交互需求。", "name": "How the shopping cart opens after adding a purchase", "name_cn": "加购后购物车的打开方式", "raw_sline": "{{#if settings.cart_add_type == \"drawer\"}}\nOpen the drawer cart.\n{{#else/}}\nJump to cart page.\n{{/if}}"}], "link": "https://developer.shopline.com/docs/sline/object/settings", "name": "settings", "properties": [], "summary": "You can access the theme's global settings via the `theme.config.json` file.", "summary_cn": "您可以根据 `theme.config.json` 文件访问主题的全局设置。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/shipping-method", "name": "shipping_method", "properties": [{"deprecated": false, "name": "id", "return_type": "string", "summary": "The identifier for the shipping method.", "summary_cn": "订单运输号 ID。"}, {"deprecated": false, "description": ":::tip{title=\"TIP\"}\nUse the `money_with_currency` helper to output formatted amounts.\n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 `money_with_currency helper` 输出格式化的金额。\n:::\n", "name": "original_price", "return_type": "number", "summary": "Shipping price before applying discounts. This amount is presented in the currency format specific to the customer.", "summary_cn": "应用折扣前运输价格。该值以客户的本地货币格式输出。"}, {"deprecated": false, "description": ":::tip{title=\"TIP\"}\nUse `money_with_currency` helper to output a formatted amount.\n:::", "description_cn": ":::tip{title=\"提示\"}\n使用 `money_with_currency` helper 输出格式化的金额。\n:::", "name": "price", "return_type": "number", "summary": "Shipping price after applying discounts. This amount is presented in the currency format specific to the customer.", "summary_cn": "应用折扣后运输价格。该值以客户的本地货币格式输出。"}, {"array_return_type": "tax_line", "deprecated": false, "name": "tax_lines", "return_type": "array", "summary": "List of taxes for the shipping method.", "summary_cn": "运输方式的税额。"}, {"deprecated": false, "name": "title", "return_type": "string", "summary": "Name of the shipping method.", "summary_cn": "运输方式的标题。"}], "summary": "Shipping method information", "summary_cn": "有关订单运输方式的信息。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/shop", "name": "shop", "properties": [{"_internal_": {}, "deprecated": false, "name": "allow_cancel_account", "return_type": "boolean", "summary": "Indicates whether customers are allowed to deactivate their accounts.", "summary_cn": "是否允许客户注销账户。"}, {"_internal_": {}, "deprecated": false, "name": "cart_discount_enable", "return_type": "boolean", "summary": "Indicates whether discount codes can be used at cart checkout.", "summary_cn": "购物车结算是否可以使用优惠码。"}, {"_internal_": {}, "deprecated": false, "name": "cart_taxes_included", "return_type": "boolean", "summary": "Indicates whether cart prices include taxes.", "summary_cn": "购物车价格是否含税。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "collections_count", "return_type": "number", "summary": "Total number of categories in the store.", "summary_cn": "店铺下的分类总数。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "currency", "return_type": "currency", "summary": "The currency of the store.", "summary_cn": "店铺货币。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "customer_accounts_enabled", "return_type": "boolean", "summary": "Returns true if customer login is required to complete checkout; otherwise, returns false.", "summary_cn": "如果需要客户帐户才能完成结帐，则返回true，否则返回false。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "customer_accounts_optional", "return_type": "boolean", "summary": "Returns true if customer login is not required to complete checkout; otherwise, returns false.", "summary_cn": "如果客户帐户是可选的以完成结帐，则返回true，否则返回false。"}, {"_internal_": {}, "deprecated": false, "name": "email", "return_type": "string", "summary": "The store's sender email address.", "summary_cn": "店铺的发件人电子邮件。"}, {"_internal_": {}, "array_return_type": "currency", "deprecated": false, "name": "enabled_currencies", "return_type": "array", "summary": "Currencies supported by the store.", "summary_cn": "店铺支持的货币。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The ID of the store.", "summary_cn": "店铺 ID。"}, {"_internal_": {}, "deprecated": false, "name": "money_format", "return_type": "string", "summary": "The store's currency format.", "summary_cn": "店铺的货币格式。"}, {"_internal_": {}, "deprecated": false, "name": "money_with_currency_format", "return_type": "string", "summary": "The store's currency format, including the currency type.", "summary_cn": "店铺的货币格式，包含货币类型。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "name", "return_type": "string", "summary": "The name of the store.", "summary_cn": "店铺名称。"}, {"_internal_": {}, "deprecated": false, "name": "password_message", "return_type": "string", "summary": "Information about the store's password page.", "summary_cn": "店铺的密码页面信息。"}, {"_internal_": {}, "deprecated": false, "name": "permanent_domain", "return_type": "string", "summary": "The `.myshopline.com` domain of the store.", "summary_cn": "店铺的赠送域名。"}, {"_internal_": {}, "array_return_type": "policy", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "policies", "return_type": "array", "summary": "The store's policies.\nThese policies are set in the store's settings.", "summary_cn": "店铺的相关政策。\n这些政策是在商店的规则中设置的。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "privacy_policy", "return_type": "policy", "summary": "The store's privacy policy.", "summary_cn": "店铺的隐私政策。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "products_count", "return_type": "number", "summary": "Total number of products in the store.", "summary_cn": "店铺下的商品总数。"}, {"_internal_": {}, "array_return_type": "shop_locale", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "published_locales", "return_type": "array", "summary": "The list of supported languages in the store settings.", "summary_cn": "店铺设置中，支持的语言列表。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "refund_policy", "return_type": "policy", "summary": "The store's refund policy.", "summary_cn": "店铺的退款政策。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "shipping_policy", "return_type": "policy", "summary": "The store's shipping policy.", "summary_cn": "店铺的运输政策。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "terms_of_service", "return_type": "policy", "summary": "The store's terms of service.", "summary_cn": "店铺的服务条款。"}, {"_internal_": {}, "array_return_type": "string", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "types", "return_type": "array", "summary": "All product types in the store.", "summary_cn": "店铺下的所有产品类型。"}, {"_internal_": {}, "array_return_type": "string", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "vendors", "return_type": "array", "summary": "All product vendors in the store.", "summary_cn": "店铺下的所有产品供应商。"}], "summary": "Information about the store, including total product count, address, policies, and other details.", "summary_cn": "店铺相关的信息，包括商品总数、地址、政策等内容。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [{"description": "Setting the `og:type` field of the `meta` tag by determining the current template tells the social platform what the page should be rendered as when someone shares it.", "description_cn": "通过判断当前模版来设置 `meta` 标签的 `og:type` 字段，告诉社交平台，当有人分享这个网页时，应该以什么方式来呈现这个网页。", "name": "Presenting the different page types of the site", "name_cn": "呈现网站的不同页面类型", "path": "/products/earring-ab001", "raw_sline": "{{#if template.name == \"product\"}}\n<meta property=\"og:type\" content=\"product\" />\n{{/if}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/object/template", "name": "template", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "Returns null if the template's parent directory is /templates.", "description_cn": "如果模板的父目录是 /templates，则返回 null。", "name": "directory", "return_type": "string", "summary": "The name of the template's parent directory.", "summary_cn": "模板的父目录的名称。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "| **Template File** | **template.directory** | **template.name** |  \n|---------|-----------|---------|  \n| 404.json | | 404 |\n| article.json | | article |\n| blog.json | | blog |\n| cart.json | | cart |\n| collection.json | | collection |\n| collections_all.json | | collections_all |\n| customers/account.json | customers | account |\n| customers/activate_account.json | customers | activate_account |\n| customers/addresses.json | customers | addresses |\n| customers/login.json | customers | login |\n| customers/order.json | customers | order |\n| customers/register.json | customers | register |\n| customers/reset_password.json | customers | reset_password |\n| gift_card.json | | gift_card |\n| index.json | | index |\n| page.json | | page |\n| password.json | | password |\n| product.json | | product |\n| search.json | | search |\n", "description_cn": "| **模板文件** | **template.directory** | **template.name** |  \n|---------|-----------|---------|  \n| 404.json | | 404 |\n| article.json | | article |\n| blog.json | | blog |\n| cart.json | | cart |\n| collection.json | | collection |\n| collections_all.json | | collections_all |\n| customers/account.json | customers | account |\n| customers/activate_account.json | customers | activate_account |\n| customers/addresses.json | customers | addresses |\n| customers/login.json | customers | login |\n| customers/order.json | customers | order |\n| customers/register.json | customers | register |\n| customers/reset_password.json | customers | reset_password |\n| gift_card.json | | gift_card |\n| index.json | | index |\n| page.json | | page |\n| password.json | | password |\n| product.json | | product |\n| search.json | | search |\n", "name": "name", "return_type": "string", "summary": "The name of the template's type.", "summary_cn": "模板类型的名称。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "Returns null if the default template is being used.", "description_cn": "如果正在使用默认模板，则返回null。", "name": "suffix", "return_type": "string", "summary": "The custom name of an alternate template.", "summary_cn": "备用模板的自定义名称。"}], "summary": "Information about the current template can be used in the public logic to determine which template the current rendering is used to determine the execution of different conditional branch statements.", "summary_cn": "有关当前模板的信息，可以在公共逻辑里判断当前渲染是哪个模板用来判断执行不同的条件分支语句。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/tax-line", "name": "tax_line", "properties": [{"deprecated": false, "description": "The value is shown in the customer's local (presentment) currency.\n\n:::tip{title=\"TIP\"}\nUse the [money helper](https://developer.shopline.com/zh-hans-cn/doc/hdwvcvrgnfcjf4gq/tubdydxgdfu7x9tp) to format the price.\n:::", "description_cn": "值以客户的本地（展示）货币输出。\n\n:::tip{title=\"提示\"}\n使用 [money helper](https://developer.shopline.com/zh-hans-cn/doc/hdwvcvrgnfcjf4gq/tubdydxgdfu7x9tp) 以输出格式化的价格。\n:::", "name": "price", "return_type": "number", "summary": "The tax amount in currency subunits.", "summary_cn": "以货币子单位表示的税额。"}, {"deprecated": false, "name": "rate", "return_type": "number", "summary": "The tax rate as a decimal value.", "summary_cn": "税率的小数值。"}, {"deprecated": false, "name": "title", "return_type": "string", "summary": "Tax's title.", "summary_cn": "税务的标题。"}], "summary": "Information about tax lines for checkout or orders.", "summary_cn": "关于结账或订单的税费行的信息。"}, {"deprecated": false, "description": "", "description_cn": "", "examples": [], "link": "https://developer.shopline.com/docs/sline/object/variant", "name": "variant", "properties": [{"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "available", "return_type": "boolean", "summary": "Returns `true` if the variant is available. Returns `false` if not.", "summary_cn": "如果该款式可用，则返回 `true`。如果不可用，则返回 `false`。 "}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "barcode", "return_type": "string", "summary": "The barcode of the variant.", "summary_cn": "该款式的条形码。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "compare_at_price", "return_type": "number", "summary": "The compare at price of the variant in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.\n\nTip: Use `money helpers` to output a formatted price.", "summary_cn": "该款式的比较价格。\n\n该值以顾客当地（显示）的货币来输出。\n\n提示：使用 `money helpers` 来输出格式化后的价格。 "}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "featured_image", "return_type": "image", "summary": "The image attached to the variant.\n\nNote: This is the same value as `variant.image`.", "summary_cn": "该款式的图片。\n\n注意：此值与 `variant.image` 的值相同。 "}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "featured_media", "return_type": "media", "summary": "The first media object attached to the variant.", "summary_cn": "该款式的第一个媒体对象。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "id", "return_type": "string", "summary": "The ID of the variant.", "summary_cn": "该款式的 ID。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "image", "return_type": "image", "summary": "The image attached to the variant.\n\nNote: This is the same value as `variant.featured_image`.", "summary_cn": "该款式的图片。\n\n注意：此值与 `variant.featured_image` 的值相同。 "}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "inventory_quantity", "return_type": "number", "summary": "The inventory quantity of the variant.\n\nIf inventory isn't tracked, then `0` is returned.", "summary_cn": "该款式的库存数量。\n\n如果未跟踪库存，则返回 `0`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "metafields", "return_type": "", "summary": "The metafields applied to the variant.\n\nTip: To learn about how to create metafields, refer to Create and manage metafields or visit the [Shopline Help Center](https://help.shopline.com/hc/en-001/articles/7095355017113-Guide-to-Using-Metafields-Feature).", "summary_cn": "该款式的元字段。\n\n提示：想要了解如何创建元字段，可以参考创建和管理元字段 或者访问[Shopline帮助中心](https://help.shopline.com/hc/zh-cn/articles/7095355017113-%E5%85%83%E5%AD%97%E6%AE%B5-Metafields-%E5%8A%9F%E8%83%BD%E4%BD%BF%E7%94%A8%E6%8C%87%E5%8D%97)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "option1", "return_type": "string", "summary": "The value of the variant for the first product option.\n\nIf there's no first product option, then `null` is returned.", "summary_cn": "第一个商品规格的规格值。\n\n如果没有第一个商品规格，则返回 `null`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "option2", "return_type": "string", "summary": "The value of the variant for the second product option.\n\nIf there's no second product option, then `null` is returned.", "summary_cn": "第二个商品规格的规格值。\n\n如果没有第二个商品规格，则返回 `null`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "option3", "return_type": "string", "summary": "The value of the variant for the third product option.\n\nIf there's no third product option, then `null` is returned.", "summary_cn": "第三个商品规格的规格值。\n\n如果没有第三个商品规格，则返回 `null`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "option4", "return_type": "string", "summary": "The value of the variant for the fourth product option.\n\nIf there's no fourth product option, then `null` is returned.", "summary_cn": "第四个商品规格的规格值。\n\n如果没有第四个商品规格，则返回 `null`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "option5", "return_type": "string", "summary": "The value of the variant for the fifth product option.\n\nIf there's no fifth product option, then `null` is returned.", "summary_cn": "第五个商品规格的规格值。\n\n如果没有第五个商品规格，则返回 `null`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "options", "return_type": "string", "summary": "The values of the variant for each product option.\n\n```sline\n{{#each product.variants as |variant|}}\n  {{variant.id}}: {{join variant.options '/'}}\n{{/each}}\n```", "summary_cn": "款式的规格值列表。\n\n```sline\n{{#each product.variants as |variant|}}\n  {{variant.id}}: {{join variant.options '/'}}\n{{/each}}\n```"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "price", "return_type": "number", "summary": "The price of the variant in the currency's subunit.\n\nThe value is output in the customer's local (presentment) currency.\n\nTip: Use `money helpers` to output a formatted price.", "summary_cn": "该款式的价格。\n\n该值以顾客当地（显示）的货币来输出。\n\n提示：使用 `money helpers` 来输出格式化后的价格。 "}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "requires_selling_plan", "return_type": "boolean", "summary": "Returns `true` if the variant is set to require a selling_plan when being added to the cart. Returns `false` if not.", "summary_cn": "如果款式需要一个销售计划才能被添加到购物车时返回 `true`，否则返回 `false`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "requires_shipping", "return_type": "boolean", "summary": "Returns `true` if the variant requires shipping. Returns `false` if it doesn't.", "summary_cn": "如果变体需要货运时返回 `true`，否则返回 `false`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "selected", "return_type": "boolean", "summary": "Returns `true` if the variant is currently selected. Returns `false` if it's not.\n\nNote: The selected `variant` is determined by the variant URL parameter. This URL parameter is available on product pages URLs only.", "summary_cn": "如果款式是当前被选中的返回 `true`，否则返回 `false`。\n\n提示：被选中的款式是由 `variant` URL参数来决定。这个 URL 参数仅在商品页面的 URL 中有效。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "selling_plan_allocation", "return_type": "array", "summary": "The `selling_plan_allocation` objects for the variant.", "summary_cn": "针对款式的 `selling_plan_allocation` 对象。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "sku", "return_type": "string", "summary": "The SKU of the variant.", "summary_cn": "款式的 SKU (货号)。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "store_availabilities", "return_type": "array", "summary": "The store availabilities for the variant.\n\nThe array is defined in only the following cases:\n- `variant.selected` is `true`\n- The variant is the product's first available variant. For example, `product.first_available_variant` or `product.selected_or_first_available_variant`.", "summary_cn": "商店关于该款式的可售卖情况。\n\n该数组仅在以下情况中被定义：\n\n- `variant.selected` 为 `true`\n- 款式是商品的第一个可售卖的款式。比如，`product.first_available_variant` 或者 `product.selected_or_first_available_variant`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "taxable", "return_type": "boolean", "summary": "Returns `true` if taxes should be charged on the variant. Returns `false` if not.", "summary_cn": "如果购买款式需要缴税返回 `true`，否则返回 `false`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "title", "return_type": "string", "summary": "A concatenation of each variant option, separated by a `/`.", "summary_cn": "每个款式规格的组合，以 `/` 分隔。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "url", "return_type": "string", "summary": "The URL of the variant.\n\nVariant URL use the following structure: `/products/[product-handle]?sku=[variant-id]`", "summary_cn": "款式的 URL。\n\n款式 URL 使用以下的结构：`/products/[product-handle]?sku=[variant-id]`"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "weight_in_unit", "return_type": "number", "summary": "The weight of the variant in the unit specified by `variant.weight_unit`.\n\nTip: To output this weight, use this property, and the `variant.weight_unit` property, with the `weight_with_unit helper`.", "summary_cn": "款式以 `variant.weight_unit` 指定的单位表示的重量。\n\n提示：要输出重量，使用这个属性，以及 `variant.weight_unit` 属性，并使用 `weight_with_unit helper`。"}, {"_internal_": {}, "array_return_type": "", "deprecated": false, "deprecation_reason": "", "deprecation_reason_cn": "", "description": "", "description_cn": "", "name": "weight_unit", "return_type": "string", "summary": "The unit for the weight of the variant.\n\nTip: To output the weight of a variant in this unit, use this property, and the `variant.weight_in_unit` property, with the `weight_with_unit helper`.", "summary_cn": "款式重量的单位。\n\n提示：要以该单位输出重量，使用这个属性，以及 `variant.weight_in_unit` 属性，并使用 `weight_with_unit helper`。"}], "summary": "A product variant.", "summary_cn": "产品款式。"}, {"deprecated": false, "description": "You can access specific menus from the linklists object by using the menu's handle.", "description_cn": "你可以通过使用菜单的 handle 从 linklists 对象中访问特定的菜单。", "link": "https://developer.shopline.com/docs/sline/object/linklists", "name": "linklists", "summary": "All navigation menus in your store.", "summary_cn": "商店中的所有导航菜单。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/curency", "name": "curency", "properties": [{"deprecated": false, "description": "", "name": "iso_code", "return_type": "string", "summary": "Currency's ISO code.", "summary_cn": "货币的 ISO 代码。"}, {"deprecated": false, "name": "name", "return_type": "string", "summary": "<PERSON><PERSON><PERSON><PERSON>'s name.", "summary_cn": "货币的名称。"}, {"deprecated": false, "name": "symbol", "return_type": "string", "summary": "<PERSON><PERSON><PERSON><PERSON>'s symbol.", "summary_cn": "货币的符号。"}], "summary": "Information about currency, such as ISO code and symbol.", "summary_cn": "关于货币的信息，如 ISO 代码和符号。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/location", "name": "location", "properties": [{"deprecated": false, "name": "id", "return_type": "string", "summary": "The location's ID.", "summary_cn": "地点的 ID。 "}, {"deprecated": false, "name": "name", "return_type": "string", "summary": "The location's name.", "summary_cn": "地点的名称。"}, {"deprecated": false, "name": "address", "return_type": "address", "summary": "Detailed address information.", "summary_cn": "详细地址信息。"}], "summary": "Store pickup location information.", "summary_cn": "到店取货地点信息。"}, {"deprecated": false, "description": "Using the `all_collections` object directly will not output data. It needs to be used in conjunction with the [get filter](/docs/sline/filter/get).", "description_cn": "直接使用 `all_collections` object不会输出数据，需要结合 [get filter](/docs/sline/filter/get) 一起使用。", "examples": [{"description": "Get [collection object](/docs/sline/object/collection) through collection handle.", "description_cn": "通过商品分类 Handle 获取 [collection object](/docs/sline/object/collection)。", "name": "Get the specified category", "name_cn": "获取指定分类", "raw_sline": "{{#var collection = all_collections | get(\"bottom\") /}}\n{{#link_to collection.url}}\n  {{collection.title}}\n{{/link_to}}"}], "link": "https://developer.shopline.com/docs/sline/object/all-collections", "name": "all_collections", "summary": "All product collections in the store.", "summary_cn": "店铺的全部商品分类。"}, {"deprecated": false, "description": "Using the `all_pages` object directly will not output data. It needs to be used in conjunction with [get filter](/docs/sline/filter/get).", "description_cn": "直接使用 `all_pages` object 不会输出数据，需要结合[get filter](/docs/sline/filter/get)一起使用。", "examples": [{"description": "Specify a custom page handle to get a [page object](/docs/sline/object/page).", "description_cn": "指定自定义页面 handle 获取 [page object](/docs/sline/object/page).", "name": "Get the specified custom page", "name_cn": "获取指定自定义页面", "raw_sline": "{{#var page = all_pages | get(\"privacy-policy\") /}}\n{{#link_to page.url}}\n  {{page.title}}\n{{/link_to}}"}], "link": "https://developer.shopline.com/docs/sline/object/all-pages", "name": "all_pages", "summary": "All custom pages of the store.", "summary_cn": "店铺的全部自定义页面。"}, {"deprecated": false, "description": "Directly using the all_blogs object will not output data. It needs to be used in conjunction with [get filter](/docs/sline/filter/get).", "description_cn": "直接使用 all_articles object 不会输出数据，需要结合[get filter](/docs/sline/filter/get)一起使用。", "examples": [{"description": "Get [article object](/docs/sline/object/article) by specifying the blog and article handle in the format: `blogHandle/articleHandle`.", "description_cn": "通过指定博客和文章 handle 获取 [article object](/docs/sline/object/article)，格式为：`blogHandle/articleHandle`。", "name": "Get the specified article", "name_cn": "获取指定文章", "raw_sline": "{{#var article = all_articles | get(\"news/blog-2\") /}}\n{{#link_to article.url}}\n  {{article.title}}\n{{/link_to}}"}], "link": "https://developer.shopline.com/docs/sline/object/all-articles", "name": "all_articles", "summary": "All articles in the store blog.\n\n", "summary_cn": "商店博客中的所有文章。"}, {"deprecated": false, "description": "Directly using the `all_blogs` object will not output data. It needs to be used in conjunction with [get filter](/docs/sline/filter/get).", "description_cn": "直接使用 `all_blogs` object 不会输出数据，需要结合[get filter](/docs/sline/filter/get)一起使用。", "examples": [{"description": "Get a single [blog object](/docs/sline/object/blog) by specifying the blog handle.", "description_cn": "通过指定博客 handle 获取单个 [blog object](/docs/sline/object/blog)。", "name": "Get the specified blog collection", "name_cn": "获取指定博客集合", "raw_sline": "{{#var blog = all_blogs | get(\"news\") /}}\n{{#link_to blog.url}}\n  {{blog.title}}\n{{/link_to}}"}], "link": "https://developer.shopline.com/docs/sline/object/all-blogs", "name": "all_blogs", "summary": "A collection of all blogs in the store.", "summary_cn": "店铺的全部博客集合。"}, {"deprecated": false, "description": "The font object can be used within Handlebars assets or style tags to apply values from font settings to the theme's CSS.", "description_cn": "您可以在 HTML 资源或样式标签内使用 font 对象，将字体设置值应用于主题 CSS。\n\n", "examples": [{"description": "Set the style of sorting titles in global configurations.", "description_cn": "设置全局配置中排序标题的样式。", "name": "<PERSON>", "name_cn": "设置字体样式", "raw_sline": "--sort-title-font-weight: {{settings.sort_title_font.weight}};\n--sort-title-font-style: {{settings.sort_title_font.style}};"}], "link": "https://developer.shopline.com/docs/sline/object/font", "name": "font", "properties": [{"_internal_": {}, "deprecated": false, "name": "fallback_families", "return_type": "string", "summary": "The fallback families of the font.", "summary_cn": "字体的备用系列。\n"}, {"_internal_": {}, "deprecated": false, "name": "family", "return_type": "string", "summary": "The name of the font.\n", "summary_cn": "字体的名称。"}, {"_internal_": {}, "deprecated": false, "name": "style", "return_type": "string", "summary": "The style of the font.\n", "summary_cn": "字体的样式。"}, {"_internal_": {}, "deprecated": false, "name": "system", "return_type": "boolean", "summary": "Returns true if the font is a system font. Returns false if not.", "summary_cn": "如果字体是系统字体，则返回 true。如果不是，则返回 false。"}, {"_internal_": {}, "deprecated": false, "name": "weight", "return_type": "string", "summary": "The weight of the font.\n\n", "summary_cn": "字体的粗细。"}], "summary": "The font in the font selector settings.", "summary_cn": "字体选择器设置中的字体。"}, {"deprecated": false, "description": "The handle object only returns a value when the current template is one of the following:\n\n- [article](/docs/sline/object/article)\n- [blog](/docs/sline/object/blog)\n- [collection](/docs/sline/object/collection)\n- [page](/docs/sline/object/page)\n- [product](/docs/sline/object/product)\n\nIf the current template is any other template, it returns nil.", "description_cn": "只有当前模板为以下模板时，handle对象才会返回值：\n\n- [article](/docs/sline/object/article)\n- [blog](/docs/sline/object/blog)\n- [collection](/docs/sline/object/collection)\n- [page](/docs/sline/object/page)\n- [product](/docs/sline/object/product)\n\n如果当前模板是其他模板，则返回 nil。", "examples": [{"description": "The `handle` of each resource can be displayed as the page title on its corresponding resource page.", "description_cn": "可以在每个资源页面上展示对应资源的 `handle` 作为页面标题。", "name": "Create Page Title", "name_cn": "创建页面标题", "path": "/products/earring-ab001", "raw_sline": "<meta property=\"og:title\" content=\"{{handle}}\">", "source_object": ""}], "link": "https://developer.shopline.com/docs/sline/object/handle", "name": "handle", "summary": "The handle related to the current template resource.", "summary_cn": "当前模板资源关联的 handle。"}, {"deprecated": false, "examples": [{"description": "Get a set of products in the store via [get_product_pagination filter](/docs/sline/filter/get-product-pagination) with the return value `paginate object`.", "description_cn": "通过 [get_product_pagination filter](/docs/sline/filter/get-product-pagination) 获取店铺内的一组商品，返回值为 `paginate object`.", "name": "Create product navigation", "name_cn": "创建商品导航", "raw_sline": "{{#var paginate = all_products | get_product_pagination(5) /}}\n{{#for product in paginate.list}}\n  {{#link_to product.url}}\n    {{product.title}}\n  {{/link_to}}\n{{/for}}"}], "link": "https://developer.shopline.com/docs/sline/object/paginate", "name": "paginate", "properties": [{"_internal_": {}, "array_return_type": "part", "deprecated": false, "description": "Pagination parts are used to build pagination navigation.", "description_cn": "分页部分用于构建分页导航。", "name": "parts", "return_type": "array", "summary": "The pagination parts. ", "summary_cn": "分页部件。"}, {"_internal_": {}, "array_return_type": "untyped", "deprecated": false, "name": "list", "return_type": "array", "summary": "Paginated data list.\n", "summary_cn": "分页数据列表"}, {"_internal_": {}, "deprecated": false, "description": "For example, if you show 5 items per page and are on page 3, then the value of `paginate.current_offset` is 10.\n", "description_cn": "例如，如果每页显示 5 个项目并且当前位于第 3 页，则 `paginate.current _offset` 的值为 10。\n\n\n", "name": "current_offset", "return_type": "number", "summary": "The total number of items on pages previous to the current page.\n", "summary_cn": "当前页之前的页面上的项目总数。"}, {"_internal_": {}, "deprecated": false, "name": "current_page", "return_type": "number", "summary": "The page number of the current page.\n", "summary_cn": "当前页的页码。"}, {"_internal_": {}, "deprecated": false, "description": "For example, if you paginate a collection of 120 products, then the value of `paginate.items` is 120.", "description_cn": "例如，如果您对 120 个产品的集合进行分页，则 `paginate.items` 的值为 120。", "name": "items", "return_type": "number", "summary": "The total number of items to be paginated.", "summary_cn": "需要分页的项目总数。"}, {"_internal_": {}, "deprecated": false, "description": "The default value is page.", "description_cn": "默认值为 page 。", "name": "page_param", "return_type": "string", "summary": "The URL parameter denoting the pagination.\n", "summary_cn": "表示分页的 URL 参数。"}, {"_internal_": {}, "deprecated": false, "name": "page_size", "return_type": "number", "summary": "The number of items displayed per page.\n", "summary_cn": "每页显示的项目数。"}, {"_internal_": {}, "deprecated": false, "name": "pages", "return_type": "number", "summary": "The total number of pages.", "summary_cn": "总页数。"}, {"_internal_": {}, "deprecated": false, "name": "previous", "return_type": "part", "summary": "The pagination part to go to the previous page.\n", "summary_cn": "分页部分用于转到上一页。"}, {"_internal_": {}, "deprecated": false, "name": "next", "return_type": "part", "summary": "The pagination part to go to the next page.\n", "summary_cn": "分页部分转到下一页。"}], "summary": "A set of paging information that can be used for paged navigation and paged data presentation.", "summary_cn": "一组分页信息，可用于分页导航和分页数据展示。"}, {"deprecated": false, "examples": [{"description": "You can create a pagination navigation by iterating over each `part object` of a [paginate object](/docs/sline/object/paginate).", "description_cn": "您可以通过遍历 [paginate object](/docs/sline/object/paginate) 的每个 `part object` 来创建分页导航。", "name": "Create pagination navigation with part", "name_cn": "使用 part 创建分页导航", "raw_sline": "{{#var paginate = all_products | get_product_pagination(10) /}}\n{{#for part in paginate.parts}}\n  {{#if part.is_link }}\n    {{#link_to part.url}}\n      {{part.title}}\n    {{/link_to}}\n  {{#else/}}\n    <span>{{part.title}}</span>\n  {{/if}}\n{{/for}}"}], "link": "https://developer.shopline.com/docs/sline/object/part", "name": "part", "properties": [{"_internal_": {"domain": "1"}, "deprecated": false, "name": "is_link", "return_type": "boolean", "summary": "Returns `true` if the part is a link. Returns `false` if not.", "summary_cn": "如果该部件是链接，则返回 `true` 。如果不是，则返回 `false` 。"}, {"_internal_": {}, "deprecated": false, "name": "title", "return_type": "string", "summary": "The page number associated with the part.\n", "summary_cn": "与该部分关联的页码。"}, {"_internal_": {}, "deprecated": false, "description": "It consists of the current page URL path with the pagination parameter for the current part appended.\n", "description_cn": "它由当前页面 URL 路径和附加的当前部分的分页参数组成。", "name": "url", "return_type": "string", "summary": "The URL of the part.\n", "summary_cn": "该部分的 URL。"}], "summary": "The pagination part within navigation can be used to construct navigation elements.", "summary_cn": "导航中的分页部分，可以用于构造导航元素。"}, {"deprecated": false, "description": "Only exists within a [blocks](/docs/sline/tag/blocks) loop.", "description_cn": "只有在 [blocks](/docs/sline/tag/blocks) 循环中存在。", "examples": [{"description": "Add an outer div for the title block.", "description_cn": "针对 title block增加一个外层div。", "name": "Processing for special blocks", "name_cn": "针对特殊block做处理", "syntax": "{{#blocks}}\n    {{#if forblock.type == \"title\"}}\n        <div class=\"title-wrapper\">\n            {{#block wrapped=true /}}\n        </div>\n    {{#else /}}\n        {{#block /}}\n    {{/if}}\n{{/blocks}}"}], "link": "https://developer.shopline.com/docs/sline/object/forblock", "name": "forblock", "properties": [{"_internal_": {}, "deprecated": false, "name": "id", "return_type": "string", "summary": "Returns the id of the block in the current iteration.", "summary_cn": "返回当前迭代中 block 的 id。"}, {"_internal_": {}, "deprecated": false, "name": "index0", "return_type": "number", "summary": "The 0-based index of the current iteration.", "summary_cn": "当前迭代的从 0 开始的索引。"}, {"_internal_": {}, "deprecated": false, "name": "index", "return_type": "number", "summary": "The 1-based index of the current iteration.", "summary_cn": "当前迭代的从 1 开始的索引。"}, {"_internal_": {}, "deprecated": false, "name": "type", "return_type": "string", "summary": "In local iteration, the type of the current block.", "summary_cn": "本地迭代中，当前 block 的 type。"}, {"_internal_": {}, "deprecated": false, "name": "blocks_size", "return_type": "number", "summary": "The total number of iterations in the loop.", "summary_cn": "循环中的总迭代次数。"}, {"_internal_": {}, "deprecated": false, "name": "settings", "return_type": "any", "summary": "In local iteration, the settings of the current block.", "summary_cn": "本地迭代中，当前 block 的 settings。"}], "summary": "Information about the parent blocks loop.", "summary_cn": "有关父级 blocks 循环的信息。"}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/object/shop-locale", "name": "shop_locale", "properties": [{"_internal_": {}, "deprecated": false, "name": "endonym_name", "return_type": "string", "summary": "The name of the language.", "summary_cn": "语种的名称。"}, {"_internal_": {}, "deprecated": false, "name": "iso_code", "return_type": "string", "summary": "The code of the language.", "summary_cn": "语种的代码。"}, {"_internal_": {}, "deprecated": false, "name": "primary", "return_type": "boolean", "summary": "Whether it is the primary language.", "summary_cn": "是否主要语种。"}, {"_internal_": {}, "deprecated": false, "name": "root_url", "return_type": "string", "summary": "Root path associated with the language.", "summary_cn": "语种关联的根路径。"}], "summary": "Language information in the store.", "summary_cn": "店铺下的语种信息。"}]