[{"deprecated": false, "description": "Each `for` loop has an associated [forloop object](/docs/sline/object/forloop) that contains information about the loop.", "description_cn": "每个`for`循环都有一个关联的[forloop object](/docs/sline/object/forloop)，其中包含有关循环的信息。", "examples": [{"path": "/products/earring-ab001", "raw_sline": "{{#for tag in product.tags}}\n  {{forloop.index}}: {{tag}}\n{{/for}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/tag/for", "name": "for", "summary": "Renders an expression for each item in an array.", "summary_cn": "为数组中的每个项渲染一个表达式。", "syntax": "{{#for item in array}}\n  expression\n{{/for}}", "syntax_keywords": [{"description": "The array to iterate over.", "description_cn": "需要执行循环的数组", "keyword": "array"}, {"description": "The current item in the array.", "description_cn": "当前循环中的变量值", "keyword": "item"}, {"description": "The expression to render for each iteration.", "description_cn": "每次循环中执行的语句", "keyword": "expression"}]}, {"arguments": [], "deprecated": false, "examples": [{"name": "default", "name_cn": "default", "raw_sline": "{{#cart_form id=\"cart-form-id\"}}\n    your code\n    <button type=\"submit\" class=\"button\" name=\"checkout\">checkout</button>\n{{/cart_form}}", "source_object": "cart", "syntax": ""}, {"name": "class and button", "name_cn": "class and button", "raw_sline": "{{#cart_form id=\"cart-form-id\" data-attr=\"attr-test\" class=\"cartFrom\"}}\n    your code\n    <button type=\"submit\" class=\"button\" name=\"checkout\">checkout</button>\n{{/cart_form}}", "source_object": "cart", "syntax": ""}], "hashs": [{"description": "", "description_cn": "你可以添加任意参数来制定 HTML 属性，该参数应与（包含但不限于 data-前缀）属性名和所需值相匹配。", "name": "attr", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/cart-form", "name": "cart_form", "summary": "Generates a form for creating a checkout based on the items currently in the cart. The cart form requires a [cart object](TODO cart object)  as a parameter. To learn more about using the cart form in your theme, refer to the [cart template](TODO templates/cart#proceed-to-checkout).", "summary_cn": "根据当前购物车中的商品生成用于创建结帐的表单。购物车表单需要一个 [cart object](TODO cart object) 作为参数。要了解在主题中使用购物车表单的更多信息，请查阅 [cart template](TODO templates/cart#proceed-to-checkout)", "syntax": "{{#cart_form id=\"cart-form-id\"}}\n    your code\n{{/cart_form}}"}, {"arguments": [{"description": "Payment Type, required", "description_cn": "付款类型，必填", "name": "type", "types": ["string"]}, {"description": "Specify the class attribute of the <svg> tag.\n\n", "description_cn": "指定 `<svg>` 标记的 class 属性。\n", "name": "class", "types": ["string"]}], "deprecated": false, "link": "https://developer.shopline.com/docs/sline/tag/payment-type-svg", "name": "payment_type_svg", "summary": "Generates an HTML `<svg>` tag for a given payment type.", "summary_cn": "为一个给定的支付类型生成一个 HTML `<svg>` 标签。\n", "syntax": "#### Base\n```html\n{{#payment_type_svg \"affrim\" /}}\n```\n\n#### class\n```html\n{{#payment_type_svg \"affrim\" class=\"payment-svg-class\" /}}\n```"}, {"arguments": [{"description": "[address object](TODO address object)", "description_cn": "[address object](TODO address object)", "name": "address", "types": ["object"]}], "deprecated": false, "examples": [{"name": "default", "name_cn": "default", "raw_sline": "{{#format_address address /}}", "source_object": "address", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/tag/format-address", "name": "format_address", "summary": "Generates an HTML address display, with each address component ordered according to the address's locale.\n\n", "summary_cn": "生成 HTML 地址显示，其中每个地址组件都根据地址所在地区排序。\n", "syntax": "```html\n{{#format_address address /}}\n```\n\n"}, {"deprecated": false, "examples": [{"name": "Unsubscribing an email", "name_cn": "取消邮箱订阅", "path": "/user/center", "raw_sline": "{{#customer_unsubscribe_form}}\n    {{#if form.posted_successfully}}\n        <div>success</div>\n    {{/if}}\n    {{#if form.errors.messages}}\n        <div>{{ form.errors.messages }}</div>\n    {{/if}}\n    <input type=\"hidden\" name=\"customer[unsubscribe_type]\" value=\"email\" />\n    {{#for reason in form.unsubscribe_reasons}}\n    <label>\n        <input type=\"radio\" name=\"customer[unsubscribe_reason]\" value=\"{{reason.value}}\" />\n        <span>{{reason.text | t()}}</span>\n        {{#if reason.show_other_reason}}\n          <input type=\"text\" name=\"customer[other_reason]\" />\n        {{/if}}\n    </label>\n    {{/for}}     \n    <button type=\"submit\">Unsubscribe</button>\n{{/customer_unsubscribe_form}}", "syntax": "{{#customer_unsubscribe_form}}\n    form_content\n{{/customer_unsubscribe_form}}"}, {"name": "Unsubscribing a mobile phone", "name_cn": "取消手机订阅", "path": "/user/center", "raw_sline": "{{#customer_unsubscribe_form}}\n    {{#if form.posted_successfully}}\n        <div>success</div>\n    {{/if}}\n    {{#if form.errors.messages}}\n        <div>{{ form.errors.messages }}</div>\n    {{/if}}\n    <input type=\"hidden\" name=\"customer[unsubscribe_type]\" value=\"phone\" />\n    <button type=\"submit\">Unsubscribe</button>\n{{/customer_unsubscribe_form}}", "syntax": "{{#customer_unsubscribe_form}}\n    form_content\n{{/customer_unsubscribe_form}}"}, {"description": "The `return_to` parameter allows you to specify a URL to redirect to. ", "description_cn": "使用 `return_to` 参数可以指定表单提交后重定向到的页面。", "name": "Specify a URL to redirect to", "name_cn": "自定义跳转", "path": "/user/center", "raw_sline": "{{#customer_unsubscribe_form return_to=routes.root_url}}\n    {{#if form.posted_successfully}}\n        <div>success</div>\n    {{/if}}\n    {{#if form.errors.messages}}\n        <div>{{ form.errors.messages }}</div>\n    {{/if}}\n    <input type=\"hidden\" name=\"customer[unsubscribe_type]\" value=\"phone\" />\n    <button type=\"submit\">Unsubscribe</button>\n{{/customer_unsubscribe_form}}", "syntax": "{{#customer_unsubscribe_form return_to=routes.root_url}}\n    form_content\n{{/customer_unsubscribe_form}}"}], "hashs": [{"description": "Redirect URL after form submit, defaults to the current page URL.", "description_cn": "表单提交后的跳转地址，默认为当前页面地址。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/customer-unsubscribe-form", "name": "customer_unsubscribe_form", "summary": "Generates a form for unsubscribing an email or mobile phone.\n\n## Form Input\n**customer[unsubscribe_type]** string **required**\n\nUnsubscribe type. Valid values are: \n- `email`: unsubscribing an email.\n- `phone`: unsubscribing a mobile phone\n\n**customer[unsubscribe_reason]** number\n\nUnsubscribe reason. available when unsubscribing an email, Valid values are: \n- `1`: I no longer want to receive these emails.\n- `2`: I receive too many emails.\n- `3`: I did not subscribe to this list.\n- `4`: Emails are not relevant to me.\n- `5`: Email content is too long.\n- `6`: Other reason.\n\n**customer[other_reason]** string\n\nCustom unsubscribe reason, available when `customer[unsubscribe_reason]` value is `6`.", "summary_cn": "生成取消邮箱/手机订阅表单。\n\n## 表单输入\n**customer[unsubscribe_type]** string **必填**\n\n取消订阅类型。有效枚举值包含：\n- `email`: 取消邮箱订阅。\n- `phone`: 取消手机订阅。\n\n**customer[unsubscribe_reason]** number\n\n取消订阅原因。取消邮箱订阅是有效，有效枚举值包含：\n- `1`：我不想接收此类邮件。\n- `2`：我收到的邮件数量过多。\n- `3`：我未订阅过此类邮件。\n- `4`：邮件内容与我无关。\n- `5`：邮件内容过长。\n- `6`：其他原因。\n\n**customer[other_reason]** string\n\n其他原因，当 `customer[unsubscribe_reason]` 值为 `6` 时有效.", "syntax": "{{#customer_unsubscribe_form}}\n    form_content\n{{/customer_unsubscribe_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Subscribing an email", "name_cn": "邮箱订阅", "path": "/user/center", "raw_sline": "{{#customer_subscribe_form}}\n    {{#if form.posted_successfully}}\n        <div>success</div>\n    {{/if}}\n    {{#if form.errors.messages}}\n        <div>{{ form.errors.messages }}</div>\n    {{/if}}\n    <input type=\"email\" name=\"customer[email]\" />\n    <button type=\"submit\">Subscribe</button>\n{{/customer_subscribe_form}}", "syntax": "{{#customer_subscribe_form}}\n    form_content\n{{/customer_subscribe_form}}"}, {"name": "Unsubscribing a mobile phone", "name_cn": "手机订阅", "path": "/user/center", "raw_sline": "{{#customer_subscribe_form}}\n    {{#if form.posted_successfully}}\n        <div>success</div>\n    {{/if}}\n    {{#if form.errors.messages}}\n        <div>{{ form.errors.messages }}</div>\n    {{/if}}\n    <select name=\"customer[code]\">\n      {{#for item in props.all_country_dialing_code}}\n      <option value=\"{{item.dialing_code}}\">{{item.name}}</option>\n      {{/for}}\n    </select>\n    <input type=\"tel\" name=\"customer[phone]\" />\n    <button type=\"submit\">Subscribe</button>\n{{/customer_subscribe_form}}", "syntax": "{{#customer_subscribe_form}}\n    form_content\n{{/customer_subscribe_form}}"}, {"description": "The `return_to` parameter allows you to specify a URL to redirect to. ", "description_cn": "使用 `return_to` 参数可以指定表单提交后重定向到的页面。", "name": "Specify a URL to redirect to", "name_cn": "自定义跳转", "path": "/user/center", "raw_sline": "{{#customer_subscribe_form return_to=routes.root_url}}\n    {{#if form.posted_successfully}}\n        <div>success</div>\n    {{/if}}\n    {{#if form.errors.messages}}\n        <div>{{ form.errors.messages }}</div>\n    {{/if}}\n    <input type=\"email\" name=\"customer[email]\" />\n    <button type=\"submit\">Subscribe</button>\n{{/customer_subscribe_form}}", "syntax": "{{#customer_subscribe_form return_to=routes.root_url}}\n    form_content\n{{/customer_subscribe_form}}"}], "hashs": [{"description": "Redirect URL after form submit, defaults to the current page URL.", "description_cn": "表单提交后的跳转地址，默认为当前页面地址。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/customer-subscribe-form", "name": "customer_subscribe_form", "summary": "Generates a form for subscribing an email or mobile phone.\n\n## Form Input\n\n**customer[email]** email\n\nEmail. Required for subscribing an email.\n\n**customer[phone]** phone\n\nMobile phone. Required for subscribing a mobile phone. The mobile phone number format is an international closed phone number, e.g. China cell phone number: ***********.\n\n**customer[code]** number\n\nArea code. Required for subscribing a phone. The area code format is an international telephone area code, for example, China cell phone area code: 86.\n\n\n\n", "summary_cn": "生成邮箱/手机订阅表单。\n\n## 表单输入\n\n**customer[email]** email\n\n邮箱。订阅邮箱时必填。\n\n**customer[phone]** phone\n\n手机号。订阅手机时必填，手机号格式为国际电话号码, 例如中国手机号码：***********。\n\n**customer[code]** number\n\n区号。订阅手机时必填，区号为国际电话区号，例如中国手机区号：86。", "syntax": "{{#customer_subscribe_form}}\n    form_content\n{{/customer_subscribe_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Cancel delete customer", "name_cn": "取消注销账号", "path": "/user/center", "raw_sline": "{{#if customer.cancelling_account}}\n    {{#cancel_delete_customer_form return_to=routes.root_url}}\n        {{#if form.posted_successfully}}\n            <div>success</div>\n        {{/if}}\n        {{#if form.errors.messages}}\n            <div>{{ form.errors.messages }}</div>\n        {{/if}}\n        <button type=\"submit\">Submit</button>\n    {{/cancel_delete_customer_form}}\n{{/if}}", "syntax": "{{#cancel_delete_customer_form}}\n    form_content\n{{/cancel_delete_customer_form}}"}], "hashs": [{"description": "Redirect URL after form submit, defaults to the current page URL.", "description_cn": "表单提交后的跳转地址，默认为当前页面地址。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/cancel-delete-customer-form", "name": "cancel_delete_customer_form", "summary": "Generate a form for cancelling delete an account.", "summary_cn": "生成取消注销账号表单。", "syntax": "{{#cancel_delete_customer_form}}\n    form_content\n{{/cancel_delete_customer_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Bind customer phone", "name_cn": "修改客户手机号", "path": "/user/center", "raw_sline": "{{#bind_customer_phone_form id=\"bind-customer-phone\"}}\n    {{#if form.register_config.check_tag}}\n        <div class=\"step1-form\">\n          <div class=\"verifycode\">\n            <input type=\"text\" name=\"customer[verifycode1]\" placeholder=\"verifycode\" />\n            <button class=\"verifycode-button\">send</button>\n          </div>\n          <div>\n            <button class=\"next-step-button\">next</button>\n          </div>\n        </div>\n        <div class=\"step2-form\" style=\"display: none;\">\n          <div>\n            <label for=\"phone\">new phone</label>\n            <input id=\"code\" type=\"text\" name=\"customer[code]\" />\n            <input id=\"phone\" type=\"text\" name=\"customer[phone]\" />\n          </div>\n          <div class=\"verifycode\">\n            <input type=\"text\" name=\"customer[verifycode]\" placeholder=\"verifycode\" />\n            <button class=\"verifycode-button\">send</button>\n          </div>\n          <div class=\"submit\">\n            <input class=\"submit-button\" type=\"submit\" value=\"change phone\">\n          </div>\n        </div>\n    {{else}}\n        <div>\n          <label for=\"phone\">new phone</label>\n          <input id=\"code\" type=\"text\" name=\"customer[code]\" placeholder=\"code\" />\n          <input id=\"phone\" type=\"text\" name=\"customer[phone]\" />\n        </div>\n        <div class=\"submit\">\n          <input class=\"submit-button\" type=\"submit\" value=\"change phone\">\n        </div>\n    {{/if}}\n{{/bind_customer_phone_form}}\n\n<script>\n  class BindCustomerPhone {\n    constructor() {\n      this.form = document.querySelector('#bind-customer-phone');\n      this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n      const checkTag = this.form.getAttribute('data-check');\n      if (checkTag === 'true') { // need verifyCode\n        // init step1 event\n        this.formStep1 = this.form.querySelector('.step1-form')\n        this.verifyCodeButtonStep1 = this.formStep1.querySelector('.verifycode-button');\n        this.verifyCodeButtonStep1.addEventListener(\n          'click', this.onSendVerifyCodeHandlerSetp1.bind(this)\n        );\n\n        this.nextStepButton = this.form.querySelector('.next-step-button')\n        this.nextStepButton.addEventListener(\n          'click', this.onNextStepHandler.bind(this)\n        );\n        // init step2 event\n        this.formStep2 = this.form.querySelector('.step2-form')\n        this.verifyCodeButtonStep2 = this.formStep2.querySelector('.verifycode-button');\n        this.verifyCodeButtonStep2.addEventListener(\n          'click', this.onSendVerifyCodeHandlerSetp2.bind(this)\n        );\n      }\n\n      this.bindCustomerPhone = new window.Shopline.customerAccount.BindCustomerPhone(this.form);\n    }\n\n    onSendVerifyCodeHandlerSetp1(e) { // send verifyCode in setp1\n      e.preventDefault();\n\n      if (this.verifyCodeButtonStep1.getAttribute('aria-disabled') === 'true') return;\n      this.verifyCodeButtonStep1.setAttribute('aria-disabled', true);\n      \n      this.bindCustomerPhone.sendVerifyCodeStep1()\n        .then(response => {\n          // you can get the current encrypted email/phone by response.data.emailMask/mobileMask. e.g. m****<EMAIL>\n          console.log('send verfiyCode to:', response.data.emailMask || response.data.mobileMask);\n        })\n        .catch((error) => {\n          console.log(error)\n          this.verifyCodeButtonStep1.removeAttribute('aria-disabled');\n        })\n    }\n\n    onNextStepHandler(e) { // go to next step\n      e.preventDefault();\n      this.bindCustomerPhone.nextStep()\n        .then(response => {\n          // show next step form when succeed\n          this.formStep1.style.display = 'none';\n          this.formStep2.style.display = 'block';\n        })\n        .catch((error) => {\n          console.log(error)\n        })\n    }\n\n    onSendVerifyCodeHandlerSetp2(e) { // send verifyCode in setp2\n      e.preventDefault();\n\n      if (this.verifyCodeButtonStep2.getAttribute('aria-disabled') === 'true') return;\n      this.verifyCodeButtonStep2.setAttribute('aria-disabled', true);\n      \n      this.bindCustomerPhone.sendVerifyCodeStep2()\n        .then(response => {\n          console.log(response);\n        })\n        .catch((error) => {\n          console.log(error)\n          this.verifyCodeButtonStep2.removeAttribute('aria-disabled');\n        })\n    }\n\n    onSubmitHandler(e) { // submit form\n      e.preventDefault();\n\n      this.bindCustomerPhone.submit()\n        .then(response => {\n          window.location.href = '/user/center';\n        })\n        .catch(error => {\n          console.log(error)\n        })\n    }\n  }\n\n  window.Shopline.loadFeatures(\n    [\n      {\n        name: 'customer-account-api',\n        version: '0.3'\n      }\n    ],\n    function(error) {\n      if (error) {\n        throw error;\n      }\n\n      new BindCustomerPhone();\n    }\n  );\n</script>", "syntax": "{{#bind_customer_phone_form}}\n    form_content\n{{/bind_customer_phone_form}}"}], "link": "https://developer.shopline.com/docs/sline/tag/bind-customer-phone-form", "name": "bind_customer_phone_form", "summary": "Generates a form for change customer phone.\n\n## Form Input\n**customer[verifycode1]** number\n\nVerification code which used to verify the current phone. Required when the merchant opens the identity verification.\n\n**customer[phone]** phone **required**\n\nMobile phone. The mobile phone number format is an international closed phone number, e.g. China cell phone number: ***********.\n\n**customer[code]** number **required**\n\nArea code. The area code format is an international telephone area code, for example, China cell phone area code: 86.\n\n**customer[verifycode]** number\n\nVerification code which used to verify the new phone. Required when the merchant opens the identity verification.", "summary_cn": "生成修改用户手机号表单。\n\n## 表单输入\n**customer[verifycode1]** number\n\n用于验证当前手机号的验证码，商家开启身份验证时必填。\n\n**customer[phone]** phone **必填**\n\n手机号，格式为国际电话号码, 例如中国手机号码：***********。\n\n**customer[code]** number **必填**\n\n国际电话区号，例如中国手机区号：86。\n\n**customer[verifycode]** number\n\n用于验证新手机号的验证码，商家开启身份验证时必填。", "syntax": "{{#bind_customer_phone_form}}\n    form_content\n{{/bind_customer_phone_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": " Bind customer email", "name_cn": "修改客户邮箱", "path": "/user/center", "raw_sline": "{{#bind_customer_email_form id=\"bind-customer-email\"}}\n    {{#if form.register_config.check_tag}}\n        <div class=\"step1-form\">\n          <div class=\"verifycode\">\n            <input type=\"text\" name=\"customer[verifycode1]\" placeholder=\"verifycode\" />\n            <button class=\"verifycode-button\">send</button>\n          </div>\n          <div>\n            <button class=\"next-step-button\">next</button>\n          </div>\n        </div>\n        <div class=\"step2-form\" style=\"display: none;\">\n          <div>\n            <label for=\"email\">new email</label>\n            <input id=\"email\" type=\"text\" name=\"customer[email]\" />\n          </div>\n          <div class=\"verifycode\">\n            <input type=\"text\" name=\"customer[verifycode]\" placeholder=\"verifycode\" />\n            <button class=\"verifycode-button\">send</button>\n          </div>\n          <div class=\"submit\">\n            <input class=\"submit-button\" type=\"submit\" value=\"change email\">\n          </div>\n        </div>\n    {{else}}\n        <div>\n          <label for=\"email\">new email</label>\n          <input id=\"email\" type=\"text\" name=\"customer[email]\" />\n        </div>\n        <div class=\"submit\">\n          <input class=\"submit-button\" type=\"submit\" value=\"change email\">\n        </div>\n    {{/if}}\n{{/bind_customer_email_form}}\n\n<script>\n  class BindCustomerEmail {\n    constructor() {\n      this.form = document.querySelector('#bind-customer-email');\n      this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n      const checkTag = this.form.getAttribute('data-check');\n      if (checkTag === 'true') { // need verifyCode\n        // init step1 event\n        this.formStep1 = this.form.querySelector('.step1-form')\n        this.verifyCodeButtonStep1 = this.formStep1.querySelector('.verifycode-button');\n        this.verifyCodeButtonStep1.addEventListener(\n          'click', this.onSendVerifyCodeHandlerSetp1.bind(this)\n        );\n\n        this.nextStepButton = this.form.querySelector('.next-step-button')\n        this.nextStepButton.addEventListener(\n          'click', this.onNextStepHandler.bind(this)\n        );\n        // init step2 event\n        this.formStep2 = this.form.querySelector('.step2-form')\n        this.verifyCodeButtonStep2 = this.formStep2.querySelector('.verifycode-button');\n        this.verifyCodeButtonStep2.addEventListener(\n          'click', this.onSendVerifyCodeHandlerSetp2.bind(this)\n        );\n      }\n\n      this.bindCustomerEmail = new window.Shopline.customerAccount.BindCustomerEmail(this.form);\n    }\n\n    onSendVerifyCodeHandlerSetp1(e) { // send verifyCode in setp1\n      e.preventDefault();\n\n      if (this.verifyCodeButtonStep1.getAttribute('aria-disabled') === 'true') return;\n      this.verifyCodeButtonStep1.setAttribute('aria-disabled', true);\n      \n      this.bindCustomerEmail.sendVerifyCodeStep1()\n        .then(response => {\n          // you can get the current encrypted email/phone by response.data.emailMask/mobileMask. e.g. m****<EMAIL>\n          console.log('send verfiyCode to:', response.data.emailMask || response.data.mobileMask);\n        })\n        .catch((error) => {\n          console.log(error)\n          this.verifyCodeButtonStep1.removeAttribute('aria-disabled');\n        })\n    }\n\n    onNextStepHandler(e) { // go to next step\n      e.preventDefault();\n      this.bindCustomerEmail.nextStep()\n        .then(response => {\n          // show next step form when succeed\n          this.formStep1.style.display = 'none';\n          this.formStep2.style.display = 'block';\n        })\n        .catch((error) => {\n          console.log(error)\n        })\n    }\n\n    onSendVerifyCodeHandlerSetp2(e) { // send verifyCode in setp2\n      e.preventDefault();\n\n      if (this.verifyCodeButtonStep2.getAttribute('aria-disabled') === 'true') return;\n      this.verifyCodeButtonStep2.setAttribute('aria-disabled', true);\n      \n      this.bindCustomerEmail.sendVerifyCodeStep2()\n        .then(response => {\n          console.log(response);\n        })\n        .catch((error) => {\n          console.log(error)\n          this.verifyCodeButtonStep2.removeAttribute('aria-disabled');\n        })\n    }\n\n    onSubmitHandler(e) { // submit form\n      e.preventDefault();\n\n      this.bindCustomerEmail.submit()\n        .then(response => {\n          window.location.href = '/user/center';\n        })\n        .catch(error => {\n          console.log(error)\n        })\n    }\n  }\n\n  window.Shopline.loadFeatures(\n    [\n      {\n        name: 'customer-account-api',\n        version: '0.3'\n      }\n    ],\n    function(error) {\n      if (error) {\n        throw error;\n      }\n\n      new BindCustomerEmail();\n    }\n  );\n</script>", "syntax": "{{#bind_customer_email_form}}\n    form_content\n{{/bind_customer_email_form}}"}], "link": "https://developer.shopline.com/docs/sline/tag/bind-customer-email-form", "name": "bind_customer_email_form", "summary": "Generates a form for change customer email.\n\n## Form Input\n**customer[verifycode1]** number\n\nVerification Code which used to verify the current email. Required when the merchant opens the identity verification.\n\n**customer[email]** email **required**\n\nEmail.\n\n**customer[verifycode]** number\n\nVerification Code which used to verify the new email. Required when the merchant opens the identity verification.", "summary_cn": "生成修改用户邮箱表单。\n\n## 表单输入\n**customer[verifycode1]** number\n\n用于验证当前邮箱的验证码，商家开启身份验证时必填。\n\n**customer[email]** email **必填**\n\n邮箱。\n\n**customer[verifycode]** number\n\n用于验证新邮箱的验证码，商家开启身份验证时必填。", "syntax": "{{#bind_customer_email_form}}\n    form_content\n{{/bind_customer_email_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Update customer", "name_cn": "更新客户信息", "path": "/user/center", "raw_sline": "{{#update_customer_form}}\n  {{#if form.posted_successfully}}\n    <div>success</div>\n  {{/if}}\n  {{#if form.errors.messages}}\n    <div>{{ form.errors.messages }}</div>\n  {{/if}}\n  <div>\n    <label for=\"first_name\">first_name</label>\n    <input id=\"first_name\" type=\"text\" name=\"customer[first_name]\" />\n  </div>\n\n  <div>\n    <label for=\"last_name\">last_name</label>\n    <input id=\"last_name\" type=\"text\" name=\"customer[last_name]\" />\n  </div>\n\n  <div>\n    <label for=\"birthday\">birthday</label>\n    <input id=\"birthday\" type=\"text\" name=\"customer[birthday]\" />\n  </div>\n\n  <div>\n    <label for=\"gender\">gender</label>\n\n    <div>\n      <input type=\"radio\" id=\"male\" name=\"customer[gender]\" value=\"1\" checked>\n      <label for=\"male\">male</label>\n    </div>\n\n    <div>\n      <input type=\"radio\" id=\"female\" name=\"customer[gender]\" value=\"2\">\n      <label for=\"female\">female</label>\n    </div>\n\n    <div>\n      <input type=\"radio\" id=\"secret\" name=\"customer[gender]\" value=\"3\">\n      <label for=\"secret\">secret</label>\n    </div>\n  </div>\n\n  <div class=\"submit\">\n    <input class=\"submit-button\" type=\"submit\" value=\"save\">\n  </div>\n{{/update_customer_form}}", "syntax": "{{#update_customer_form}}\n    form_content\n{{/update_customer_form}}"}, {"description": "The `return_to` parameter allows you to specify a URL to redirect to. ", "description_cn": "使用 `return_to` 参数可以指定表单提交后重定向到的页面。", "name": "Specify a URL to redirect to", "name_cn": "自定义跳转", "path": "/user/center", "raw_sline": "{{#update_customer_form return_to=routes.root_url}}\n  {{#if form.posted_successfully}}\n    <div>success</div>\n  {{/if}}\n  {{#if form.errors.messages}}\n    <div>{{ form.errors.messages }}</div>\n  {{/if}}\n  <div>\n    <label for=\"first_name\">first_name</label>\n    <input id=\"first_name\" type=\"text\" name=\"customer[first_name]\" />\n  </div>\n\n  <div class=\"submit\">\n    <input class=\"submit-button\" type=\"submit\" value=\"save\">\n  </div>\n{{/update_customer_form}}", "syntax": "{{#update_customer_form return_to=routes.root_url}}\n    form_content\n{{/update_customer_form}}"}], "hashs": [{"description": "Redirect URL after form submit, defaults to the current page URL.", "description_cn": "表单提交后的跳转地址，默认为当前页面地址。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/update-customer-form", "name": "update_customer_form", "summary": "Generates a form for update customer info.\n\n## Form Input\n**customer[first_name]** string\n\nFirst name.\n\n**customer[last_name]** string\n\nLast name.\n\n**customer[birthday]** string\n\nBirthday. Format: `YYYYMMDD`, example: `19990101`.\n\n**customer[gender]** number\n\nGender. Valid values are:\n- `1`: male\n- `2`: female\n- `3`: secret", "summary_cn": "生成更新个人信息表单。\n\n## 表单输入\n**customer[first_name]** string\n\n姓氏。\n\n**customer[last_name]** string\n\n名字。\n\n**customer[birthday]** string\n\n生日。格式：`YYYYMMDD`，例如：`19990101`.\n\n**customer[gender]** number\n\n性别。有效枚举值包含：\n- `1`: 男\n- `2`: 女\n- `3`: 保密", "syntax": "{{#update_customer_form}}\n    form_content\n{{/update_customer_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Reset customer password", "name_cn": "重置密码", "path": "/user/passwordNew", "raw_sline": "{{#reset_customer_password_form id=\"reset-password-form\"}}\n{{#var support_type_list = form.register_config.types | split(\",\") /}}\n{{#var support_email = support_type_list | contains(\"email\") /}}\n{{#var support_mobile = support_type_list | contains(\"mobile\") /}}\n\n{{#if support_email }}\n<div class=\"account\">\n  <label for=\"email\">email</label>\n  <input type=\"text\" name=\"customer[email]\" placeholder=\"email\" />\n</div>\n{{/if}}\n\n{{#if support_mobile }}\n<div class=\"account\">\n  <label for=\"phone\">phone</label>\n  <input type=\"text\" name=\"customer[code]\" placeholder=\"code\" />\n  <input type=\"text\" name=\"customer[phone]\" placeholder=\"phone\" />\n</div>\n{{/if}}\n\n<div class=\"verifycode\">\n  <input type=\"text\" name=\"customer[verifycode]\" placeholder=\"verifycode\" />\n  <button class=\"verifycode-button\">send</button>\n</div>\n\n<div class=\"password\">\n  <label for=\"password\">password</label>\n  <input type=\"password\" name=\"customer[password]\" placeholder=\"password\" />\n</div>\n\n<div class=\"password\">\n  <label for=\"passwordConfirm\">confirm password</label>\n  <input type=\"password\" name=\"customer[password_confirm]\" placeholder=\"confirm password\" />\n</div>\n\n<div class=\"submit\">\n  <input class=\"submit-button\" type=\"submit\" value=\"save\">\n</div>\n{{/reset_customer_password_form}}\n\n<script>\nclass PasswordNew {\n  constructor() {\n    this.form = document.querySelector('#reset-password-form');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n    this.verifyCodeButton = this.form.querySelector('.verifycode-button');\n    this.verifyCodeButton.addEventListener(\n      'click', this.onSendVerifyCodeHandler.bind(this)\n    );\n\n    this.passwordNew = new window.Shopline.customerAccount.PasswordNew(this.form)\n  }\n\n  onSendVerifyCodeHandler(e) {\n    e.preventDefault();\n\n    if (this.verifyCodeButton.getAttribute('aria-disabled') === 'true') return;\n    this.verifyCodeButton.setAttribute('aria-disabled', true);\n\n    this.passwordNew.sendVerifyCode()\n      .then(response => {\n        if (response.errorMessage) {\n          this.verifyCodeError = true;\n        }\n      })\n      .finally(() => {\n        if (!this.verifyCodeError) {\n          this.verifyCodeButton.removeAttribute('aria-disabled');\n        }\n      })\n  }\n\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.passwordNew.submit()\n      .then(response => {\n        window.location.href = '/user/signIn';\n      })\n      .catch(error => {\n        console.log(error)\n      })\n  }\n}\n\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'customer-account-api',\n      version: '0.3'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n    new PasswordNew();\n  }\n);\n</script>", "syntax": "{{#reset_customer_password_form}}\n    form_content\n{{/reset_customer_password_form}}"}], "link": "https://developer.shopline.com/docs/sline/tag/reset-customer-password-form", "name": "reset_customer_password_form", "summary": "Generate a Forgot Password form.\n\n## Form Input\n**customer[email]** email\n\nEmail. Available when the merchant enables email registration. Required when reset password by email. You can only submit one of `customer[email]` and `customer[phone]` at a time.\n\n**customer[phone]** phone\n\nMobile phone. Available when the merchant enables phone number registration. Required when reset password by phone. The mobile phone number format is an international closed phone number, e.g. China cell phone number: ***********.\n\n**customer[code]** number\n\nArea code. Use with `customer[phone]`. The area code format is an international telephone area code, for example, China cell phone area code: 86.\n\n**customer[password]** string **required**\n\nNew password.\n\nMaximum length: 18\n\nMinimum length: 6\n\n**customer[password_confirm]** string **required**\n\nSecond confirmation of new password.\n\nMaximum length: 18\n\nMinimum length: 6\n\n**customer[verifycode]** number **required**\n\nVerification code.", "summary_cn": "生成忘记密码表单。\n\n## 表单输入\n**customer[email]** email\n\n邮箱。商家开启邮箱注册时可用，使用邮箱重置密码时必填。`customer[email]` 和 `customer[phone]` 只能同时提交其中之一。\n\n**customer[phone]** phone\n\n手机号。商家开启手机号注册时可用，使用手机号重置密码时必填。格式为国际电话号码, 例如中国手机号码：***********。\n\n**customer[code]** number\n\n区号。和 `customer[phone]` 一起使用，格式为国际电话区号，例如大陆手机区号：86。\n\n**customer[password]** string **必填**\n\n新密码。\n\n最大长度限制：18\n\n最小长度限制：6\n\n**customer[password_confirm]** string **必填**\n\n二次确认新密码。\n\n最大长度限制：18\n\n最小长度限制：6\n\n**customer[verifycode]** number **必填**\n\n验证码。", "syntax": "{{#reset_customer_password_form}}\n    form_content\n{{/reset_customer_password_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Delete customer", "name_cn": "注销账号", "path": "/user/center", "raw_sline": "{{#delete_customer_form id=\"delete-customer\"}}\n<div class=\"verifycode\">\n  <input type=\"text\" name=\"customer[verifycode]\" placeholder=\"verifycode\" />\n  <button class=\"verifycode-button\">send</button>\n</div>\n\n<div class=\"submit\">\n  <input class=\"submit-button\" type=\"submit\" value=\"delete account\">\n</div>\n{{/delete_customer_form}}\n\n<script>\nclass DeleteCustomer {\n  constructor() {\n    this.form = document.querySelector('#delete-customer');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n    this.verifyCodeButton = this.form.querySelector('.verifycode-button');\n    this.verifyCodeButton.addEventListener(\n      'click', this.onSendVerifyCodeHandler.bind(this)\n    );\n\n    this.deleteCustomer = new window.Shopline.customerAccount.DeleteCustomer(this.form)\n  }\n\n  // Send email or SMS verification code\n  onSendVerifyCodeHandler(e) {\n    e.preventDefault();\n\n    if (this.verifyCodeButton.getAttribute('aria-disabled') === 'true') return;\n    this.verifyCodeButton.setAttribute('aria-disabled', true);\n\n    this.deleteCustomer.sendVerifyCode()\n      .then(response => {\n        if (response.errorMessage) {\n          // Handle sending failure exceptions\n          console.log(response.errorMessage);\n        }\n      })\n      .finally(() => {\n        if (!this.verifyCodeError) {\n          this.verifyCodeButton.removeAttribute('aria-disabled');\n        }\n      })\n  }\n\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.deleteCustomer.submit()\n      .then(response => {\n        window.location.href = '/user/signIn';\n      })\n      .catch(error => {\n        // Handle failure exceptions\n        console.log(error)\n      })\n  }\n}\n\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'customer-account-api',\n      version: '0.3'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n    new DeleteCustomer();\n  }\n);\n</script>", "syntax": "{{#delete_customer_form}}\n    form_content\n{{/delete_customer_form}}"}], "link": "https://developer.shopline.com/docs/sline/tag/delete-customer-form", "name": "delete_customer_form", "summary": "Generate a form for deleting an account.\n## Form Input\n\n**customer[verifycode]** number **required**\n\nVerification code.", "summary_cn": "生成注销账号表单。\n## 表单输入\n\n**customer[verifycode]** number **必填**\n\n验证码。", "syntax": "{{#delete_customer_form}}\n    form_content\n{{/delete_customer_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Customer login", "name_cn": "客户登录", "path": "/user/signIn", "raw_sline": "{{#customer_login_form id=\"customer-login-form\"}}\n{{#var support_type_list = form.register_config.types | split(\",\") /}}\n{{#var support_email = support_type_list | contains(\"email\") /}}\n{{#var support_mobile = support_type_list | contains(\"mobile\") /}}\n\n{{#if support_email }}\n<div class=\"account\">\n  <label for=\"email\">email</label>\n  <input type=\"text\" name=\"customer[email]\" placeholder=\"email\" />\n</div>\n{{/if}}\n\n{{#if support_mobile }}\n<div class=\"account\">\n  <label for=\"phone\">phone</label>\n  <input type=\"text\" name=\"customer[code]\" placeholder=\"code\" />\n  <input type=\"text\" name=\"customer[phone]\" placeholder=\"phone\" />\n</div>\n{{/if}}\n\n<div class=\"password\">\n  <label for=\"password\">password</label>\n  <input type=\"password\" name=\"customer[password]\" placeholder=\"password\" />\n</div>\n\n<div class=\"submit\">\n  <input class=\"submit-button\" type=\"submit\" value=\"signIn\">\n</div>\n{{/customer_login_form}}\n\n<script>\nclass Login {\n  constructor() {\n    this.form = document.querySelector('#customer-login-form');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n    this.login = new window.Shopline.customerAccount.Login(this.form)\n  }\n\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.login.submit()\n      .then(response => {\n        window.location.href = '/user/center';\n      })\n      .catch(error => {\n        // Handling login failure exception scenarios\n      })\n  }\n}\n\n// Initialize user login JS SDK\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'customer-account-api',\n      version: '0.3'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n\n    new Login();\n  }\n);\n</script>", "syntax": "{{#customer_login_form}}\n    form_content\n{{/customer_login_form}}"}, {"name": "Third Party Login", "name_cn": "第三方登录", "path": "/user/signIn", "raw_sline": "{{#customer_login_form id=\"customer-login-form\"}}\n<div class=\"account\">\n  <label for=\"email\">email</label>\n  <input type=\"text\" name=\"customer[email]\" placeholder=\"email\" />\n</div>\n\n<div class=\"password\">\n  <label for=\"password\">password</label>\n  <input type=\"password\" name=\"customer[password]\" placeholder=\"password\" />\n</div>\n\n<div class=\"submit\">\n  <input class=\"submit-button\" type=\"submit\" value=\"signIn\">\n</div>\n\n<div id=\"third-login-container\"></div>\n{{/customer_login_form}}\n\n<script>\nclass Login {\n  constructor() {\n    this.form = document.querySelector('#customer-login-form');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n    this.login = new window.Shopline.customerAccount.Login(this.form, {\n      thirdLogin: {\n        container: this.form.querySelector('#third-login-container'), // Third party login button container\n        handleSuccess: () => { // Third party login is successful, you can customize the jump address\n          window.location.href = '/user/center';\n        },\n        handleError: (error) => { // Third party login failed, error message returned\n          console.log(error)\n        }\n      }\n    })\n  }\n\n  // Login Logic\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.login.submit()\n      .then(response => {\n        window.location.href = '/user/center';\n      })\n      .catch(error => {\n        // Handling login failure exception scenarios\n        console.log(error)\n      })\n  }\n}\n\n// Initialize user login JS SDK\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'customer-account-api',\n      version: '0.3'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n\n    new Login();\n  }\n);\n</script>", "syntax": "{{#customer_login_form thirdLogin=\"true\"}}\n    form_content\n{{/customer_login_form}}"}, {"description": "SHOPLINE Admin can be configured to support unregistered customers activating accounts in login pages.", "description_cn": "SHOPLINE 商家后台可配置支持未注册客户在登录页激活账号。", "name": "Activate accounts in login pages", "name_cn": "未注册客户在登录页激活账号", "path": "/user/signIn", "raw_sline": "{{#customer_login_form id=\"customer-login-form\"}}\n  <div class=\"account\">\n    <label for=\"email\">email</label>\n    <input type=\"text\" name=\"customer[acct]\" placeholder=\"email\" />\n  </div>\n  \n  <div class=\"password\">\n    <label for=\"password\">password</label>\n    <input type=\"password\" name=\"customer[password]\" placeholder=\"password\" />\n  </div>\n\n  {{#if form.register_config.activation_tag}}\n    <div class=\"verifycode\" style=\"display: none;\">\n      <input type=\"text\" name=\"customer[verifycode]\" placeholder=\"verifycode\" />\n      <button class=\"verifycode-button\" id=\"customer-login-activate-send-btn\">send</button>\n    </div>\n  {{/if}}\n\n  <div class=\"submit\">\n    <input class=\"submit-button\" type=\"submit\" value=\"signIn\">\n  </div>\n{{/customer_login_form}}\n\n<script>\nclass Login {\n    constructor() {\n      this.form = document.querySelector('#customer-login-form');\n      this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n      this.verifyCodeFormItem = this.form.querySelector('.verifycode');\n      this.submitBtn = this.form.querySelector('.submit-button');\n\n      this.verifyCodeButton = this.form.querySelector('.verifycode-button');\n      this.verifyCodeButton.addEventListener(\n        'click', this.onSendVerifyCodeHandler.bind(this)\n      );\n\n      this.login = new window.Shopline.customerAccount.Login(this.form, {\n        activate: {\n          verifyCodeBtn: 'customer-login-activate-send-btn'\n        }\n      })\n    }\n\n    // Sending email or SMS verification code\n    onSendVerifyCodeHandler(e) {\n      e.preventDefault();\n\n      if (this.verifyCodeButton.getAttribute('aria-disabled') === 'true') return;\n      this.verifyCodeButton.setAttribute('aria-disabled', true);\n      \n      this.login.sendVerifyCode()\n        .then(response => {\n          if (response.errorMessage) {\n            this.verifyCodeError = true;\n            // Handling send failure exception scenarios\n          }\n        })\n        .finally(() => {\n          if (!this.verifyCodeError) {\n            this.verifyCodeButton.removeAttribute('aria-disabled');\n          }\n        })\n    }\n\n    // Login Logic\n    onSubmitHandler(e) {\n      e.preventDefault();\n\n      this.login.submit()\n        .then(response => {\n          window.location.href = '/user/center';\n        })\n        .catch(error => {\n          // Handling login failure exception scenarios\n          console.log('----submit error', error);\n          if (error.code === 'needActivate') {\n            // Account can be activated. Display the verification code input box and modify the text as needed\n            this.verifyCodeFormItem.style.display = 'block';\n            this.submitBtn.value = 'activate';\n          }\n        })\n    }\n  }\n  \n  // Initialize user login JS SDK\n  window.Shopline.loadFeatures(\n    [\n      {\n        name: 'customer-account-api',\n        version: '0.3'\n      }\n    ],\n    function(error) {\n      if (error) {\n        throw error;\n      }\n\n      new Login();\n    }\n  );\n</script>", "syntax": "{{#customer_login_form}}\n    form_content\n{{/customer_login_form}}"}], "hashs": [{"description": "Enable third-party login.", "description_cn": "开启第三方登录。", "name": "third<PERSON><PERSON><PERSON>", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/customer-login-form", "name": "customer_login_form", "summary": "Generates a form for logging into a customer account.\n\n## Form Input\n**customer[email]** email\n\nEmail. Available when the merchant enables email registration. Required when login by email. You can only submit one of `customer[email]` and `customer[phone]` at a time.\n\n**customer[phone]** phone\n\nMobile phone. Available when the merchant enables phone number registration. Required when login by phone. The mobile phone number format is an international closed phone number, e.g. China cell phone number: ***********.\n\n**customer[code]** number\n\nArea code. Use with `customer[phone]`. The area code format is an international telephone area code, for example, China cell phone area code: 86.\n\n**customer[password]** string **required**\n\nPassword.\n\nMaximum length: 18\n\nMinimum length: 6", "summary_cn": "生成客户登录表单。\n## 表单输入\n**customer[email]** email\n\n邮箱。商家开启邮箱注册时可用，使用邮箱登录时必填。`customer[email]` 和 `customer[phone]` 只能同时提交其中之一。\n\n**customer[phone]** phone\n\n手机号。商家开启手机号注册时可用，使用手机号登录时必填。格式为国际电话号码, 例如中国手机号码：***********。\n\n**customer[code]** number\n\n区号。和 `customer[phone]` 一起使用，格式为国际电话区号，例如大陆手机区号：86。\n\n**customer[password]** string **必填**\n\n密码。\n\n最大长度限制：18\n\n最小长度限制：6", "syntax": "{{#customer_login_form}}\n    form_content\n{{/customer_login_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Add shipping address", "name_cn": "新增收货地址", "path": "/user/address/new", "raw_sline": "{{#customer_address_form return_to=routes.account_url}}\n<input type=\"text\" name=\"address[address1]\" placeholder=\"address\" />\n<input class=\"submit-button\" type=\"submit\" value=\"submit\">\n{{/customer_address_form}}", "syntax": "{{#customer_address_form}}\n    form_content\n{{/customer_address_form}}"}, {"name": "Edit shipping address", "name_cn": "编辑收货地址", "path": "/user/address/{address_seq}/edit", "raw_sline": "{{#customer_address_form address=customer.editing_address return_to=routes.account_url}}\n<input type=\"text\" name=\"address[first_name]\" value=\"{{form.address.first_name}}\"\n/>\n<input class=\"submit-button\" type=\"submit\" value=\"submit\">\n{{/customer_address_form}}", "syntax": "{{#customer_address_form address=customer.editing_address}}\n    form_content\n{{/customer_address_form}}"}, {"name": "Delete shipping address", "name_cn": "删除收货地址", "path": "/user/center", "raw_sline": "{{#for address in customer.addresses}}\n    {{#customer_address_form address_seq=address.id}}\n        <input class=\"submit-button\" type=\"submit\" value=\"delete\">\n    {{/customer_address_form}}\n{{/for}}", "syntax": "{{#customer_address_form address_seq=address.id}}\n    form_content\n{{/customer_address_form}}"}], "hashs": [{"description": "Redirect URL after form submit, defaults to the current page URL.", "description_cn": "表单提交后的跳转地址，默认为当前页面地址。", "name": "return_to", "types": ["string"]}, {"description": "The id field of the address and is used to process the address to be deleted. You can obtain it through the `addresses` field in the `customer object`.", "description_cn": "地址的id字段，删除地址时传入。可以通过 `customer object` 中的 `addresses` 字段获取。", "name": "address_seq", "types": ["string"]}, {"description": "Address detailed data used for editing addresses. You can obtain it through the `addresses` field in the `customer object`.", "description_cn": "用于编辑地址的地址详细数据。可以通过 `customer object` 中的 `addresses` 字段获取。", "name": "address", "types": ["untyped"]}], "link": "https://developer.shopline.com/docs/sline/tag/customer-address-form", "name": "customer_address_form", "summary": "Generate forms for creating new addresses, editing or deleting existing addresses on customer accounts.\n\n## Form Input\n**address[first_name]** string\n\nThe first name of the recipient.\n\n**address[last_name]** string\n\nThe last name of the recipient.\n\n**address[address1]** string\n\nThe first line of the address. This typically includes information such as the street address or a post office box number.\n\n**address[address2]** string\n\nThe second line of the address. This typically includes information such as apartment, suite, or unit number.\n\n**address[city]** string\n\nThe city in the address.\n\n**address[city_code]** string\n\nThe code for the city in the address.\n\n**address[company]** string\n\nThe company name of the recipient.\n\n**address[country]** string\n\nThe country or region in the address.\n\n**address[country_code]** string\n\nA two-letter country or region code that follows the [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) standard, used to identify a specific country or region in the address. Example: `US`.\n\n**address[district]** string\n\nThe district or county in the address.\n\n**address[district_code]** string\n\nThe code for the district or county in the address.\n\n**address[phone]** string\n\nThe phone number of the recipient.\n\n**address[province]** string\n\nThe province in the address.\n\n**address[province_code]** string\n\nThe code for the province in the address, which can be a custom code or a two-digit [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) international code.\n\n**address[zip]** string\n\nThe postal code information of the address.\n\n**address[def]** boolean\nWhether the address is default address.\n- `true`: is default address。\n- `false`: not default address。", "summary_cn": "生成用于在客户帐户上创建新地址、编辑或删除现有地址的表单。\n\n## 表单输入\n**address[first_name]** string\n\n收件人的名。\n\n**address[last_name]** string\n\n收件人的姓。\n\n**address[address1]** string\n\n地址的第一行。通常是街道地址或邮政信箱编号等信息。\n\n**address[address2]** string\n\n地址的第二行。通常是公寓、套房或单元等信息。\n\n**address[city]** string\n\n地址中的城市。\n\n**address[city_code]** string\n\n地址中城市的编码。\n\n**address[company]** string\n\n收件人的公司名称。\n\n**address[country]** string\n\n地址中的国家或区域。\n\n**address[country_code]** string\n\n地址中国家或区域的二位码，遵循 [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) 国际标准，例如 `US`。\n\n**address[district]** string\n\n地址中的区或县。\n\n**address[district_code]** string\n\n地址中区或县的编码。\n\n**address[phone]** string\n\n收件人的手机号码。\n\n**address[province]** string\n\n地址中的省份。\n\n**address[province_code]** string\n\n地址中省份的编码，该编码可以是自定义编号或者为二位的 [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) 国际编码。\n\n**address[zip]** string\n\n地址的邮编信息。\n\n**address[def]** boolean\n\n是否默认地址。\n- `true`：默认地址。\n- `false`：非默认地址。", "syntax": "{{#customer_address_form}}\n    form_content\n{{/customer_address_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Footer subscription", "name_cn": "页脚订阅", "raw_sline": "{{#customer_form}}\n    {{#if form.posted_successfully}}\n      <div>Success</div>\n    {{/if}}\n    {{#if form.errors.messages}}\n      <div>{{ form.errors.messages }}</div>\n    {{/if}}\n    <input type=\"hidden\" name=\"contact[tags]\" value=\"home_page,footer\">\n    <div class=\"email\">\n      <label for=\"contact[email]\">Subscribe Email</label>\n      <input type=\"email\" name=\"contact[email]\" placeholder=\"Please enter your email address\" />\n    </div>\n    <div class=\"submit\">\n      <input class=\"submit-button\" type=\"submit\" value=\"Subscription\">\n    </div>\n{{/customer_form}}", "syntax": "{{#customer_form}}\n    form_content\n{{/customer_form}}"}], "hashs": [{"description": "Redirect URL after form submit, defaults to the current page URL.", "description_cn": "表单提交后的跳转地址，默认为当前页面地址。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/customer-form", "name": "customer_form", "summary": "Generate an email subscription form.\n\n## Form Input\n\n**contact[email]** email **requires**\n\nEmail.\n\n**contact[tags]** string\n\nTags, connect multiple with commas. Example: home_page,footer.", "summary_cn": "生成邮件订阅表单。\n\n## 表单输入\n\n**contact[email]** email **必填**\n\n邮箱。\n\n**contact[tags]** string\n\n标签，多个用 `,` 连接，例如：home_page,footer。", "syntax": "{{#customer_form}}\n    form_content\n{{/customer_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Customer register", "name_cn": "客户注册", "path": "/user/signUp", "raw_sline": "{{#create_customer_form id=\"create-customer-form\"}}\n{{#var support_type_list = form.register_config.types | split(\",\") /}}\n{{#var support_email = support_type_list | contains(\"email\") /}}\n{{#var support_mobile = support_type_list | contains(\"mobile\") /}}\n\n{{#if support_email }}\n<div class=\"account\">\n  <label for=\"email\">email</label>\n  <input type=\"text\" name=\"customer[email]\" placeholder=\"email\" />\n</div>\n{{/if}}\n\n{{#if support_mobile }}\n<div class=\"account\">\n  <label for=\"phone\">phone</label>\n  <input type=\"text\" name=\"customer[code]\" placeholder=\"code\" />\n  <input type=\"text\" name=\"customer[phone]\" placeholder=\"phone\" />\n</div>\n{{/if}}\n\n<div class=\"password\">\n  <label for=\"password\">password</label>\n  <input type=\"password\" name=\"customer[password]\" placeholder=\"password\" />\n</div>\n\n{{#if form.register_config.check_tag}}\n  <div class=\"verifycode\">\n    <input type=\"text\" name=\"customer[verifycode]\" placeholder=\"verifycode\" />\n    <button class=\"verifycode-button\">send</button>\n  </div>\n{{/if}}\n\n{{#if form.register_config.first_name_check}}\n  <div class=\"first-name\">\n    <label for=\"first-name\">firstName</label>\n    <input type=\"text\" name=\"customer[first_name]\" placeholder=\"firstName\" />\n  </div>\n{{/if}}\n\n{{#if form.register_config.last_name_check}}\n  <div class=\"last-name\">\n    <label for=\"last-name\">lastName</label>\n    <input type=\"text\" name=\"customer[last_name]\" placeholder=\"lastName\" />\n  </div>\n{{/if}}\n\n{{#if form.register_config.birthday_check}}\n  <div class=\"birthday\">\n    <label for=\"birthday\">birthday</label>\n    <input type=\"date\" name=\"customer[birthday]\" placeholder=\"birthday\" />\n  </div>\n{{/if}}\n\n{{#if form.register_config.gender_check}}\n    <select name=\"customer[gender]\">\n      <option value=\"0\">\n        {{\"customer.account.gender_secret\" | t()}}\n      </option>\n      <option value=\"1\">\n        {{\"customer.account.gender_male\" | t()}}\n      </option>\n      <option value=\"2\">\n        {{\"customer.account.gender_female\" | t()}}\n      </option>\n    </select>\n{{/if}}\n\n<div class=\"age\">\n  <label for=\"age\">age</label>\n  <input type=\"text\" name=\"attribute[age]\" placeholder=\"age\" />\n</div>\n\n<div class=\"accepts-marketing\">\n  <input type=\"checkbox\" name=\"customer[accepts_marketing]\" value=\"true\">\n  <label for=\"accepts-marketing\">Subscribe to email marketing</label>\n</div>\n\n<div class=\"submit\">\n  <input class=\"submit-button\" type=\"submit\" value=\"signUp\">\n</div>\n{{/create_customer_form}}\n\n<script>\nclass Register {\n  constructor() {\n    this.form = document.querySelector('#create-customer-form');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n    this.verifyCodeButton = this.form.querySelector('.verifycode-button');\n    this.verifyCodeButton.addEventListener(\n      'click', this.onSendVerifyCodeHandler.bind(this)\n    );\n\n    this.register = new window.Shopline.customerAccount.Register(this.form)\n  }\n\n  // Sending email or SMS verification code\n  onSendVerifyCodeHandler(e) {\n    e.preventDefault();\n\n    if (this.verifyCodeButton.getAttribute('aria-disabled') === 'true') return;\n    this.verifyCodeButton.setAttribute('aria-disabled', true);\n\n    this.register.sendVerifyCode()\n      .then(response => {\n        if (response.errorMessage) {\n          this.verifyCodeError = true;\n          // Handling send failure exception scenarios\n        }\n      })\n      .finally(() => {\n        if (!this.verifyCodeError) {\n          this.verifyCodeButton.removeAttribute('aria-disabled');\n        }\n      })\n  }\n\n  // Registration Logic\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.register.submit()\n      .then(response => {\n        window.location.href = '/user/center';\n      })\n      .catch(error => {\n        // Handling registration failure exception scenarios\n      })\n  }\n}\n\n// Initialize user login registration JS SDK\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'customer-account-api',\n      version: '0.3'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n\n    new Register();\n  }\n);\n</script>", "syntax": "{{#create_customer_form}}\n    form_content\n{{/create_customer_form}}"}], "link": "https://developer.shopline.com/docs/sline/tag/create-customer-form", "name": "create_customer_form", "summary": "Generates a form for creating a new customer account.\n\n## Form Input\n**customer[email]** email\n\nEmail. Available when the merchant enables email registration. Required when create by email. You can only submit one of `customer[email]` and `customer[phone]` at a time.\n\n**customer[phone]** phone\n\nMobile phone. Available when the merchant enables phone number registration. Required when create by phone. The mobile phone number format is an international closed phone number, e.g. China cell phone number: ***********.\n\n**customer[code]** number\n\nArea code. Use with `customer[phone]`. The area code format is an international telephone area code, for example, China cell phone area code: 86.\n\n**customer[password]** string **required**\n\nPassword.\n\nMaximum length: 18\n\nMinimum length: 6\n\n**customer[verifycode]** number\n\nVerification code. Required when the merchant opens the identity verification.\n\n**customer[first_name]** string\n\nThe first name of the customer.\n\n**customer[last_name]** string\n\nThe last name of the customer.\n\n**customer[birthday]** string\n\nThe birthday of the customer. Format: `YYYYMMDD`, example: `19990101`.\n\n**customer[gender]** number\n\nThe gender of the customer. Valid values are:\n- `1`: male\n- `2`: female\n- `3`: secret\n\n**customer[accepts_marketing]** boolean\n\nWhether the customer subscribe to marketing emails.\n\n**attribute[your_key]** string\n\nAny customer information you want to collect. e.g. `<input name=\"attribute[age]\" />`.\n", "summary_cn": "生成注册账号表单。\n\n## 表单输入\n**customer[email]** email\n\n邮箱。商家开启邮箱注册时可用，使用邮箱注册时必填。`customer[email]` 和 `customer[phone]` 只能同时提交其中之一。\n\n**customer[phone]** phone\n\n手机号。商家开启手机号注册时可用，使用手机号注册时必填。格式为国际电话号码, 例如中国手机号码：***********。\n\n**customer[code]** number\n\n区号。和 `customer[phone]` 一起使用，格式为国际电话区号，例如大陆手机区号：86。\n\n**customer[password]** string **required**\n\n密码。\n\n最大长度限制：18\n\n最小长度限制：6\n\n**customer[verifycode]** number\n\n验证码，商家开启身份验证时必填。\n\n**customer[first_name]** string\n\n姓氏。\n\n**customer[last_name]** string\n\n名字。\n\n**customer[birthday]** string\n\n生日。格式：`YYYYMMDD`，例如：`19990101`。\n\n**customer[gender]** number\n\n性别。有效枚举值包含：\n- `1`: 男\n- `2`: 女\n- `3`: 保密\n\n**customer[accepts_marketing]** boolean\n\n客户是否接受电子邮件订阅。\n\n**attribute[your_key]** string\n\n任意你想要收集的客户信息，例如：`<input name=\"attribute[age]\" />`。", "syntax": "{{#create_customer_form}}\n    form_content\n{{/create_customer_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Activate customer", "name_cn": "激活客户", "path": "/user/activate", "raw_sline": "{{#activate_customer_password_form id=\"customer-activate-form\"}}\n<div class=\"password\">\n  <label for=\"customer[password]\">user password</label>\n  <input type=\"password\" name=\"customer[password]\" placeholder=\"user password\" />\n</div>\n\n<div class=\"accepts-marketing\">\n  <input type=\"checkbox\" name=\"customer[accepts_marketing]\" value=\"true\">\n  <label for=\"accepts-marketing\">Subscribe to email marketing</label>\n</div>\n\n<div class=\"submit\">\n  <input class=\"submit-button\" type=\"submit\" value=\"Submit\">\n</div>\n{{/activate_customer_password_form}}\n\n<script>\nclass Activate {\n  constructor() {\n    this.form = document.querySelector('#customer-activate-form');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n\n    this.activate = new window.Shopline.customerAccount.Activate(this.form)\n  }\n\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.activate.submit()\n      .then(response => {\n        window.location.href = '/user/signIn';\n      })\n      .catch(error => {\n        // Handling failure\n        console.log(error)\n      })\n  }\n}\n\n// Initializing the JS SDK\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'customer-account-api',\n      version: '0.3'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n    new Activate();\n  }\n);\n</script>", "syntax": "{{#activate_customer_password_form}}\n    form_content\n{{/activate_customer_password_form}}"}], "link": "https://developer.shopline.com/docs/sline/tag/activate-customer-password-form", "name": "activate_customer_password_form", "summary": "Generate an account activation form with the required field password. token is required to activate the account, clicking on the bounce from the activation email will carry the token parameter in the url.\n\n## Form Input\n**customer[password]** string **required**\n\nPassword.\n\nMaximum length: 18\n\nMinimum length: 6\n\n**customer[accepts_marketing]** boolean\n\nWhether the customer subscribe to marketing emails.", "summary_cn": "生成激活账号表单。商家可以给账号未激活的客户发送激活邀请邮件，客户点击邮件中的账号激活链接将进入激活页面，输入密码后即可激活账号。\n\n## 表单输入\n\n**customer[password]** string **required**\n\n密码。\n\n最大长度限制：18\n\n最小长度限制：6\n\n**customer[accepts_marketing]** boolean\n\n客户是否接受电子邮件订阅。", "syntax": "{{#activate_customer_password_form}}\n    form_content\n{{/activate_customer_password_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"deprecated": false, "examples": [{"name": "Company account application", "name_cn": "公司账户申请", "path": "/user/company", "raw_sline": "{{#form \"company_account_application_form\"}}\n  {{#if form.posted_successfully}}\n    <div>Successfully submitted</div>\n  {{/if}}\n\n  {{#if form.errors.messages}}\n    <div>{{ form.errors.messages }}</div>\n  {{/if}}\n\n  <div class=\"email\">\n    <label for=\"company[email]\">email</label>\n    <input type=\"text\" name=\"company[email]\" placeholder=\"email\" />\n  </div>\n\n  <div class=\"company_name\">\n    <label for=\"company[company_name]\">company_name</label>\n    <input type=\"text\" name=\"company[company_name]\" placeholder=\"company_name\" />\n  </div>\n\n  <div class=\"submit\">\n    <input class=\"submit-button\" type=\"submit\" value=\"submit\">\n  </div>\n{{/company_account_application_form}}", "syntax": "{{#company_account_application_form}}\n    form_content\n{{/company_account_application_form}}"}], "hashs": [{"description": "Redirect URL after form submit, defaults to the current page URL.", "description_cn": "表单提交后的跳转地址，默认为当前页面地址。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/company-account-application-form", "name": "company_account_application_form", "summary": "Used to create a form for company account application. Only the store with the Enterprise plan can create company accounts for customers. \n\n## Form Input\n\n**company[email]** email **required**\n\nThe email of the main contact person of the company.\n\n**company[first_name]** string\n\nThe first name of the main contact person of the company.\n\n**company[last_name]** string\n\nThe last name of the main contact person of the company.\n\n**company[company_name]** string **required**\n\nThe name of the company.\n\n**company[shipping_address][mobile_phone]** string\n\nThe phone number of the shipping address.\n\n**company[shipping_address][addr]** string\n\nThe first line of the shipping address. This typically includes information such as the street address or a post office box number.\n\n**company[shipping_address][addr_two]** string\n\nThe second line of the shipping address. This typically includes information such as apartment, suite, or unit number.\n\n**company[shipping_address][country]** string\n\nThe country or region in the shipping address.\n\n**company[shipping_address][country_code]** string\n\nA two-letter country or region code that follows the [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) standard, used to identify a specific country or region in the shipping address. Example: `US`.\n\n**company[shipping_address][province]** string\n\nThe province in the shipping address.\n\n**company[shipping_address][province_code]** string\n\nThe code for the province in the shipping address, which can be a custom code or a two-digit [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) international code.\n\n**company[shipping_address][city]** string\n\nThe city in the shipping address.\n\n**company[shipping_address][city_code]** string\n\nThe code for the city in the shipping address.\n\n**company[shipping_address][district]** string\n\nThe district or county in the shipping address.\n\n**company[shipping_address][district_code]** string\n\nThe code for the district or county in the shipping address.\n\n**company[shipping_address][zip_code]** string\n\nThe postal code information of the shipping address.\n\n**company[billing_address][mobile_phone]** string\n\nThe phone number of the billing address.\n\n**company[billing_address][addr]** string\n\nThe first line of the billing address. This typically includes information such as the street address or a post office box number.\n\n**company[billing_address][addr_two]** string\n\nThe second line of the billing address. This typically includes information such as apartment, suite, or unit number.\n\n**company[billing_address][country]** string\n\nThe country or region in the billing address.\n\n**company[billing_address][country_code]** string\n\nA two-letter country or region code that follows the [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) standard, used to identify a specific country or region in the billing address. Example: `US`.\n\n**company[billing_address][province]** string\n\nThe province in the billing address.\n\n**company[billing_address][province_code]** string\n\nThe code for the province in the billing address, which can be a custom code or a two-digit [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) international code.\n\n**company[billing_address][city]** string\n\nThe city in the billing address.\n\n**company[billing_address][city_code]** string\n\nThe code for the city in the billing address.\n\n**company[billing_address][district]** string\n\nThe district or county in the billing address.\n\n**company[billing_address][district_code]** string\n\nThe code for the district or county in the billing address.\n\n**company[billing_address][zip_code]** string\n\nThe postal code information of the billing address.\n\n**attribute[your_key]** string\n\nAny company information you want to collect. e.g. `<input name=\"attribute[age]\" />`.", "summary_cn": "生成公司账户申请表单。该功能仅适用于购买了 SHOPLINE Enterprise 套餐的商家。\n\n## 表单输入\n\n**company[email]** email **必填**\n\n公司主要联系人的邮箱。\n\n**company[first_name]** string\n\n公司主要联系人的名字。\n\n**company[last_name]** string\n\n公司主要联系人的姓氏。\n\n**company[company_name]** string **必填**\n\n公司名称。\n\n**company[shipping_address][mobile_phone]** string\n\n收货地址的手机号码。\n\n**company[shipping_address][addr]** string\n\n收货地址的第一行。通常是街道地址或邮政信箱编号等信息。\n\n**company[shipping_address][addr_two]** string\n\n收货地址的第二行。通常是公寓、套房或单元等信息。\n\n**company[shipping_address][country]** string\n\n收货地址中的国家或区域。\n\n**company[shipping_address][country_code]** string\n\n收货地址中国家或区域的二位码，遵循 [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) 国际标准，例如 `US`。\n\n**company[shipping_address][province]** string\n\n收货地址中的省份。\n\n**company[shipping_address][province_code]** string\n\n收货地址中省份的编码，该编码可以是自定义编号或者为二位的 [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) 国际编码。\n\n**company[shipping_address][city]** string\n\n收货地址中的城市。\n\n**company[shipping_address][city_code]** string\n\n收货地址中城市的编码。\n\n**company[shipping_address][district]** string\n\n收货地址中的区或县。\n\n**company[shipping_address][district_code]** string\n\n收货地址中区或县的编码。\n\n**company[shipping_address][zip_code]** string\n\n收货地址的邮编信息。\n\n**company[billing_address][mobile_phone]** string\n\n账单地址的手机号码。\n\n**company[billing_address][addr]** string\n\n账单地址的第一行。通常是街道地址或邮政信箱编号等信息。\n\n**company[billing_address][addr_two]** string\n\n账单地址的第二行。通常是公寓、套房或单元等信息。\n\n**company[billing_address][country]** string\n\n账单地址中的国家或区域。\n\n**company[billing_address][country_code]** string\n\n账单地址中国家或区域的二位码，遵循 [ISO 3166-1 (alpha 2)](https://www.iso.org/glossary-for-iso-3166.html) 国际标准，例如 `US`。\n\n**company[billing_address][province]** string\n\n账单地址中的省份。\n\n**company[billing_address][province_code]** string\n\n账单地址中省份的编码，该编码可以是自定义编号或者为二位的 [ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) 国际编码。\n\n**company[billing_address][city]** string\n\n账单地址中的城市。\n\n**company[billing_address][city_code]** string\n\n账单地址中城市的编码。\n\n**company[billing_address][district]** string\n\n账单地址中的区或县。\n\n**company[billing_address][district_code]** string\n\n账单地址中区或县的编码。\n\n**company[billing_address][zip_code]** string\n\n账单地址的邮编信息。\n\n**attribute[your_key]** string\n\n任意你想要收集的公司信息，例如：`<input name=\"attribute[age]\" />`。", "syntax": "{{#company_account_application_form}}\n    form_content\n{{/company_account_application_form}}", "syntax_keywords": [{"description": "The form contents.", "description_cn": "表单内容。", "keyword": "form_content"}]}, {"arguments": [], "deprecated": false, "examples": [{"name": "Link to register", "name_cn": "注册链接", "raw_sline": "{{#link_to_customer_register \"Create an account\" /}}", "syntax": "{{#link_to_customer_register str /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-customer-register", "name": "link_to_customer_register", "summary": "Generates an HTML link to the customer registration page.", "summary_cn": "生成指向客户注册页面的 HTML 链接。", "syntax": "{{#link_to_customer_register str /}}", "syntax_keywords": [{"description": "Link text.", "description_cn": "链接文本。", "keyword": "str"}]}, {"deprecated": false, "examples": [{"name": "Link to logout", "name_cn": "登出链接", "raw_sline": "{{#link_to_customer_logout \"Logout\" /}}", "syntax": "{{#link_to_customer_logout str /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-customer-logout", "name": "link_to_customer_logout", "summary": "Generates an HTML hyperlink to the customer logout page.", "summary_cn": "生成指向客户登出页面的 HTML 链接。", "syntax": "{{#link_to_customer_logout str /}}", "syntax_keywords": [{"description": "Link text.", "description_cn": "链接文本。", "keyword": "str"}]}, {"deprecated": false, "examples": [{"name": "Link to login", "name_cn": "登录链接", "raw_sline": "{{#link_to_customer_login \"Log in\" /}}", "syntax": "{{#link_to_customer_login str /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-customer-login", "name": "link_to_customer_login", "summary": "Generates an HTML hyperlink to the customer login page.", "summary_cn": "生成指向客户登录页面的 HTML 链接。", "syntax": "{{#link_to_customer_login str /}}", "syntax_keywords": [{"description": "Link text.", "description_cn": "链接文本。", "keyword": "str"}]}, {"arguments": [{"description": "metafield data", "description_cn": "metafield 数据", "name": "metafield", "types": ["object"]}], "deprecated": false, "description": "Type: `boolean`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-boolean\"`\n\n---\n\nType: `date`\n\nTag: `<time>`\n\nAttribute: `datetime=\"<the metafield value>\" class=\"metafield-date\"`\n\n---\n\nType: `date_at_time`\n\nTag: `<time>`\n\nAttribute: `datetime=\"<the metafield value>\" class=\"metafield-date\"`\n\n---\n\nType: `json`\n\nTag: `<script>`\n\nAttribute: `type=\"application/json\" class=\"metafield-json\"`\n\n---\n\nType: `money`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-money\"`\n\n---\n\nType: `rich_text_field`\n\nTag: `<div>`\n\nAttribute: `class=\"metafield-rich_text_field\"`\n\n---\n\nType: `multi_line_text_field`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-multi_line_text_field\"`\n\n---\n\nType: `color`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-color\"`\n\n---\n\nType: `number_decimal`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-number_decimal\"`\n\n---\n\nType: `number_integer`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-number_integer\"`\n\n---\n\nType: `rating`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-rating\"`\n\n---\n\nType: `url`\n\nTag: `<a>`\n\nAttribute: `class=\"metafield-url\"`\n\n---\n\nType: `weight`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-weight\"`\n\n---\n\nType: `volume`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-volume\"`\n\n---\n\nType: `dimension`\n\nTag: `<span>`\n\nAttribute: `class=\"metafield-dimension\"`\n\n---\n\nType: `single_line_text_field` \n\nTag: `<span>` \n\nAttribute: `class=\"metafield-single_line_text_field\"`\n\n---\n\nType: `collection_reference` \n\nTag: `<a>` \n\nAttribute: `href=\"{{URL}}\" class=\"metafield-collection_reference\"`\n\n---\n\nType: `page_reference` \n\nTag: `<a>` \n\nAttribute: `href=\"{{URL}}\" class=\"metafield-page_reference\"`\n\n---\n\nType: `product_reference` \n\nTag: `<a>` \n\nAttribute: `href=\"{{URL}}\" class=\"metafield-product_reference\"`\n\n---\n\nType: `variant_reference`\n\nTag: `<a>`\n\nAttribute: `href=\"{{URL}}\" class=\"metafield-variant_reference\"`\n\n---\n\nType: `file_reference`\n\nTag: `<img>` | `<video>` | `<a>`\n\nAttribute: Different tags have different default attribute values\n\n---\n\n> Tip: Currently `metafield_tag` only supports single-line text `single_line_text_field` passing in multiple values.", "description_cn": "支持以下几种元字段类型：\n\n类型：`boolean`\n\n标签：`<span>`\n\n属性：`class=\"metafield-boolean\"`\n\n---\n\n类型：`date` \n\n标签：`<time>` \n\n属性：`datetime=\"<the metafield value>\" class=\"metafield-date\"`\n\n---\n\n类型：`date_at_time` \n\n标签：`<time>` \n\n属性：`datetime=\"<the metafield value>\" class=\"metafield-date\"`\n\n---\n\n类型：`json` \n\n标签：`<script>` \n\n属性：`type=\"application/json\" class=\"metafield-json\"`\n\n---\n\n类型：`money` \n\n标签：`<span>` \n\n属性：`class=\"metafield-money\"`\n\n---\n\n类型：`rich_text_field` \n\n标签：`<div>` \n\n属性：`class=\"metafield-rich_text_field\"`\n\n---\n\n类型：`multi_line_text_field` \n\n标签：`<span>` \n\n属性：`class=\"metafield-multi_line_text_field\"`\n\n---\n\n类型：`color` \n\n标签：`<span>` \n\n属性：`class=\"metafield-color\"`\n\n---\n\n类型：`number_decimal` \n\n标签：`<span>` \n\n属性：`class=\"metafield-number_decimal\"`\n\n---\n\n类型：`number_integer` \n\n标签：`<span>` \n\n属性：`class=\"metafield-number_integer\"`\n\n---\n\n类型：`rating` \n\n标签：`<span>` \n\n属性：`class=\"metafield-rating\"`\n\n---\n\n类型：`url` \n\n标签：`<a>` \n\n属性：`class=\"metafield-url\"`\n\n---\n\n类型：`weight` \n\n标签：`<span>` \n\n属性：`class=\"metafield-weight\"`\n\n---\n\n类型：`volume` \n\n标签：`<span>` \n\n属性：`class=\"metafield-volume\"`\n\n---\n\n类型：`dimension` \n\n标签：`<span>` \n\n属性：`class=\"metafield-dimension\"`\n\n---\n\n类型：`single_line_text_field` \n\n标签：`<span>` \n\n属性：`class=\"metafield-single_line_text_field\"`\n\n---\n\n类型：`collection_reference` \n\n标签：`<a>` \n\n属性：`href=\"{{URL}}\" class=\"metafield-collection_reference\"`\n\n---\n\n类型：`page_reference` \n\n标签：`<a>` \n\n属性：`href=\"{{URL}}\" class=\"metafield-page_reference\"`\n\n---\n\n类型：`product_reference` \n\n标签：`<a>` \n\n属性：`href=\"{{URL}}\" class=\"metafield-product_reference\"`\n\n---\n\n类型：`variant_reference`\n\n标签：`<a>`\n\n属性：`href=\"{{URL}}\" class=\"metafield-variant_reference\"`\n\n---\n\n类型：`file_reference`\n\n标签：`<img>` | `<video>` | `<a>`\n\n属性：不同标签会有不同的默认属性值\n\n---\n\n> 提示：目前 `metafield_tag` 仅支持单行文本 `single_line_text_field` 传入多个值。", "examples": [{"description": "To generate HTML containing metafield data, you need to use [get_metafields filter](/docs/sline/filter/get-metafields).", "description_cn": "生成生成包含元字段数据的 HTML 需要结合 [get_metafields filter](/docs/sline/filter/get-metafields) 使用。", "name": "Basic example", "name_cn": "基础示例", "path": "/products/earring-ab001", "raw_sline": "{{#var metafield_ns = product | get_metafields(\"my_fields\") /}}\n\n{{#metafield_tag metafield_ns.my_key /}}", "source_object": "product", "syntax": "{{#metafield_tag variable /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/metafield-tag", "name": "metafield_tag", "summary": "Generates HTML containing the metafield data.", "summary_cn": "生成包含元字段数据的 HTML 。", "syntax": "{{#metafield_tag metafield /}}", "syntax_keywords": []}, {"arguments": [{"description": "External video URL", "description_cn": "外部视频 URL", "name": "videoUrl", "types": ["string"]}], "deprecated": false, "description": "Currently only YouTube video links are supported.", "description_cn": "目前仅支持 YouTuBe 的视频链接。", "examples": [{"description": "The generation of the video player needs to be used in conjunction with [external_video_url](/docs/sline/filter/external-video-url).", "description_cn": "视频播放器的生成，需结合 [external_video_url](/docs/sline/filter/external-video-url) 使用。", "name": "Basic example", "name_cn": "基础示例", "path": "/products/earring-ab001", "raw_sline": "{{#for media in product.media}}\n    {{#switch media.media_type}}\n        {{#case \"external_video\" /}}\n            {{#if media.host == \"youtube\"}}\n                {{#external_video_tag media | external_video_url(autoplay=true, loop=false) /}}\n            {{/if}}\n    {{/switch}}\n{{/for}}", "source_object": "product", "syntax": "{{#external_video_tag media | external_video_url() /}}"}, {"description": "You can add standard-compliant [HTML iframe attributes](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/iframe#%E5%B1%9E%E6%80%A7) to the `<iframe>` tag, such as: `class`, `frameborder`, etc.", "description_cn": "你可以为 `<iframe>` 标签添加符合规范标准的 [HTML iframe 属性](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/iframe#%E5%B1%9E%E6%80%A7)，如：`class` 、`frameborder` 等。", "name": "Adding HTML attributes", "name_cn": "添加 HTML 属性", "path": "/products/earring-ab001", "raw_sline": "{{#for media in product.media}}\n    {{#switch media.media_type}}\n        {{#case \"external_video\" /}}\n            {{#if media.host == \"youtube\"}}\n                {{#external_video_tag media | external_video_url(autoplay=true, loop=false) class=\"video-media-youtube\" frameborder=\"0\" /}}\n            {{/if}}\n    {{/switch}}\n{{/for}}", "source_object": "product", "syntax": "{{#external_video_tag media | external_video_url() attribute=any /}}"}], "hashs": [], "link": "https://developer.shopline.com/docs/sline/tag/external-video-tag", "name": "external_video_tag", "summary": "Generates a player for the specified external video in an `<iframe>` tag.", "summary_cn": "生成指定外部视频的播放器 `<iframe>` 标签。", "syntax": "{{#external_video_tag videoUrl attribute=any /}}"}, {"arguments": [{"description": "The image CDN link returned by [image_url filter](/docs/sline/filter/image-url)", "description_cn": "通过 [image_url filter](/docs/sline/filter/image-url) 返回的图片 CDN 链接", "name": "imageUrl", "types": ["string"]}], "deprecated": false, "description_cn": "", "examples": [{"description": "When setting the tag name to `source`, the `src` and `alt` attributes will have no effect.", "description_cn": "当设置标签名为 `source` 时，`src` 和 `alt` 属性会失效。", "name": "Custom tag name", "name_cn": "自定义标签名", "path": "/products/earring-ab001", "raw_sline": "{{#image_tag product | image_url() tagName=\"source\" /}}", "source_object": "product", "syntax": "{{#image_tag variable | image_url() tagName=string /}}"}, {"name": "Custom image width", "name_cn": "自定义图片宽度", "path": "/products/earring-ab001", "raw_sline": "{{#image_tag product | image_url() width=100 /}}", "source_object": "product", "syntax": "{{#image_tag variable | image_url() width=number /}}"}, {"name": "Custom image height", "name_cn": "自定义图片高度", "path": "/products/earring-ab001", "raw_sline": "{{#image_tag product | image_url() height=100 /}}", "source_object": "product", "syntax": "{{#image_tag variable | image_url() height=number /}}"}, {"name": "Customizing the sizes attribute", "name_cn": "自定义 sizes 属性", "path": "/products/earring-ab001", "raw_sline": "{{#image_tag product | image_url() sizes=\"20px\" /}}", "source_object": "product", "syntax": "{{#image_tag variable | image_url() sizes=string /}}"}, {"description": "The `widths` configuration will override the `srcset` attribute generated by the system by default. Its configuration specification must be a string of numbers separated by commas. For example: \"10,20,30\".", "description_cn": "`widths` 配置会覆盖系统默认生成的 `srcset` 属性。其配置规范需为以英文逗号分隔的数字字符串。如：\"10,20,30\" 。", "name": "Custom widths property", "name_cn": "自定义 widths 属性", "path": "/products/earring-ab001", "raw_sline": "{{#image_tag product | image_url() widths=\"10,20,30\" /}}", "source_object": "product", "syntax": "{{#image_tag variable | image_url() widths=string /}}"}, {"name": "Image preloading", "name_cn": "图片预加载", "path": "/products/earring-ab001", "raw_sline": "{{#image_tag product | image_url() preload=true /}}", "source_object": "product", "syntax": "{{#image_tag variable | image_url() preload=boolean /}}"}], "hashs": [{"description": "The generated HTML tag name, default value is `img`.", "description_cn": "生成的 HTML 标签名，默认值为 `img` 。", "name": "tagName", "types": ["string"]}, {"description": "The width attribute of the HTML tag.", "description_cn": "HTML 标签的宽度属性。", "name": "width", "types": ["number"]}, {"description": "The height attribute of the HTML tag.", "description_cn": "HTML 标签的高度属性。", "name": "height", "types": ["number"]}, {"description": "Use the [sizes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#sizes) attribute to specify the size.", "description_cn": "用 [sizes](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/img#sizes) 属性指定尺寸。", "name": "sizes", "types": ["string"]}, {"description": "Set a `srcset` attribute breakpoint with a specified `widths` breakpoint, otherwise it will be automatically generated.", "description_cn": "用指定 `widths` 断点，设置 `srcset` 属性断点，否则程序自动生成。", "name": "widths", "types": ["string"]}, {"description": "Whether to preload images. When `preload` is set to true, the browser will load these resources in advance.", "description_cn": "是否预加载图片。当 `preload` 设置为 true 时，浏览器会提前加载这些资源。", "name": "preload", "types": ["boolean"]}], "link": "https://developer.shopline.com/docs/sline/tag/image-tag", "name": "image_tag", "summary": "Generates an `<img>` tag for the specified [image_url](/docs/sline/filter/image-url).", "summary_cn": "生成指定 [image_url](/docs/sline/filter/image-url) 的 `<img>` 标签。", "syntax": "{{#image_tag variable | image_url() /}}"}, {"arguments": [{"description": "Supported name types", "description_cn": "支持的名称类型", "name": "name", "types": ["string"]}], "deprecated": false, "description": "The following are supported name types:\n- image\n- product-1\n- product-2\n- product-3\n- product-4\n- product-5\n- product-6\n- lifestyle-1\n- lifestyle-2\n- collection-1\n- collection-2\n- collection-3\n- collection-4\n- collection-5\n- collection-6\n- hero-apparel-1\n- hero-apparel-2\n- hero-apparel-3\n- blog-apparel-1\n- blog-apparel-2\n- blog-apparel-3\n- detailed-apparel-1", "description_cn": "以下是支持的名称类型：\n- image\n- product-1\n- product-2\n- product-3\n- product-4\n- product-5\n- product-6\n- lifestyle-1\n- lifestyle-2\n- collection-1\n- collection-2\n- collection-3\n- collection-4\n- collection-5\n- collection-6\n- hero-apparel-1\n- hero-apparel-2\n- hero-apparel-3\n- blog-apparel-1\n- blog-apparel-2\n- blog-apparel-3\n- detailed-apparel-1", "examples": [{"description": "Generates an `svg` tag of type `product-1`.", "description_cn": "生成 `product-1` 类型的 `svg` 标签。", "name": "Basic example", "name_cn": "基础示例", "path": "/", "raw_sline": "{{#placeholder_svg \"product-1\" /}}", "source_object": "", "syntax": ""}, {"description": "Customize the `class` attribute.", "description_cn": "自定义 `class` 属性。", "name": "Adding the class attribute", "name_cn": "添加 class 属性", "path": "/", "raw_sline": "{{#placeholder_svg \"product-1\" class=\"custom-class\" /}}", "source_object": ""}], "hashs": [{"description": "Custom class", "description_cn": "自定义类", "name": "class", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/placeholder-svg", "name": "placeholder_svg", "summary": "Generates a `<svg>` tag of the specified name type.", "summary_cn": "生成指定名称类型的 `<svg>` 标签。", "syntax": "{{#placeholder_svg name /}}"}, {"arguments": [{"description": "media object", "description_cn": "媒体对象", "name": "media", "types": ["object"]}], "deprecated": false, "description": "Currently only `.mp4` video format is supported.", "description_cn": "目前仅支持 `.mp4` 格式的视频。", "examples": [{"name": "Basic example", "name_cn": "基础示例", "path": "/products/earring-ab001", "raw_sline": "{{#for media in product.media}}\n    {{#switch media.media_type}}\n        {{#case \"video\" /}}\n            {{#video_tag media /}}\n    {{/switch}}\n{{/for}}", "source_object": "product"}, {"description": "The example only sets the width of the cover image, and the program will automatically match the optimal height of the image.", "description_cn": "示例仅设置了封面图宽度，程序会自动匹配图片最佳高度。", "name": "Set cover image width", "name_cn": "设置封面图宽度", "path": "/products/earring-ab001", "raw_sline": "{{#for media in product.media}}\n    {{#switch media.media_type}}\n        {{#case \"video\" /}}\n            {{#video_tag media image_size=\"100x\" /}}\n    {{/switch}}\n{{/for}}", "source_object": "product", "syntax": ""}, {"description": "You can add standard HTML video attributes to the [video](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/video#%E5%B1%9E%E6%80%A7) tag, such as autoplay , loop , etc.", "description_cn": "你可以为 `<video>` 标签添加符合规范标准的 [HTML video 属性](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/video#%E5%B1%9E%E6%80%A7)，如：`autoplay` 、`loop` 等。", "name": "Adding HTML attributes", "name_cn": "添加 HTML 属性", "path": "/products/earring-ab001", "raw_sline": "{{#for media in product.media}}\n    {{#switch media.media_type}}\n        {{#case \"video\" /}}\n            {{#video_tag media autoplay=true loop=true /}}\n    {{/switch}}\n{{/for}}", "source_object": "product"}], "hashs": [{"description": "Specify the width and height of the video cover image. The format is `{width}x{height}`, you can set only the width or the height.", "description_cn": "指定视频封面图宽高。格式为 `{width}x{height}` ，可仅设置宽或者高。", "name": "image_size", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/video-tag", "name": "video_tag", "summary": "Generates a player for the specified video `<video>` tag.", "summary_cn": "生成指定视频的播放器 `<video>` 标签。", "syntax": "{{#video_tag media /}}"}, {"deprecated": false, "description": "This `form` tag is used for two types of form submissions:\n\n- Country selector\n- Language selector", "description_cn": "此 `form` 标签适用于以下两种类型的表单提交：\n\n- 国家/地区选择器\n- 语言选择器", "examples": [{"description": "The form submission field is `country_code`, and its value is the corresponding country code, which can be obtained through the iso_code field in `localization.available_countries` object.", "description_cn": "表单提交字段为 `country_code` ，值为对应国家码，可通过 `localization.available_countries` object 里的 iso_code 字段获取。", "name": "Country Selector", "name_cn": "国家/地区选择器", "path": "/products/earring-ab001", "raw_sline": "{{#localization_form enctype=\"multipart/form-data\" accept-charset=\"UTF-8\"}}\n  <select name=\"country_code\" onchange=\"this.form.submit();\">\n    {{#for country in localization.available_countries}}\n      <option value=\"{{country.iso_code}}\" {{#if localization.country.iso_code == country.iso_code}}selected{{/if}}>\n        {{country.name}}（{{country.currency.iso_code}}\n        {{country.currency.symbol}}）\n      </option>\n    {{/for}}\n  </select>\n{{/localization_form}}", "source_object": "localization"}, {"description": "The form submission field is `locale_code`, and its value is the corresponding language encoding, which can be obtained through the iso_code field in `localization.available_languages` object .", "description_cn": "表单提交字段为 `locale_code` ，值为对应语言编码，可通过 `localization.available_languages` object 里的 iso_code 字段获取。", "name": "Language selector", "name_cn": "语言选择器", "path": "/products/earring-ab001", "raw_sline": "{{#localization_form enctype=\"multipart/form-data\" accept-charset=\"UTF-8\"}}\n  <select name=\"locale_code\" onchange=\"this.form.submit();\">\n    {{#for language in localization.available_languages}}\n      <option value=\"{{language.iso_code}}\" {{#if localization.language.endonym_name == language.endonym_name}}selected{{/if}}>\n        {{language.endonym_name}}\n      </option>\n    {{/for}}\n  </select>\n{{/localization_form}}", "source_object": "localization"}, {"description": "You can add standard HTML form attributes to the [form tag](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/form#%E5%B1%9E%E6%80%A7), such as accept , accept-charset , etc.", "description_cn": "你可以为 `<form>` 标签添加符合规范标准的 [HTML form 属性](https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/form#%E5%B1%9E%E6%80%A7)，如：`accept` 、`accept-charset` 等。", "name": "Adding HTML attributes", "name_cn": "添加 HTML 属性", "path": "/products/earring-ab001", "raw_sline": "{{#localization_form id=\"localization-form\" enctype=\"multipart/form-data\" accept-charset=\"UTF-8\"}}\ncustom code\n{{/localization_form}}", "source_object": "", "syntax": ""}], "hashs": [{"description": "The redirect URL after the form is submitted. The default is the current page.", "description_cn": "表单提交后的重定向 URL ，默认为当前页面。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/localization-form", "name": "localization_form", "summary": "Generates a submission `<form>` form for switching countries and languages.", "summary_cn": "生成用于国家/地区、语言切换的提交 `<form>` 表单。", "syntax": "{{#localization_form /}} custom code {{/localization_form}}"}, {"deprecated": false, "description": "### Form input\n\ncontact[email] email Required\n\nEmail address.\n\nattribute[key] any Optional\n\nCustom attributes, such as name, phone number, birthday.", "description_cn": "### 表单输入\n\ncontact[email] email 必填\n\n邮箱。\n\nattribute[key] any 选填\n\n自定义属性，例如：name、phone、birthday。\n", "examples": [{"description": "The following example is used to generate a form containing user name, email address, and phone number information.", "description_cn": "以下示例用于生成包含用户名、邮箱、电话信息的表单。", "name": "Basic example", "name_cn": "基础示例", "path": "/products/earring-ab001", "raw_sline": "{{#contact_form id=\"ContactForm\"}}\n  {{#if form.posted_successfully}}\n    <div>Submit Successfully</div>\n  {{/if}}\n\n  {{#if form.errors.messages}}\n    <div>{{form.errors.messages}}</div>\n  {{/if}}\n\n  <div class=\"fields\">\n    <label class=\"field\">\n      <input type=\"text\" name=\"contact[name]\" title=\"Name\" placeholder=\"Please enter your name\">\n    </label>\n\n    <label class=\"field\">\n      <input type=\"email\" name=\"contact[email]\" title=\"Email\" placeholder=\"Please enter your email address\" required>\n    </label>\n\n    <label class=\"field\">\n      <input type=\"tel\" name=\"contact[phone]\" title=\"Phone\" pattern=\"[0-9\\-]*\" placeholder=\"Please enter your phone number\">\n    </label>\n\n    <div>\n      <input type=\"submit\" value=\"Submit\">\n    </div>\n  </div>\n{{/contact_form}}", "source_object": ""}, {"description": "In order to achieve multi-language email content, the `form` submission of the contact form needs to be used in conjunction with a script.\n\n```js\ndocument.querySelector('[type=\"submit\"]').addEventListener('click', (e) => {\n    const form = document.querySelector('#ContactForm')\n    const inputs = form.querySelectorAll('input')\n    const translateInput = form.querySelector('input[name=_translate]')\n    const translate = {}\n\n    for (let i = 0; i < inputs.length; i++) {\n      const input = inputs[i]\n      const title = input.getAttribute('title')\n      const name = input.getAttribute('name')\n      if (/contact|attribute\\[[\\w]+\\]/.test(name)) {\n        translate[name] = title\n      }\n    }\n\n    translateInput.setAttribute('value', JSON.stringify(translate))\n})\n```", "description_cn": "为了实现邮件内容的多语言，联系表单的 `form` 提交，需要结合脚本去使用。\n\n```js\ndocument.querySelector('[type=\"submit\"]').addEventListener('click', (e) => {\n    const form = document.querySelector('#ContactForm')\n    const inputs = form.querySelectorAll('input')\n    const translateInput = form.querySelector('input[name=_translate]')\n    const translate = {}\n\n    for (let i = 0; i < inputs.length; i++) {\n      const input = inputs[i]\n      const title = input.getAttribute('title')\n      const name = input.getAttribute('name')\n      if (/contact|attribute\\[[\\w]+\\]/.test(name)) {\n        translate[name] = title\n      }\n    }\n\n    translateInput.setAttribute('value', JSON.stringify(translate))\n})\n```", "name": "Multi-language support", "name_cn": "多语言支持", "path": "/products/earring-ab001", "raw_sline": ""}], "hashs": [{"description": "The redirect URL after the form is submitted. The default is the current page.", "description_cn": "表单提交后的重定向 URL ，默认为当前页面。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/contact-form", "name": "contact_form", "summary": "Generates a `<form>` form for submitting an email to the merchant.", "summary_cn": "生成用于向商家提交电子邮件的 `<form>` 表单。", "syntax": "{{#contact_form /}} custom code {{/contact_form}}"}, {"arguments": [{"description": "Article object", "description_cn": "文章对象", "name": "article", "types": ["object"]}], "deprecated": false, "description": "### Form input\n\ncomment[author] text Required\n\nCommenter name.\n\ncomment[email] email Required\n\nCommenter email address.\n\ncomment[body] body Required\n\nComment content.", "description_cn": "### 表单输入\n\ncomment[author] text 必填\n\n评论者名称。\n\ncomment[email] email 必填\n\n评论者邮箱地址。\n\ncomment[body] body 必填\n\n评论内容。\n", "examples": [{"description_cn": "", "name": "Basic example", "name_cn": "基础示例", "path": "/blogs/news/blog-2", "raw_sline": "{{#new_comment_form article}}\n  {{#if form.posted_successfully}}\n    <div>Submit Successfully</div>\n  {{/if}}\n\n  {{#if form.errors.messages}}\n    <div>{{form.errors.messages}}</div>\n  {{/if}}\n\n  <div class=\"fields\">\n    <label class=\"field\">\n      <input type=\"text\" name=\"comment[author]\" placeholder=\"Please enter your name\" required>\n    </label>\n\n    <label class=\"field\">\n      <input type=\"email\" name=\"comment[email]\" placeholder=\"Please enter your email address\" required>\n    </label>\n\n    <label class=\"field\">\n      <textarea name=\"comment[body]\" placeholder=\"Please enter your comment\" required></textarea>\n    </label>\n\n    <div>\n      <input type=\"submit\" value=\"Submit\">\n    </div>\n  </div>\n{{/new_comment_form}}", "source_object": "article"}], "link": "https://developer.shopline.com/docs/sline/tag/new-comment-form", "name": "new_comment_form", "summary": "Generates a `<form>` for creating new comments on a blog post.", "summary_cn": "生成用于博客文章新建评论的 `<form>` 表单。", "syntax": "{{#new_comment_form /}} custom code {{/new_comment_form}}"}, {"deprecated": false, "description": "### Form input\n\npassword password Required\n\nStore password.", "description_cn": "### 表单输入\n\npassword password 必填\n\n店铺密码。", "examples": [{"name": "Basic example", "name_cn": "基础示例", "path": "/products/earring-ab001", "raw_sline": "{{#storefront_password_form}}\n  {{#if form.posted_successfully}}\n    <div>Submit Successfully</div>\n  {{/if}}\n\n  {{#if form.errors.messages}}\n    <div>{{form.errors.messages}}</div>\n  {{/if}}\n\n  <div class=\"fields\">\n    <label class=\"field\">\n      <input type=\"password\" name=\"password\" placeholder=\"Please enter password\" required>\n    </label>\n    <div>\n      <input type=\"submit\" value=\"Submit\">\n    </div>\n  </div>\n{{/storefront_password_form}}"}], "hashs": [{"description": "The redirect URL after the form is submitted. The default is the current page.", "description_cn": "表单提交后的重定向 URL ，默认为当前页面。", "name": "return_to", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/storefront-password-form", "name": "storefront_password_form", "summary": "Generate store password `<form>` form.", "summary_cn": "生成店铺密码 `<form>` 表单。", "syntax": "{{#storefront_password_form /}} custom code {{/storefront_password_form}}"}, {"deprecated": false, "description": "### Form input\n\ntext order_tracking[order_number] Required\n\nOrder number.\n\nemail order_tracking[email] Optional\n\nOrder email address.\n\ntext order_tracking[phone] Optional\n\nOrder phone number.\n\ntext order_tracking[phone_area_code] Optional\n\nOrder phone area code.", "description_cn": "### 表单输入\n\ntext order_tracking[order_number] 必填\n\n订单编号。\n\nemail order_tracking[email] 选填\n\n订单邮箱。\n\ntext order_tracking[phone] 选填\n\n订单手机号。\n\ntext order_tracking[phone_area_code] 选填\n\n订单手机区号。", "examples": [{"description_cn": "", "name": "Use email to check orders", "name_cn": "使用邮箱查询订单", "path": "/products/earring-ab001", "raw_sline": "{{#order_tracking_form id=\"OrderTrackingForm\"}}\n  <div class=\"fields\">\n    <label class=\"field\">\n      <input type=\"email\" name=\"order_tracking[email]\" placeholder=\"Please enter email\" required>\n    </label>\n    <label class=\"field\">\n      <input type=\"text\" name=\"order_tracking[order_number]\" placeholder=\"Please enter order number\" required>\n    </label>\n    <div>\n      <input type=\"submit\" value=\"Submit\">\n    </div>\n  </div>\n{{/order_tracking_form}}", "syntax": ""}, {"name": "Use mobile phone number to check order", "name_cn": "使用手机号查询订单", "path": "/products/earring-ab001", "raw_sline": "{{#order_tracking_form id=\"OrderTrackingForm\"}}\n  <div class=\"fields\">\n    <label class=\"field\">\n      <input type=\"text\" name=\"order_tracking[phone_area_code]\" placeholder=\"Please enter order phone area code\" required>\n    </label>\n    <label class=\"field\">\n      <input type=\"text\" name=\"order_tracking[phone]\" placeholder=\"Please enter phone\" required>\n    </label>\n    <label class=\"field\">\n      <input type=\"text\" name=\"order_tracking[order_number]\" placeholder=\"Please enter order number\" required>\n    </label>\n    <div>\n      <input type=\"submit\" value=\"Submit\">\n    </div>\n  </div>\n{{/order_tracking_form}}"}, {"description": "To query the `form` submitted for an order, you need to use the `order-tracking-api` SDK instance to call it.\n\n```js\nclass OrderTracking {\n  constructor() {\n    this.form = document.querySelector('#OrderTrackingForm');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n    this.orderTracking = new window.Shopline.OrderTracking(this.form);\n  }\n  // form submit\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.orderTracking\n      .submit()\n      .then(response => {\n        if (response.data?.orderUrl) {\n          window.location.href = response.data.orderUrl;\n        } else {\n            // handle other situations without response.data.orderUrl\n        }\n      })\n      .catch(response => {\n        // handle other situations with response.error_fields\n      })\n  }\n}\n\n// init JS SDK\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'order-tracking-api',\n      version: '0.1'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n    new OrderTracking();\n  }\n);\n```", "description_cn": "查询订单的 `form` 提交，需要使用 `order-tracking-api` SDK 实例去调用。\n\n```js\nclass OrderTracking {\n  constructor() {\n    this.form = document.querySelector('#OrderTrackingForm');\n    this.form.addEventListener('submit', this.onSubmitHandler.bind(this));\n    this.orderTracking = new window.Shopline.OrderTracking(this.form);\n  }\n  // 表单提交\n  onSubmitHandler(e) {\n    e.preventDefault();\n\n    this.orderTracking\n      .submit()\n      .then(response => {\n        if (response.data?.orderUrl) {\n          window.location.href = response.data.orderUrl;\n        } else {\n            // handle other situations without response.data.orderUrl\n        }\n      })\n      .catch(response => {\n        // handle other situations with response.error_fields\n      })\n  }\n}\n\n// 初始化 JS SDK\nwindow.Shopline.loadFeatures(\n  [\n    {\n      name: 'order-tracking-api',\n      version: '0.1'\n    }\n  ],\n  function(error) {\n    if (error) {\n      throw error;\n    }\n    new OrderTracking();\n  }\n);\n```", "name": "Order Tracking API", "name_cn": "Order Tracking API", "path": "/products/earring-ab001", "raw_sline": ""}], "link": "https://developer.shopline.com/docs/sline/tag/order-tracking-form", "name": "order_tracking_form", "summary": "Generate an order query form, allowing customers to query order information by account number and order number without logging in.", "summary_cn": "生成一个查询订单 `form` 表单，允许客户在不登录的情况下通过账号和订单编号查询订单信息。", "syntax": "{{#order_tracking_form /}} custom code {{/order_tracking_form}}"}, {"arguments": [{"description": "Product object.", "description_cn": "商品对象。", "name": "product", "types": ["product"]}], "deprecated": false, "examples": [{"name_cn": "例子", "path": "/products/product-example", "raw_sline": "```\n{{#product_form product id=\"productFrom\" class=\"productFrom\"}}\n        <div style=\"margin-bottom:10px;\">\n            <input type=\"number\" required step=\"1\" form=\"product-form-{{section.id}}\" name=\"quantity\" min='1' max='999' value='1'/>\n        </div>\n        {{#var firstProduct = product.variants | first() /}}\n        <div style=\"margin-bottom:10px;\">\n            <input type=\"hidden\" name=\"id\" value=\"{{firstProduct | default(product.selected_or_first_available_variant.id)}}\" />\n        </div>\n        <button style=\"margin-bottom:10px;\" class='btn btn-primary' type='submit'>{{ \"products.product_list.add_to_cart\" | t() }}</button>\n        {{#payment_button /}}\n    {{/product_form}}\n```", "source_object": "product", "syntax": "```\n{{#product_form product id=\"xxxxx\"}}\n    expression\n{{/product_form}}\n```"}], "hashs": [{"description": "You can pass in the attribute name and attribute value parameters to specify [HTML attributes](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes).", "description_cn": "可以传入属性名称和属性值参数来指定 [HTML属性](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes)。", "name": "HTML attributes", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/product-form", "name": "product_form", "summary": "Generate a form for adding a product variant to the  cart. This product form requires a product object to be passed in as a parameter.", "summary_cn": "生成一个用于将产品款式添加到购物车的表单。该产品表单需要传入一个产品对象作为参数。", "syntax": "```\n{{#product_form product id=\"productFrom\" class=\"productFrom\"}}\n    content\n{{/product_form}}\n```", "syntax_keywords": [{"description": "Elements displayed within the `<form>` tag on the page.", "description_cn": "显示在页面的 `<form>` 标签内的元素。", "keyword": "content"}]}, {"arguments": [{"description": "Tag.", "description_cn": "产品标签。", "name": "tag", "types": ["string"]}], "deprecated": false, "description": "", "description_cn": "", "examples": [{"name_cn": "例子", "path": "/collections/${handle}", "raw_sline": "  {{#for tag in collection.all_tags}}\n        {{#link_to_tag tag class=\"link-class\" my_attr=\"abc\" }}\n            {{tag}}\n        {{/link_to_tag}}\n    {{/for}}", "source_object": "collection", "syntax": "{{#link_to_tag tag [attribute...]}}content{{/link_to_tag}}"}], "hashs": [{"description": "You can pass in the attribute name and attribute value parameters to specify [HTML attributes](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes).", "description_cn": "可以传入属性名称和属性值参数来指定 [HTML属性](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes)。", "name": "HTML attributes", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-tag", "name": "link_to_tag", "summary": "Generate an HTML `<a>` tag with an `href` attribute. The link address will include the specified tag passed in. Clicking on this tag allows you to navigate to the current product collection listing page. After the navigation, the page will filter and display only the products corresponding to the specified tag.\n\nCurrently, it is only supported on the product collection listing page, and is not available on other pages for now.", "summary_cn": "生成一个带有 `href` 属性的 HTML `<a>` 标签，其链接地址会包含传入的指定标签。点击该标签，即可跳转至当前所在的商品分类列表页。跳转后，页面将过滤并仅展示对应指定标签的商品。\n\n目前，仅支持在商品分类列表页使用，其他页面暂不支持。 \n", "syntax": "```\n{{#link_to_tag \"my_tag\" class=\"link-class\" my_attr=\"abc\" }}\n  content\n{{/link_to_tag}}\n```", "syntax_keywords": [{"description": "The text content displayed within the `<a>` tag on the page. ", "description_cn": "显示在页面的 `<a>` 标签内的文案。", "keyword": "content"}]}, {"arguments": [{"description": "Type.", "description_cn": "商品类型。", "name": "tag", "types": ["string"]}], "deprecated": false, "examples": [{"name_cn": "例子", "path": "/collections", "raw_sline": "{{#for type in collection.all_types}}\n    {{#link_to_type type class=\"link-class\" my_attr=\"abc\" /}}\n{{/for}}", "source_object": "collection", "syntax": "{{#link_to_type type [attribute...] /}}"}], "hashs": [{"description": "You can pass in the attribute name and attribute value parameters to specify [HTML attributes](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes).", "description_cn": "可以传入属性名称和属性值参数来指定 [HTML属性](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes)。", "name": "HTML attributes", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-type", "name": "link_to_type", "summary": "Generate an HTML `<a>` tag with an `href` attribute. Clicking on this tag enables navigation to the current collection listing page. The link of this tag will include the specified type passed in. After the navigation, the new page will filter and display products corresponding to the specified type. ", "summary_cn": "生成一个带有 `href` 属性的 HTML `<a>` 标签，点击该标签可跳转至当前所在的分类列表页。该标签的链接会包含传入的指定商品类型，跳转后，新页面将过滤展示对应指定商品类型的商品。", "syntax": "```\n{{#link_to_type \"content\" class=\"link-class\" my_attr=\"abc\"/}}\n```", "syntax_keywords": [{"description": "The text content displayed within the `<a>` tag on the page. ", "description_cn": "显示在页面的 `<a>` 标签内的文案。", "keyword": "content"}]}, {"arguments": [{"description": "Vender.", "description_cn": "商品厂商。", "name": "tag", "types": ["string"]}], "deprecated": false, "examples": [{"name_cn": "例子", "path": "/collections", "raw_sline": "```\n{{#for vendor in collection.all_vendors}}\n        {{#link_to_vendor vendor class=\"link-class\" my_attr=\"abc\"/}}\n    {{/for}}\n```", "source_object": "collection", "syntax": "```\n{{#link_to_vendor vendor [attribute...] /}}\n```"}], "hashs": [{"description": "You can pass in the attribute name and attribute value parameters to specify [HTML attributes](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes).", "description_cn": "可以传入属性名称和属性值参数来指定 [HTML属性](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes)。", "name": "HTML attributes", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-vendor", "name": "link_to_vendor", "summary": "Generate an HTML `<a>` tag with an `href` attribute. Clicking on this tag enables navigation to the current collection listing page. The link of this tag will include the specified vendor passed in. After the navigation, the new page will filter and display products corresponding to the specified vendor. ", "summary_cn": "生成一个带有 `href` 属性的 HTML `<a>` 标签，点击该标签可跳转至当前所在的分类列表页。该标签的链接会包含传入的指定商品厂商信息，跳转后，新页面将过滤展示对应指定商品厂商的商品。", "syntax": "```\n{{#link_to_vendor \"content\" class=\"link-class\" my_attr=\"abc\"/}}\n```", "syntax_keywords": [{"description": "The text content displayed within the `<a>` tag on the page. ", "description_cn": "显示在页面的 `<a>` 标签内的文案。", "keyword": "content"}]}, {"arguments": [{"description": "Type, required.", "description_cn": "产品类型, 必填。", "name": "tag", "types": ["string"]}], "deprecated": false, "examples": [{"name_cn": "例子", "path": "/collections/${handle}", "raw_sline": "```\n{{#for tag in collection.all_tags}} \n    {{#link_to_add_tag tag class=\"link-class\" my_attr=\"abc\" }}\n        {{tag}} \n    {{/link_to_add_tag}}\n{{/for}}\n```", "source_object": "collection", "syntax": "{{#link_to_remove_tag tag [attribute...] /}}"}], "hashs": [{"description": "You can pass in the attribute name and attribute value parameters to specify [HTML attributes](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes).", "description_cn": "可以传入属性名称和属性值参数来指定 [HTML属性](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes)。", "name": "HTML attributes", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-remove-tag", "name": "link_to_remove_tag", "summary": "Generate an HTML `<a>` tag with an `href` attribute. Clicking on this tag allows you to navigate to the current collection listing page. After the navigation, the page will filter and display products corresponding to the tags carried in the `url`, while excluding products corresponding to the passed-in tags.\n\nCurrently, it is only supported on the category listing page and is not available on other pages for now.", "summary_cn": "生成一个带 `href` 属性的 HTML `<a>` 标签，点击该标签可跳转至当前所在的分类列表页。跳转后，页面将过滤展示 `url` 中携带标签对应的商品，同时排除传入标签对应的商品。\n\n目前，仅支持在分类列表页使用，其他页面暂不支持。", "syntax": "```\n{{#link_to_remove_tag \"my_tag\" class=\"link-class\" my_attr=\"abc\" }}\n    content\n{{/link_to_remove_tag}} \n```", "syntax_keywords": [{"description": "The text content displayed within the `<a>` tag on the page. ", "description_cn": "显示在页面的 `<a>` 标签内的文案。", "keyword": "content"}]}, {"arguments": [{"description": "Type, required.", "description_cn": "产品类型, 必填。", "name": "tag", "types": ["string"]}], "deprecated": false, "examples": [{"description_cn": "", "name_cn": "例子", "path": "/collections", "raw_sline": "{{#for tag in collection.all_tags}}\n    {{#link_to_add_tag tag class=\"link-class\" my_attr=\"abc\" }}\n        {{tag}}\n    {{/link_to_add_tag}} \n{{/for}}", "source_object": "collection", "syntax": "{{#link_to_add_tag tag [attribute...] /}}"}], "hashs": [{"description": "You can pass in the attribute name and attribute value parameters to specify [HTML attributes](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes).", "description_cn": "可以传入属性名称和属性值参数来指定 [HTML属性](https://developer.mozilla.org/docs/Web/HTML/Reference/Elements/a#attributes)。", "name": "HTML attributes", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/link-to-add-tag", "name": "link_to_add_tag", "summary": "Generate an HTML `<a>` tag with an `href` attribute. The link address will include the target tag passed in. Clicking on this tag allows you to navigate to the current product collection listing page. After the navigation, the page will filter and display products corresponding to the target tag. If the page link already carries other tags, the target tag will be added to the existing tags for additional filtering and display.\n\nCurrently, it is only supported on the product collection listing page and is not available on other pages for now.", "summary_cn": "生成一个带有 `href` 属性的 HTML `<a>` 标签，其链接地址会包含传入的指定标签。点击该标签，即可跳转至当前所在的商品分类列表页。跳转后，页面将过滤并展示对应指定标签的商品。若页面链接本身已携带其他标签，指定标签会在原有标签基础上追加筛选展示。\n\n目前，仅支持在商品分类列表页使用，其他页面暂不支持。 ", "syntax": "```\n{{#link_to_add_tag \"my_tag\" class=\"link-class\" my_attr=\"abc\" }}\n    content\n{{/link_to_add_tag}} \n```", "syntax_keywords": [{"description": "The text content displayed within the `<a>` tag on the page. ", "description_cn": "显示在页面的 `<a>` 标签内的文案。", "keyword": "content"}]}, {"deprecated": false, "examples": [{"name": "payment_button", "name_cn": "payment_button", "raw_sline": "{{#product_form product}}\n    your code\n    <div class=\"form__buttons\">\n        {{#payment_button/}}\n    </div>\n{{/product_form}}", "source_object": "product", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/tag/payment-button", "name": "payment_button", "summary": "Generates an HTML container to host Dynamic checkout buttons for a product. The payment_button filter must be used on the form object within a product form.\n\n", "summary_cn": "生成一个承载网站快速结账按钮的 HTML 容器。该 filter 必须用于网站的 product form 里。\n\n", "syntax": "{{#product_form product}}\n    your code\n    <div class=\"form__buttons\">\n        {{#payment_button/}}\n    </div>\n{{/product_form}}"}, {"deprecated": false, "description": "Variables cannot be defined repeatedly. If they are defined repeatedly, the current template will not have any output.\n\nTo update the value, use [set tag](/docs/sline/tag/set).", "description_cn": "变量不可以重复定义，重复定义，当前模板不会有任何输出。\n\n需要更新值需要使用[set tag](/docs/sline/tag/set)。\n", "examples": [{"name": "Declare and define variables", "name_cn": "声明且定义变量", "path": "/products/product-009-92883-19", "raw_sline": "{{# var brand = product.brand | upcase() /}}\n{{ brand }}", "source_object": "product"}, {"name": "Declare only variables", "name_cn": "只声明变量", "path": "/products/product-009-92883-19", "raw_sline": "{{# var brand /}}\n{{#set brand = product.brand /}}\n{{ brand }}"}], "link": "https://developer.shopline.com/docs/sline/tag/var", "name": "var", "summary": "Define a variable.", "summary_cn": "定义一个变量。", "syntax": "{{#var variable = value /}}", "syntax_keywords": [{"description": "The name of the variable being defined.", "description_cn": "定义的变量名。", "keyword": "variable"}, {"description": "Defined variable value.", "description_cn": "定义的变量值。", "keyword": "value"}]}, {"deprecated": false, "description": "Variables must be declared in advance using `var` or `capture`.\n\nThe variable to be set must be consistent with the data type of the defined variable.", "description_cn": "变量必须要提前使用`var`或者`capture`声明。\n\n设置的变量必须要和定义的变量数据类型一致。", "examples": [{"description": "Only values of the same type are allowed to be modified.", "description_cn": "只允许修改同一类型的值。", "name": "Updating values of the same type", "name_cn": "更新同一类型的值", "path": "/products/product-009-92883-19", "raw_sline": "{{# var brand = product.brand | upcase() /}}\n{{ brand }}\n\n{{# set brand = brand | append(\" -1\") /}}\n{{ brand }}\n", "source_object": "product"}, {"description": "When the modified value does not match the initial value, set does not take effect.", "description_cn": "当修改的值与初始值不一致时，set 不生效。", "name": "Update value type is inconsistent", "name_cn": "更新值类型不一致", "path": "/products/product-009-92883-19", "raw_sline": "{{# var brand = product.brand | upcase() /}}\n{{ brand }}\n\n{{# set brand = 1 /}}\n{{ brand }}\n", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/tag/set", "name": "set", "summary": "Modify the value of an existing variable.", "summary_cn": "修改已有变量的值。", "syntax": "{{#set variable = value /}}", "syntax_keywords": [{"description": "The name of the variable to be modified.", "description_cn": "需要修改的变量名。", "keyword": "variable"}, {"description": "Updated variable value.", "description_cn": "更新的变量值。", "keyword": "value"}]}, {"arguments": [{"description": "The condition to evaluate.", "description_cn": "要判断的条件。", "name": "condition", "types": ["expression"]}], "deprecated": false, "examples": [{"description": "Compares two values, provided that the two values ​​are of the same type.", "description_cn": "对比两个值，前提是两个值类型是一致的。", "path": "/products/product-009-92883-19", "raw_sline": "{{#if product.price > 30 }}\n    product price more than 30\n{{/if}}", "source_object": "product"}, {"description": "", "name": "elseif", "name_cn": "elseif", "path": "/products/product-009-92883-19", "raw_sline": "{{#if product.price < 30 }}\n    product price less than 30\n{{#else if product.price > 30 /}}\n    product price more than 30\n{{/if}}", "source_object": "product", "syntax": ""}, {"name": "else", "path": "/products/product-009-92883-19", "raw_sline": "{{#if product.price < 30 }}\n    product price less than 30\n{{#else/}}\n    product price more than 30\n{{/if}}", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/tag/if", "name": "if", "summary": "Renders an expression if a specific condition is true.\n\n", "summary_cn": "如果特定条件为true则呈现表达式。", "syntax": "{{#if condition }}\n  expression\n{{/if}}", "syntax_keywords": []}, {"arguments": [{"description": "The variable to compare.", "description_cn": "要进行比较的变量。", "name": "variable", "types": ["string", "boolean", "number"]}], "deprecated": false, "examples": [{"raw_sline": "{{#var val = \"apple\" /}}\n\n{{#switch val}}\n{{#case \"cat\" /}}\n  animal\n{{#case \"apple\" \"banana\" /}}\n  fruit\n{{#else/}}\n  unknown\n{{/switch}}", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/tag/switch", "name": "switch", "summary": "Renders a specific expression based on the value of a specific variable.", "summary_cn": "根据特定变量的值呈现特定的表达式。", "syntax": "{{#switch variable}}\n{{#case condition /}}\n  value_1\n{{#case condition /}}\n  value_2\n{{#else/}}\n  value_default\n{{/switch}}", "syntax_keywords": []}, {"arguments": [{"description": "The name of the variable to be defined", "description_cn": "需要定义的变量名", "name": "variable", "types": ["string"]}], "deprecated": false, "description": "You can create complex strings using sline logic and variables.", "description_cn": "您可以使用 Sline 逻辑和变量创建复杂的字符串。", "examples": [{"name": "Creating Page Title", "name_cn": "创建页面标题", "path": "/products/product-009-92883-19", "raw_sline": "{{#capture title}}\n  {{product.title}} - {{shop.name}}\n{{/capture}}\n\n{{{title}}}\n", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/tag/capture", "name": "capture", "summary": "Creates a new variable with a string value.", "summary_cn": "创建一个string变量。", "syntax": "{{#capture variable}}\n  value\n{{/capture}}\n", "syntax_keywords": []}, {"arguments": [{"description": "\nCorresponds to the section folder name under the sections directory.", "description_cn": "对应sections目录下的section文件夹名字。", "name": "section_name", "types": ["string"]}], "deprecated": false, "description": "Static sections are components that cannot be dragged to change the order of components in the theme editor.", "description_cn": "静态section是指无法在可视化编辑器中拖动改变组件顺序的组件。", "examples": [{"name": "Rendering header section", "name_cn": "渲染页头组件", "syntax": "{{#section \"header\" /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/section", "name": "section", "summary": "Renders a static section.", "summary_cn": "渲染静态section。", "syntax": "{{#section \"section_name\" /}}", "syntax_keywords": []}, {"deprecated": false, "examples": [{"description": "Render the header group.", "description_cn": "渲染header group。", "name": "Header group", "name_cn": "Header group", "path": "/", "raw_sline": "", "syntax": "{{#sections \"header\" /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/sections", "name": "sections", "summary": "Renders section group. Currently, this component is only allowed to be used in `layout` template.\n", "summary_cn": "渲染 section group，当前组件只允许在`layout`模板中使用。    \n", "syntax": "{{#sections \"file_name\" /}}", "syntax_keywords": [{"description": "`sections/*.json` is the name of the json file in the sections directory. For example, the default `sections/header.json` needs to be written as `header` here.", "description_cn": "`sections/*.json` sections目录下的json文件名字，比如默认的 `sections/header.json` 这里需要写成 `header` 。", "keyword": "file_name"}]}, {"deprecated": false, "link": "https://developer.shopline.com/docs/sline/tag/blocks", "name": "blocks", "summary": "Renders all blocks in the current scope. It can only be used in section and block templates. It will automatically traverse all blocks in the current level. A `forblock` object will be issued in the scope. It supports some special logic processing. It needs to be matched with the [block](/docs/sline/tag/block) tag to render the content.", "summary_cn": "渲染当前作用域下的所有block，只能在section和block模板中使用，会自动遍历当前层级所有的block，作用域内会下发 `forblock` object，支持做一些特殊逻辑处理，需要搭配[block](/docs/sline/tag/block) tag渲染内容", "syntax": "{{#blocks}}\n  {{#block /}}\n{{/blocks}}"}, {"deprecated": false, "examples": [{"description": "Pass special parameters to the `@/product-card` block.", "description_cn": "针对 `@/product-card` block传入特殊参数 。", "name": "Special block processing", "name_cn": "特殊block处理", "syntax": "<!-- section/product-list/product-list.html -->\n{{#blocks}}\n  {{#if forblock.type == \"@/product-card\"}}\n    {{#block product=product /}}\n  {{#else /}}\n    {{#block /}}\n  {{/if}}\n{{/blocks}}\n\n\n<!-- blocks/product-card.html -->\n<div>{{ props.product.title }}</div>"}], "link": "https://developer.shopline.com/docs/sline/tag/block", "name": "block", "summary": "Renders the blocks in the current iteration scope. Use with the blocks tag.", "summary_cn": "渲染当前迭代作用域中的block，需配合blocks tag使用。", "syntax": "{{#blocks}}\n  {{#block [hash...] /}}\n{{/blocks}}", "syntax_keywords": [{"description": "Support passing parameters to block components.", "description_cn": "支持传入参数到block组件。", "keyword": "hash"}]}, {"deprecated": false, "description": "For detailed configuration rules, refer to [Theme Editor](/docs/online-store-3-0-themes/theme-and-editor-interaction/overview):\n- [section](/docs/online-store-3-0-themes/theme-structure/sections)\n- [block](/docs/online-store-3-0-themes/theme-structure/blocks)\n- [theme.schema.json](/docs/online-store-3-0-themes/theme-structure/theme-schema-json)", "description_cn": "  详细配置规则参考[可视化编辑器](/docs/online-store-3-0-themes/theme-and-editor-interaction/overview)：\n- [section](/docs/online-store-3-0-themes/theme-structure/sections)\n- [block](/docs/online-store-3-0-themes/theme-structure/blocks)\n- [theme.schema.json](/docs/online-store-3-0-themes/theme-structure/theme-schema-json)", "link": "https://developer.shopline.com/docs/sline/tag/schema", "name": "schema", "summary": "Schema is a configuration item used to define the dynamic components of the visual editor. The content inside the tag is a json.", "summary_cn": "schema是用于定义可视化编辑器动态组件的配置项，标签内的内容是一个json。 ", "syntax": "{{#schema}}\njson_content\n{{/schema}}", "syntax_keywords": [{"description": "Theme Editor Configuration", "description_cn": "可视化编辑器配置", "keyword": "json_content"}]}, {"deprecated": false, "description": "Renders the specified public component, which can be a global component (under the component directory) or a private section/block component (under the corresponding section/block directory).", "description_cn": "渲染指定的公共组件，组件可以是全局组件（component目录下）或者私有section/block组件（对应section/block目录下）。", "examples": [{"description": "Import the global `components/title.html` component.", "description_cn": "导入全局`components/title.html`组件。", "name": "Using Global Components", "name_cn": "使用全局组件", "syntax": "{{#component \"title\" /}}"}, {"description": "For example, currently in the `sections/header/header.html` template, use the `sections/header/title.html` template.", "description_cn": "比如当前在 `sections/header/header.html` 模板中，使用 `sections/header/title.html` 模板。", "name": "Importing private components", "name_cn": "引入私有组件", "syntax": "{{#component \"./title\" /}}"}, {"description": "For example, you want to pass the variables in section.settings.title into the title component.", "description_cn": "例如要把section.settings.title中的变量传入到title组件内。", "name": "Component passing parameters", "name_cn": "组件传递参数", "syntax": "<!--! sections/header/header.html -->\n{{#component \"./title\" title=section.settings.title show=true /}}\n\n<!--! sections/header/title.html -->\n{{#if show}}\n    {{ title }}\n{{/if}}"}], "hashs": [], "link": "https://developer.shopline.com/docs/sline/tag/component", "name": "component", "summary": "The component is isolated in scope. If external variables are needed, they must be manually passed in through hash parameters. The Sline global object has no restrictions.", "summary_cn": "component组件是隔离作用域的，如果需要外部变量需要手动通过hash参数传入，Sline全局object不限制。", "syntax": "{{#component file_path [hash...] /}}", "syntax_keywords": [{"description": "The component file path can be a relative path or an absolute path. The relative path needs to start with `./`, and the absolute path is the file under `components/`.", "description_cn": "组件文件路径，可以是相对路径或者绝对路径，相对路径需要以 `./` 开头，绝对路径就是`components/`下的文件。", "keyword": "file_path"}, {"description": "Dynamic hash parameters(key=value), with no limit on the number, are used to pass variable parameters required by the component.", "description_cn": "动态hash参数（key=value），不限制个数，用于传入component中需要的变量参数。", "keyword": "hash"}]}, {"arguments": [{"description": "Constant enumeration string `header` `footer` `layout` `blocks`", "description_cn": "常量枚举字符串 `header` `footer` `layout` `blocks`", "name": "content_type", "types": ["string"]}], "deprecated": false, "description": "Applicable content enumeration:\n- `header`: fill the header, only used in layout\n- `footer`: fill the footer, only used in layout\n- `layout`: fill the template content, only used in layout\n- `blocks`: render all sub-blocks under the current section/block", "description_cn": "可适用的内容枚举：\n- `header`：填充页头，仅限在layout中使用\n- `footer`：填充页脚，仅限在layout中使用\n- `layout`：填充模板内容，仅限在layout中使用\n- `blocks`：渲染当前 section/block 下的所有子block", "examples": [{"description": "", "name": "Render the dynamic block under section", "name_cn": "渲染section下的动态block", "raw_sline": "", "syntax": "{{#content \"blocks\" /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/content", "name": "content", "summary": "Renders dynamic content of the specified type.", "summary_cn": "渲染指定类型动态内容。", "syntax": "{{#content content_type /}}"}, {"arguments": [{"description": "The name of the layout file you want to use (enclosed in quotes), or no layout (`none`).", "description_cn": "您要使用的布局文件的名称（用引号括起来），或者为无布局（`none`）。", "name": "name", "types": ["string"]}], "deprecated": false, "description_cn": "默认使用 layouts/theme.html", "examples": [{"description": "Do not use the template under layout.", "description_cn": "不使用layout下的模板。", "name": "Not using layout", "name_cn": "不使用layout", "raw_sline": "", "syntax": "{{#layout none /}}"}, {"description_cn": "使用 `layout/password.html` 模板", "name": "Using a special template", "name_cn": "使用指定模板", "raw_sline": "", "syntax": "{{#layout \"password\" /}}"}], "link": "https://developer.shopline.com/docs/sline/tag/layout", "name": "layout", "summary": "Specifies the layout to be rendered. This can only be used in HTML templates.", "summary_cn": "指定渲染的layout，仅能在html template中使用。", "syntax": "{{#layout name /}}"}, {"arguments": [{"description": "The time string to be converted.", "description_cn": "需要转换的时间字符串。", "name": "timestring", "types": ["string"]}], "deprecated": false, "examples": [{"name": "Show product published time", "name_cn": "展示商品发布时间", "path": "/products/earring-ab001", "raw_sline": "{{#time_tag product.published_at /}}"}], "hashs": [{"description": "Date formatting types, supported types: `abbreviated_date`, `basic`, `date`, `date_at_time`, `default`, `on_date`, you can also use placeholders to customize the format of the date output, see: https://www.strfti.me/", "description_cn": "日期的格式化类型，支持的类型：`abbreviated_date`、`basic`、`date`、`date_at_time`、`default`、`on_date`，也可使用占位符自定义日期输出的格式，参考：https://www.strfti.me/", "name": "format", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/time-tag", "name": "time_tag", "summary": "Convert timestamps to HTML `<time>` tag.", "summary_cn": "将时间戳转换为 HTML `<time>` 标记。", "syntax": "{{#time_tag timestring [format=string] /}}"}, {"arguments": [], "deprecated": false, "examples": [{"name": "Setting the title color", "name_cn": "设置标题颜色", "raw_sline": "{{#style media=\"all\"}}\n.title {\n  color: red;\n}\n{{/style}}\n\n", "syntax": ""}], "link": "https://developer.shopline.com/docs/sline/tag/style", "name": "style", "summary": "Generate HTML `<style>` tag.", "summary_cn": "生成 HTML `<style>` 标签。", "syntax": "{{#style [attr=value]}} content {{/style}}"}, {"arguments": [{"description": "The url to jump to.", "description_cn": "需要跳转的 url。", "name": "url", "types": ["string"]}], "deprecated": false, "examples": [{"name": "Generate jump link tags for product detail pages", "name_cn": "生成商品详细页的跳转链接标签", "path": "/products/earring-ab001", "raw_sline": "{{#link_to product.url}}\n  {{product.title}}\n{{/link_to}}\n\n"}], "hashs": [], "link": "https://developer.shopline.com/docs/sline/tag/link-to", "name": "link_to", "summary": "Generate HTML `<a>` tag.", "summary_cn": "生成 HTML `<a>` 标签。", "syntax": "{{#link_to url}}{{/link_to}}"}, {"arguments": [{"description": "The string to be wrapped, which must appear in `content`.", "description_cn": "需要包装的字符串，该字符串必须在 `content` 中出现。", "name": "search", "types": ["string"]}], "deprecated": false, "examples": [{"name": "Bolding of a sub-string in a product title", "name_cn": "商品标题中的一段子字符串加粗", "path": "/products/earring-ab001", "raw_sline": "{{#highlight \"[AB001]\"}}{{product.title}}{{/highlight}}"}], "link": "https://developer.shopline.com/docs/sline/tag/highlight", "name": "highlight", "summary": "Wrap all instances of a specific string in a given string with an HTML `<strong>` tag with a class attribute of `highlight`.", "summary_cn": "用一个 class 属性为 `highlight` 的 HTML `<strong>` 标记包裹给定字符串中特定字符串的所有实例。", "syntax": "{{#highlight search}}content{{/highlight}}"}, {"arguments": [{"description": "The url of the resource to be preloaded.", "description_cn": "需要预加载的资源 url。", "name": "url", "types": ["string"]}], "deprecated": false, "description_cn": "你应该谨慎使用此过滤器。例如，考虑只预加载渲染所需的资源首屏内容。", "examples": [{"name": "Preload the specified javascript link", "name_cn": "预加载指定 javascript 链接", "raw_sline": "{{#preload_tag \"base/index.js\" | asset_url() /}}"}], "hashs": [{"description": "Sets the `as` attribute.", "description_cn": "设置 `as` 属性。", "name": "as", "types": ["string"]}, {"description": "Sets the `rel` attribute.", "description_cn": "设置 `rel` 属性。", "name": "rel", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/preload-tag", "name": "preload_tag", "summary_cn": "生成一个 HTML `<link>` 标签，其 rel 属性为 preload ，用于优先加载 Shopline 托管的特定资源。资源 URL 也会添加到 Link 标头中 具有 preload 的 rel 属性。", "syntax": "{{#preload_tag url [as=string] [rel=string] /}}"}, {"arguments": [{"description": "CSS resource URL.", "description_cn": "CSS 资源 URL。", "name": "url", "types": ["string"]}], "deprecated": false, "examples": [{"name": "Generate link tags for css files in themes", "name_cn": "为主题里的 css 文件生成 link 标签", "raw_sline": "{{#stylesheet \"base/index.css\" | asset_url() /}}"}], "hashs": [{"description": "If or not preloading is required, a value of `true` will add the asset URL to the Link header as well.", "description_cn": "是否需要预加载，该属性为 `true` 时会对该资产 URL 也会添加到 Link 标头中。", "name": "preload", "types": ["boolean"]}, {"description": "Sets the media property.", "description_cn": "设置 media 属性。", "name": "media", "types": ["string"]}], "link": "https://developer.shopline.com/docs/sline/tag/stylesheet", "name": "stylesheet", "summary": "Generates an HTML `<link>` tag for a given CSS resource URL.", "summary_cn": "为一个给定的 CSS 资源 URL 生成一个HTML `<link>` 标签。", "syntax": "{{#stylesheet url [preload=boolean] [media=string] /}}"}, {"arguments": [{"description": "Resource URL.", "description_cn": "资源 URL.", "name": "url", "types": ["string"]}], "deprecated": false, "description": "", "description_cn": "", "examples": [{"name": "Generate script tags for javascript files in the theme", "name_cn": "为主题里的 javascript 文件生成 script 标签", "raw_sline": "{{#script \"base/index.js\" | asset_url() }}\n{{/script}}"}], "link": "https://developer.shopline.com/docs/sline/tag/script", "name": "script", "summary": "Generates an HTML `<script>` tag for a given resource URL. The tag has a type attribute of `text/javascript`.\n\n", "summary_cn": "为给定的资源 URL 生成 HTML `<script>` 标记。该标签的 type 属性为 `text/javascript`。", "syntax": "{{#script url}}{{/script}}"}, {"arguments": [{"description": "Specific values to be matched.", "description_cn": "需要匹配的特定值。", "name": "value", "types": ["string", "number", "boolean"]}], "deprecated": false, "description": "By matching specific values, program execution is routed to the corresponding code block, the core role is to provide a structured processing solution for multiplexed conditional judgments, appropriate use can improve code readability and maintainability.", "description_cn": "通过匹配特定值，将程序执行路由到对应的代码块，核心作用是为多路条件判断提供结构化处理方案，适当使用可以提升代码可读性和可维护性。", "examples": [{"description": "Determining if a single value satisfies the condition.", "description_cn": "", "name": "Display different logic based on product type", "name_cn": "根据商品类型展示不同的逻辑", "path": "/products/earring-ab001", "raw_sline": "{{#switch product.type}}\n  {{#case \"3c\" /}}\n    show 3c product\n  {{#case \"dress\" /}}\n    show dress product\n  {{#else/}}\n    show other product\n{{/switch}}", "source_object": "product"}], "link": "https://developer.shopline.com/docs/sline/tag/case", "name": "case", "summary": "The `case tag` is used within the [switch tag](/docs/sline/tag/switch) to define specific logic branches. ", "summary_cn": "`case tag` 用于在 [switch tag](/docs/sline/tag/switch) 中定义具体的逻辑分支.", "syntax": "{{#switch variable}}\n  {{#case first_value /}}\n    first_expression\n  {{#case second_value /}}\n    second_expression\n  {{#else/}}\n    third_expression\n{{/switch}}"}]