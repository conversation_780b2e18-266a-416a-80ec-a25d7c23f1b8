// SLine 上下文分析器测试
import { describe, it, expect } from '@jest/globals';
import { ContextAnalyzer } from './contextAnalysis';
import { TextDocument } from 'vscode-languageserver-textdocument';

describe('ContextAnalyzer', () => {
    
    function createDocument(content: string): TextDocument {
        return TextDocument.create('test://test.sline', 'sline', 1, content);
    }

    function createPosition(line: number, character: number) {
        return { line, character };
    }

    describe('analyzeCompletionContext', () => {
        it('should detect tag context', () => {
            const document = createDocument('{{#fo');
            const position = createPosition(0, 5);
            
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            
            expect(context.type).toBe('tag');
            expect(context.prefix).toBe('fo');
        });

        it('should detect filter context', () => {
            const document = createDocument('{{ product.title | up');
            const position = createPosition(0, 21);
            
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            
            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('up');
        });

        it('should detect object property context', () => {
            const document = createDocument('{{ product.ti');
            const position = createPosition(0, 13); // Position after 'ti'

            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            // Debug: let's see what we actually get
            console.log('Context:', context);
            console.log('Content at position:', document.getText().substring(0, 13));

            expect(context.type).toBe('object_property');
            expect(context.prefix).toBe('ti');
            expect(context.objectName).toBe('product');
        });

        it('should detect object access start', () => {
            const document = createDocument('{{ product.');
            const position = createPosition(0, 12);
            
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            
            expect(context.type).toBe('object_property');
            expect(context.prefix).toBe('');
            expect(context.objectName).toBe('product');
        });

        it('should detect tag parameter context', () => {
            const document = createDocument('{{#for item in products limit:');
            const position = createPosition(0, 31);
            
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            
            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('for');
        });

        it('should detect filter parameter context', () => {
            const document = createDocument('{{ price | money(currency=');
            const position = createPosition(0, 27);
            
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            
            expect(context.type).toBe('filter_parameter');
            expect(context.filterName).toBe('money');
        });

        it('should return unknown for content outside expressions', () => {
            const document = createDocument('<div>Hello World</div>');
            const position = createPosition(0, 10);
            
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            
            expect(context.type).toBe('unknown');
        });

        it('should handle nested expressions', () => {
            const document = createDocument('{{#if product.available}}{{ product.title | up');
            const position = createPosition(0, 47);
            
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            
            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('up');
        });
    });

    describe('getWordRangeAtPosition', () => {
        it('should return correct word range', () => {
            const document = createDocument('{{ product.title }}');
            const position = createPosition(0, 6); // middle of 'product'
            
            const range = ContextAnalyzer.getWordRangeAtPosition(document, position);
            
            expect(range.start.character).toBe(3);
            expect(range.end.character).toBe(10);
        });

        it('should handle position at word boundary', () => {
            const document = createDocument('{{ product.title }}');
            const position = createPosition(0, 10); // end of 'product'
            
            const range = ContextAnalyzer.getWordRangeAtPosition(document, position);
            
            expect(range.start.character).toBe(3);
            expect(range.end.character).toBe(10);
        });
    });

    describe('isInSlineExpression', () => {
        it('should return true when inside expression', () => {
            const document = createDocument('{{ product.title }}');
            const position = createPosition(0, 10);
            
            const result = ContextAnalyzer.isInSlineExpression(document, position);
            
            expect(result).toBe(true);
        });

        it('should return false when outside expression', () => {
            const document = createDocument('<div>{{ product.title }}</div>');
            const position = createPosition(0, 2);
            
            const result = ContextAnalyzer.isInSlineExpression(document, position);
            
            expect(result).toBe(false);
        });

        it('should return false when after closed expression', () => {
            const document = createDocument('{{ product.title }} <div>');
            const position = createPosition(0, 25);
            
            const result = ContextAnalyzer.isInSlineExpression(document, position);
            
            expect(result).toBe(false);
        });
    });

    describe('getCurrentExpression', () => {
        it('should return current expression content', () => {
            const document = createDocument('{{ product.title | upcase }}');
            const position = createPosition(0, 15);
            
            const expression = ContextAnalyzer.getCurrentExpression(document, position);
            
            expect(expression).toBe(' product.title | upcase ');
        });

        it('should return null when not in expression', () => {
            const document = createDocument('<div>Hello</div>');
            const position = createPosition(0, 5);
            
            const expression = ContextAnalyzer.getCurrentExpression(document, position);
            
            expect(expression).toBeNull();
        });

        it('should handle incomplete expressions', () => {
            const document = createDocument('{{ product.title');
            const position = createPosition(0, 15);
            
            const expression = ContextAnalyzer.getCurrentExpression(document, position);
            
            expect(expression).toBe(' product.title');
        });
    });

    describe('isInTag', () => {
        it('should detect when inside tag', () => {
            const document = createDocument('{{#for item in products}}');
            const position = createPosition(0, 10);
            
            const result = ContextAnalyzer.isInTag(document, position);
            
            expect(result.inTag).toBe(true);
            expect(result.tagName).toBe('for');
        });

        it('should return false when not in tag', () => {
            const document = createDocument('{{ product.title }}');
            const position = createPosition(0, 10);
            
            const result = ContextAnalyzer.isInTag(document, position);
            
            expect(result.inTag).toBe(false);
        });
    });

    describe('isInFilterChain', () => {
        it('should detect when in filter chain', () => {
            const document = createDocument('{{ product.title | upcase | truncate }}');
            const position = createPosition(0, 30);
            
            const result = ContextAnalyzer.isInFilterChain(document, position);
            
            expect(result.inFilter).toBe(true);
            expect(result.filterName).toBe('truncate');
        });

        it('should return false when not in filter chain', () => {
            const document = createDocument('{{ product.title }}');
            const position = createPosition(0, 10);
            
            const result = ContextAnalyzer.isInFilterChain(document, position);
            
            expect(result.inFilter).toBe(false);
        });
    });

    describe('analyzeNestedContext', () => {
        it('should detect for loop context', () => {
            const document = createDocument('{{#for product in products}}{{ product.title }}{{/for}}');
            const position = createPosition(0, 35);
            
            const result = ContextAnalyzer.analyzeNestedContext(document, position);
            
            expect(result.inLoop).toBe(true);
            expect(result.loopVariable).toBe('product');
            expect(result.availableVariables).toContain('product');
            expect(result.availableVariables).toContain('forloop');
        });

        it('should detect conditional context', () => {
            const document = createDocument('{{#if product.available}}{{ product.title }}{{/if}}');
            const position = createPosition(0, 30);
            
            const result = ContextAnalyzer.analyzeNestedContext(document, position);
            
            expect(result.inConditional).toBe(true);
        });

        it('should detect with context', () => {
            const document = createDocument('{{#with product as item}}{{ item.title }}{{/with}}');
            const position = createPosition(0, 30);
            
            const result = ContextAnalyzer.analyzeNestedContext(document, position);
            
            expect(result.availableVariables).toContain('item');
        });

        it('should handle multiple nested contexts', () => {
            const document = createDocument('{{#for product in products}}{{#if product.available}}{{ product.title }}{{/if}}{{/for}}');
            const position = createPosition(0, 60);
            
            const result = ContextAnalyzer.analyzeNestedContext(document, position);
            
            expect(result.inLoop).toBe(true);
            expect(result.inConditional).toBe(true);
            expect(result.availableVariables).toContain('product');
        });
    });
});
