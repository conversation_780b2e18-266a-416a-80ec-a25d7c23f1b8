// SLine 代码片段管理器测试
import { describe, it, expect, beforeEach } from '@jest/globals';
import { SnippetManager } from './snippets';
import { CompletionItemKind, InsertTextFormat } from 'vscode-languageserver/node';

describe('SnippetManager', () => {
    let snippetManager: SnippetManager;

    beforeEach(() => {
        snippetManager = new SnippetManager();
    });

    describe('getSnippetCompletions', () => {
        it('should return all snippets when no prefix provided', () => {
            const completions = snippetManager.getSnippetCompletions('');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions[0].kind).toBe(CompletionItemKind.Snippet);
            expect(completions[0].insertTextFormat).toBe(InsertTextFormat.Snippet);
        });

        it('should filter snippets by prefix', () => {
            const completions = snippetManager.getSnippetCompletions('for');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.every(c => c.label.toLowerCase().startsWith('for'))).toBe(true);
        });

        it('should return empty array for non-matching prefix', () => {
            const completions = snippetManager.getSnippetCompletions('xyz123');
            
            expect(completions).toHaveLength(0);
        });

        it('should include proper snippet structure', () => {
            const completions = snippetManager.getSnippetCompletions('for');
            const forSnippet = completions.find(c => c.label === 'for');
            
            expect(forSnippet).toBeDefined();
            expect(forSnippet!.detail).toContain('Snippet');
            expect(forSnippet!.insertText).toContain('{{#for');
            expect(forSnippet!.insertText).toContain('{{/for}}');
        });
    });

    describe('getSnippetsByCategory', () => {
        it('should return control flow snippets', () => {
            const completions = snippetManager.getSnippetsByCategory('control');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(c => c.label === 'for')).toBe(true);
            expect(completions.some(c => c.label === 'if')).toBe(true);
        });

        it('should return form snippets', () => {
            const completions = snippetManager.getSnippetsByCategory('form');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(c => c.label === 'loginform')).toBe(true);
            expect(completions.some(c => c.label === 'registerform')).toBe(true);
        });

        it('should return layout snippets', () => {
            const completions = snippetManager.getSnippetsByCategory('layout');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(c => c.label === 'layout')).toBe(true);
        });

        it('should return data snippets', () => {
            const completions = snippetManager.getSnippetsByCategory('data');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(c => c.label === 'productcard')).toBe(true);
            expect(completions.some(c => c.label === 'articlepreview')).toBe(true);
        });

        it('should return utility snippets', () => {
            const completions = snippetManager.getSnippetsByCategory('utility');
            
            expect(completions.length).toBeGreaterThan(0);
            expect(completions.some(c => c.label === 'pagination')).toBe(true);
            expect(completions.some(c => c.label === 'breadcrumb')).toBe(true);
        });
    });

    describe('getSnippetByName', () => {
        it('should return snippet by name', () => {
            const snippet = snippetManager.getSnippetByName('for-loop');
            
            expect(snippet).toBeDefined();
            expect(snippet!.name).toBe('for-loop');
            expect(snippet!.prefix).toBe('for');
            expect(snippet!.category).toBe('control');
        });

        it('should return undefined for non-existent snippet', () => {
            const snippet = snippetManager.getSnippetByName('non-existent');
            
            expect(snippet).toBeUndefined();
        });
    });

    describe('getAllPrefixes', () => {
        it('should return all snippet prefixes', () => {
            const prefixes = snippetManager.getAllPrefixes();
            
            expect(prefixes.length).toBeGreaterThan(0);
            expect(prefixes).toContain('for');
            expect(prefixes).toContain('if');
            expect(prefixes).toContain('loginform');
            expect(prefixes).toContain('layout');
        });

        it('should not contain duplicates', () => {
            const prefixes = snippetManager.getAllPrefixes();
            const uniquePrefixes = [...new Set(prefixes)];
            
            expect(prefixes.length).toBe(uniquePrefixes.length);
        });
    });

    describe('snippet content validation', () => {
        it('should have valid for loop snippet', () => {
            const snippet = snippetManager.getSnippetByName('for-loop');
            
            expect(snippet).toBeDefined();
            expect(snippet!.body).toContain('{{#for ${1:item} in ${2:collection}}}');
            expect(snippet!.body).toContain('{{/for}}');
            expect(snippet!.description).toBeTruthy();
            expect(snippet!.description_cn).toBeTruthy();
        });

        it('should have valid if-else snippet', () => {
            const snippet = snippetManager.getSnippetByName('if-else');
            
            expect(snippet).toBeDefined();
            expect(snippet!.body).toContain('{{#if ${1:condition}}}');
            expect(snippet!.body).toContain('{{#else}}');
            expect(snippet!.body).toContain('{{/if}}');
        });

        it('should have valid customer login form snippet', () => {
            const snippet = snippetManager.getSnippetByName('customer-login-form');
            
            expect(snippet).toBeDefined();
            expect(snippet!.body).toContain('{{#customer_login_form}}');
            expect(snippet!.body).toContain('{{/customer_login_form}}');
            expect(snippet!.body).toContain('customer[email]');
            expect(snippet!.body).toContain('customer[password]');
        });

        it('should have valid product card snippet', () => {
            const snippet = snippetManager.getSnippetByName('product-card');
            
            expect(snippet).toBeDefined();
            expect(snippet!.body).toContain('product.title');
            expect(snippet!.body).toContain('product.price');
            expect(snippet!.body).toContain('image_url');
        });

        it('should have valid layout snippet', () => {
            const snippet = snippetManager.getSnippetByName('layout-basic');
            
            expect(snippet).toBeDefined();
            expect(snippet!.body).toContain('<!DOCTYPE html>');
            expect(snippet!.body).toContain('content_for_header');
            expect(snippet!.body).toContain('content_for_layout');
        });
    });

    describe('snippet completion item structure', () => {
        it('should create proper completion items', () => {
            const completions = snippetManager.getSnippetCompletions('for');
            const forCompletion = completions.find(c => c.label === 'for');
            
            expect(forCompletion).toBeDefined();
            expect(forCompletion!.kind).toBe(CompletionItemKind.Snippet);
            expect(forCompletion!.insertTextFormat).toBe(InsertTextFormat.Snippet);
            expect(forCompletion!.detail).toContain('Snippet');
            expect(forCompletion!.documentation).toBeDefined();
            expect(forCompletion!.sortText).toContain('snippet_');
            expect(forCompletion!.filterText).toBe('for');
        });

        it('should include markdown documentation', () => {
            const completions = snippetManager.getSnippetCompletions('if');
            const ifCompletion = completions.find(c => c.label === 'if');
            
            expect(ifCompletion).toBeDefined();
            expect(ifCompletion!.documentation).toBeDefined();
            
            if (typeof ifCompletion!.documentation === 'object') {
                expect(ifCompletion!.documentation.kind).toBe('markdown');
                expect(ifCompletion!.documentation.value).toContain('```sline');
            }
        });

        it('should include data for snippet identification', () => {
            const completions = snippetManager.getSnippetCompletions('layout');
            const layoutCompletion = completions.find(c => c.label === 'layout');
            
            expect(layoutCompletion).toBeDefined();
            expect(layoutCompletion!.data).toBeDefined();
            expect(layoutCompletion!.data.type).toBe('snippet');
            expect(layoutCompletion!.data.name).toBeTruthy();
        });
    });

    describe('snippet categories coverage', () => {
        it('should have snippets for all categories', () => {
            const categories: Array<'control' | 'form' | 'layout' | 'data' | 'utility'> = 
                ['control', 'form', 'layout', 'data', 'utility'];
            
            for (const category of categories) {
                const snippets = snippetManager.getSnippetsByCategory(category);
                expect(snippets.length).toBeGreaterThan(0);
            }
        });

        it('should have proper category distribution', () => {
            const allPrefixes = snippetManager.getAllPrefixes();
            
            // Should have at least 2 snippets per category
            expect(snippetManager.getSnippetsByCategory('control').length).toBeGreaterThanOrEqual(2);
            expect(snippetManager.getSnippetsByCategory('form').length).toBeGreaterThanOrEqual(2);
            expect(snippetManager.getSnippetsByCategory('data').length).toBeGreaterThanOrEqual(2);
            expect(snippetManager.getSnippetsByCategory('utility').length).toBeGreaterThanOrEqual(2);
            
            // Total should match all prefixes
            const totalByCategory = ['control', 'form', 'layout', 'data', 'utility']
                .reduce((sum, cat) => sum + snippetManager.getSnippetsByCategory(cat as any).length, 0);
            expect(totalByCategory).toBe(allPrefixes.length);
        });
    });
});
