// SLine 参考数据管理器测试
import { describe, it, expect, beforeEach } from '@jest/globals';
import { ReferenceDataManager } from './referenceData';
import { CompletionItemKind } from 'vscode-languageserver/node';
import * as fs from 'fs';
import * as path from 'path';

// Mock file system
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('ReferenceDataManager', () => {
    let referenceDataManager: ReferenceDataManager;
    const mockExtensionPath = '/mock/extension/path';

    // Mock JSON data
    const mockTagsData = [
        {
            name: 'for',
            syntax: '{{#for item in collection}}...{{/for}}',
            summary: 'Iterate over a collection',
            summary_cn: '遍历集合',
            arguments: [
                {
                    name: 'item',
                    types: ['string'],
                    description: 'Iterator variable',
                    description_cn: '迭代变量'
                }
            ],
            examples: [
                {
                    raw_sline: '{{#for product in products}}{{ product.title }}{{/for}}'
                }
            ],
            link: 'https://docs.example.com/for',
            deprecated: false
        },
        {
            name: 'old_tag',
            syntax: '{{#old_tag}}...{{/old_tag}}',
            summary: 'Deprecated tag',
            summary_cn: '已弃用的标签',
            arguments: [],
            examples: [],
            link: 'https://docs.example.com/old_tag',
            deprecated: true
        }
    ];

    const mockObjectsData = [
        {
            name: 'product',
            summary: 'Product object',
            summary_cn: '产品对象',
            properties: [
                {
                    name: 'title',
                    return_type: 'string',
                    summary: 'Product title',
                    summary_cn: '产品标题'
                },
                {
                    name: 'price',
                    return_type: 'number',
                    summary: 'Product price',
                    summary_cn: '产品价格'
                }
            ],
            link: 'https://docs.example.com/product'
        }
    ];

    const mockFiltersData = [
        {
            name: 'money',
            summary: 'Format as money',
            summary_cn: '格式化为货币',
            arguments: [
                {
                    name: 'currency',
                    types: ['string'],
                    description: 'Currency code',
                    description_cn: '货币代码'
                }
            ],
            return_type: ['string'],
            examples: [
                {
                    raw_sline: '{{ product.price | money() }}'
                }
            ],
            syntax: 'value | money(currency)',
            link: 'https://docs.example.com/money'
        }
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        referenceDataManager = new ReferenceDataManager(mockExtensionPath);

        // Mock file existence and content
        mockFs.existsSync.mockImplementation((filePath: string) => {
            const fileName = path.basename(filePath as string);
            return ['tag.json', 'objects.json', 'filter.json'].includes(fileName);
        });

        mockFs.readFileSync.mockImplementation((filePath: string) => {
            const fileName = path.basename(filePath as string);
            switch (fileName) {
                case 'tag.json':
                    return JSON.stringify(mockTagsData);
                case 'objects.json':
                    return JSON.stringify(mockObjectsData);
                case 'filter.json':
                    return JSON.stringify(mockFiltersData);
                default:
                    throw new Error(`File not found: ${filePath}`);
            }
        });
    });

    describe('loadReferenceData', () => {
        it('should load all reference data successfully', async () => {
            await referenceDataManager.loadReferenceData();

            expect(referenceDataManager.hasTag('for')).toBe(true);
            expect(referenceDataManager.hasObject('product')).toBe(true);
            expect(referenceDataManager.hasFilter('money')).toBe(true);
        });

        it('should handle missing files gracefully', async () => {
            mockFs.existsSync.mockReturnValue(false);

            await expect(referenceDataManager.loadReferenceData()).resolves.not.toThrow();
        });
    });

    describe('getTagCompletions', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return all tags when no prefix provided', () => {
            const completions = referenceDataManager.getTagCompletions('');
            expect(completions).toHaveLength(2);
            expect(completions[0].label).toBe('for');
            expect(completions[0].kind).toBe(CompletionItemKind.Function);
        });

        it('should filter tags by prefix', () => {
            const completions = referenceDataManager.getTagCompletions('fo');
            expect(completions).toHaveLength(1);
            expect(completions[0].label).toBe('for');
        });

        it('should mark deprecated tags', () => {
            const completions = referenceDataManager.getTagCompletions('old');
            expect(completions).toHaveLength(1);
            expect(completions[0].deprecated).toBe(true);
        });
    });

    describe('getFilterCompletions', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return filter completions with proper format', () => {
            const completions = referenceDataManager.getFilterCompletions('money');
            expect(completions).toHaveLength(1);
            expect(completions[0].label).toBe('money');
            expect(completions[0].insertText).toBe('money($0)');
            expect(completions[0].kind).toBe(CompletionItemKind.Function);
        });
    });

    describe('getObjectPropertyCompletions', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return object properties', () => {
            const completions = referenceDataManager.getObjectPropertyCompletions('product', '');
            expect(completions).toHaveLength(2);
            expect(completions.map(c => c.label)).toContain('title');
            expect(completions.map(c => c.label)).toContain('price');
        });

        it('should filter properties by prefix', () => {
            const completions = referenceDataManager.getObjectPropertyCompletions('product', 'ti');
            expect(completions).toHaveLength(1);
            expect(completions[0].label).toBe('title');
        });

        it('should return empty array for unknown object', () => {
            const completions = referenceDataManager.getObjectPropertyCompletions('unknown', '');
            expect(completions).toHaveLength(0);
        });
    });

    describe('getHoverInfo', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return hover info for tags', () => {
            const hoverInfo = referenceDataManager.getHoverInfo('for');
            expect(hoverInfo).toBeDefined();
            expect(hoverInfo!.name).toBe('for');
            expect(hoverInfo!.summary).toBe('Iterate over a collection');
            expect(hoverInfo!.parameters).toHaveLength(1);
        });

        it('should return hover info for filters', () => {
            const hoverInfo = referenceDataManager.getHoverInfo('money');
            expect(hoverInfo).toBeDefined();
            expect(hoverInfo!.name).toBe('money');
            expect(hoverInfo!.returnType).toBe('string');
        });

        it('should return null for unknown items', () => {
            const hoverInfo = referenceDataManager.getHoverInfo('unknown');
            expect(hoverInfo).toBeNull();
        });
    });

    describe('getSimilarTags', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return similar tag suggestions', () => {
            const suggestions = referenceDataManager.getSimilarTags('fo');
            expect(suggestions).toContain('for');
        });

        it('should limit suggestions to 3', () => {
            const suggestions = referenceDataManager.getSimilarTags('');
            expect(suggestions.length).toBeLessThanOrEqual(3);
        });
    });

    describe('getSimilarFilters', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return similar filter suggestions', () => {
            const suggestions = referenceDataManager.getSimilarFilters('mon');
            expect(suggestions).toContain('money');
        });
    });

    describe('getAllTagNames', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return all tag names', () => {
            const tagNames = referenceDataManager.getAllTagNames();
            expect(tagNames).toContain('for');
            expect(tagNames).toContain('old_tag');
            expect(tagNames).toHaveLength(2);
        });
    });

    describe('getAllFilterNames', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return all filter names', () => {
            const filterNames = referenceDataManager.getAllFilterNames();
            expect(filterNames).toContain('money');
            expect(filterNames).toHaveLength(1);
        });
    });

    describe('getAllObjectNames', () => {
        beforeEach(async () => {
            await referenceDataManager.loadReferenceData();
        });

        it('should return all object names', () => {
            const objectNames = referenceDataManager.getAllObjectNames();
            expect(objectNames).toContain('product');
            expect(objectNames).toHaveLength(1);
        });
    });
});
