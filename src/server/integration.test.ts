// SLine 语言服务器集成测试
import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { 
    createConnection, 
    TextDocuments, 
    InitializeParams,
    CompletionParams,
    HoverParams,
    SignatureHelpParams
} from 'vscode-languageserver/node';
import { TextDocument } from 'vscode-languageserver-textdocument';
import { ReferenceDataManager } from './referenceData';
import { ContextAnalyzer } from './contextAnalysis';
import { SnippetManager } from './snippets';

describe('SLine Language Server Integration', () => {
    let referenceDataManager: ReferenceDataManager;
    let snippetManager: SnippetManager;
    let documents: TextDocuments<TextDocument>;

    beforeAll(async () => {
        // 初始化组件
        referenceDataManager = new ReferenceDataManager(__dirname);
        snippetManager = new SnippetManager();
        documents = new TextDocuments(TextDocument);

        // 模拟加载参考数据
        try {
            await referenceDataManager.loadReferenceData();
        } catch (error) {
            // 在测试环境中，JSON 文件可能不存在，这是正常的
            console.log('Reference data not loaded in test environment');
        }
    });

    afterAll(() => {
        // 清理资源
    });

    describe('Complete Workflow Tests', () => {
        it('should provide comprehensive completion for tag context', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{#fo'
            );

            const position = { line: 0, character: 5 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('tag');
            expect(context.prefix).toBe('fo');

            // 测试标签完成
            if (referenceDataManager.hasTag('for')) {
                const tagCompletions = referenceDataManager.getTagCompletions('fo');
                expect(tagCompletions.length).toBeGreaterThan(0);
                expect(tagCompletions[0].label).toBe('for');
            }

            // 测试代码片段完成
            const snippetCompletions = snippetManager.getSnippetCompletions('fo');
            expect(snippetCompletions.length).toBeGreaterThan(0);
        });

        it('should provide object property completion', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{ product.ti'
            );

            const position = { line: 0, character: 13 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('object_property');
            expect(context.prefix).toBe('ti');
            expect(context.objectName).toBe('product');

            // 测试对象属性完成
            if (referenceDataManager.hasObject('product')) {
                const propertyCompletions = referenceDataManager.getObjectPropertyCompletions('product', 'ti');
                // 在测试环境中可能没有实际数据，但应该不抛出错误
                expect(Array.isArray(propertyCompletions)).toBe(true);
            }
        });

        it('should provide filter completion', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{ product.price | mon'
            );

            const position = { line: 0, character: 22 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('mon');

            // 测试过滤器完成
            if (referenceDataManager.hasFilter('money')) {
                const filterCompletions = referenceDataManager.getFilterCompletions('mon');
                expect(filterCompletions.length).toBeGreaterThan(0);
                expect(filterCompletions[0].label).toBe('money');
            }
        });

        it('should provide hover information', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{ product.title | money }}'
            );

            // 测试标签悬停
            if (referenceDataManager.hasTag('for')) {
                const tagHover = referenceDataManager.getHoverInfo('for');
                expect(tagHover).toBeDefined();
                expect(tagHover!.name).toBe('for');
                expect(tagHover!.summary).toBeTruthy();
            }

            // 测试过滤器悬停
            if (referenceDataManager.hasFilter('money')) {
                const filterHover = referenceDataManager.getHoverInfo('money');
                expect(filterHover).toBeDefined();
                expect(filterHover!.name).toBe('money');
                expect(filterHover!.summary).toBeTruthy();
            }
        });

        it('should detect syntax errors with smart suggestions', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{#unknown_tag}}content{{/unknown_tag}}'
            );

            // 在实际实现中，这会通过 findSmartSyntaxErrors 检测
            const context = ContextAnalyzer.analyzeCompletionContext(document, { line: 0, character: 10 });
            
            // 验证上下文分析能正确识别标签
            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('unknown_tag');
        });

        it('should handle nested contexts correctly', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{#for product in products}}{{#if product.available}}{{ product.title }}{{/if}}{{/for}}'
            );

            const position = { line: 0, character: 60 }; // 在嵌套的 if 内部
            const nestedContext = ContextAnalyzer.analyzeNestedContext(document, position);

            expect(nestedContext.inLoop).toBe(true);
            expect(nestedContext.inConditional).toBe(true);
            expect(nestedContext.availableVariables).toContain('product');
            expect(nestedContext.availableVariables).toContain('forloop');
        });

        it('should provide signature help for tag parameters', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{#for item in products limit:'
            );

            const position = { line: 0, character: 31 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('for');

            // 测试参数完成
            if (referenceDataManager.hasTag('for')) {
                const paramCompletions = referenceDataManager.getTagParameterCompletions('for', '');
                expect(Array.isArray(paramCompletions)).toBe(true);
            }
        });

        it('should provide signature help for filter parameters', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{ price | money(currency='
            );

            const position = { line: 0, character: 27 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('filter_parameter');
            expect(context.filterName).toBe('money');

            // 测试过滤器参数完成
            if (referenceDataManager.hasFilter('money')) {
                const paramCompletions = referenceDataManager.getFilterParameterCompletions('money', '');
                expect(Array.isArray(paramCompletions)).toBe(true);
            }
        });
    });

    describe('Error Handling and Edge Cases', () => {
        it('should handle malformed expressions gracefully', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{ product.title | '
            );

            const position = { line: 0, character: 19 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('filter');
            expect(context.prefix).toBe('');
        });

        it('should handle incomplete tag expressions', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{#for item in'
            );

            const position = { line: 0, character: 14 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('for');
        });

        it('should handle multiple expressions on same line', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{ product.title }} and {{ product.price | money }}'
            );

            // 测试第一个表达式
            const position1 = { line: 0, character: 10 };
            const context1 = ContextAnalyzer.analyzeCompletionContext(document, position1);
            expect(context1.type).toBe('object_property');

            // 测试第二个表达式
            const position2 = { line: 0, character: 45 };
            const context2 = ContextAnalyzer.analyzeCompletionContext(document, position2);
            expect(context2.type).toBe('filter');
        });

        it('should handle multiline expressions', () => {
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                '{{#for product in products\n  limit: 10\n  offset: 0}}'
            );

            const position = { line: 1, character: 10 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);

            expect(context.type).toBe('tag_parameter');
            expect(context.tagName).toBe('for');
        });
    });

    describe('Performance and Scalability', () => {
        it('should handle large documents efficiently', () => {
            // 创建一个大文档
            const largeContent = Array(1000).fill('{{ product.title }}').join('\n');
            const document = TextDocument.create(
                'test://test.sline',
                'sline',
                1,
                largeContent
            );

            const startTime = Date.now();
            const position = { line: 500, character: 10 };
            const context = ContextAnalyzer.analyzeCompletionContext(document, position);
            const endTime = Date.now();

            expect(context.type).toBe('object_property');
            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
        });

        it('should cache completion items for performance', () => {
            // 测试多次调用相同的完成请求
            const startTime = Date.now();
            
            for (let i = 0; i < 100; i++) {
                const completions = snippetManager.getSnippetCompletions('for');
                expect(completions.length).toBeGreaterThan(0);
            }
            
            const endTime = Date.now();
            expect(endTime - startTime).toBeLessThan(50); // 缓存应该使其很快
        });
    });

    describe('Backward Compatibility', () => {
        it('should maintain compatibility with existing functionality', () => {
            // 测试基本的标签名称获取（向后兼容）
            const tagNames = referenceDataManager.getAllTagNames();
            expect(Array.isArray(tagNames)).toBe(true);

            const filterNames = referenceDataManager.getAllFilterNames();
            expect(Array.isArray(filterNames)).toBe(true);

            const objectNames = referenceDataManager.getAllObjectNames();
            expect(Array.isArray(objectNames)).toBe(true);
        });

        it('should handle missing reference data gracefully', () => {
            // 创建一个没有加载数据的管理器
            const emptyManager = new ReferenceDataManager('/nonexistent/path');
            
            expect(() => {
                emptyManager.getTagCompletions('test');
                emptyManager.getFilterCompletions('test');
                emptyManager.getObjectPropertyCompletions('test', 'test');
            }).not.toThrow();
        });
    });
});
