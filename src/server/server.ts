// SLine 语言服务器实现
import {
	createConnection,
	TextDocuments,
	Diagnostic,
	DiagnosticSeverity,
	ProposedFeatures,
	InitializeParams,
	DidChangeConfigurationNotification,
	CompletionItem,
	CompletionItemKind,
	TextDocumentPositionParams,
	TextDocumentSyncKind,
	InitializeResult,
	DocumentDiagnosticReportKind,
	type DocumentDiagnosticReport,
	HoverParams,
	Hover,
	MarkupKind,
	SignatureHelpParams,
	SignatureHelp,
	SignatureInformation,
	ParameterInformation
} from 'vscode-languageserver/node';

import { TextDocument } from 'vscode-languageserver-textdocument';
import { ReferenceDataManager } from './referenceData';
import { ContextAnalyzer } from './contextAnalysis';
import { SnippetManager } from './snippets';
import * as path from 'path';

// Create a connection for the server
const connection = createConnection(ProposedFeatures.all);

// Create a simple text document manager
const documents: TextDocuments<TextDocument> = new TextDocuments(TextDocument);

// Initialize reference data manager and snippet manager
let referenceDataManager: ReferenceDataManager;
let snippetManager: SnippetManager;

let hasConfigurationCapability = false;
let hasWorkspaceFolderCapability = false;
let hasDiagnosticRelatedInformationCapability = false;

connection.onInitialize(async (params: InitializeParams) => {
	const capabilities = params.capabilities;

	// Does the client support the `workspace/configuration` request?
	hasConfigurationCapability = !!(
		capabilities.workspace && !!capabilities.workspace.configuration
	);
	hasWorkspaceFolderCapability = !!(
		capabilities.workspace && !!capabilities.workspace.workspaceFolders
	);
	hasDiagnosticRelatedInformationCapability = !!(
		capabilities.textDocument &&
		capabilities.textDocument.publishDiagnostics &&
		capabilities.textDocument.publishDiagnostics.relatedInformation
	);

	// Initialize snippet manager (always available)
	snippetManager = new SnippetManager();

	// Initialize reference data manager
	try {
		const extensionPath = params.rootPath || params.workspaceFolders?.[0]?.uri.replace('file://', '') || process.cwd();
		referenceDataManager = new ReferenceDataManager(extensionPath);

		// Load reference data asynchronously
		await referenceDataManager.loadReferenceData();
		console.log('SLine reference data loaded successfully');
	} catch (error) {
		console.error('Failed to initialize SLine reference data manager:', error);
		console.log('Falling back to basic completion functionality');
		// referenceDataManager remains undefined, triggering fallback logic
	}

	const result: InitializeResult = {
		capabilities: {
			textDocumentSync: TextDocumentSyncKind.Incremental,
			// Tell the client that this server supports code completion.
			completionProvider: {
				resolveProvider: true,
				triggerCharacters: ['|', '#', '{', '.']  // 管道符、井号、大括号、点号触发自动完成
			},
			// Add hover provider
			hoverProvider: true,
			// Add signature help provider
			signatureHelpProvider: {
				triggerCharacters: ['(', ',', ' ']  // 括号、逗号、空格触发参数提示
			}
			// 使用推送模式发送诊断信息，不需要声明 diagnosticProvider
			// 诊断信息通过 connection.sendDiagnostics() 主动发送
		}
	};
	if (hasWorkspaceFolderCapability) {
		result.capabilities.workspace = {
			workspaceFolders: {
				supported: true
			}
		};
	}
	return result;
});

connection.onInitialized(() => {
	if (hasConfigurationCapability) {
		// Register for all configuration changes.
		connection.client.register(DidChangeConfigurationNotification.type, undefined);
	}
	if (hasWorkspaceFolderCapability) {
		connection.workspace.onDidChangeWorkspaceFolders(_event => {
			connection.console.log('Workspace folder change event received.');
		});
	}
});

// The example settings
interface ExampleSettings {
	maxNumberOfProblems: number;
}

// The global settings, used when the `workspace/configuration` request is not supported by the client.
const defaultSettings: ExampleSettings = { maxNumberOfProblems: 1000 };
let globalSettings: ExampleSettings = defaultSettings;

// Cache the settings of all open documents
const documentSettings: Map<string, Thenable<ExampleSettings>> = new Map();

connection.onDidChangeConfiguration(change => {
	if (hasConfigurationCapability) {
		// Reset all cached document settings
		documentSettings.clear();
	} else {
		globalSettings = <ExampleSettings>(
			(change.settings.slineLanguageServer || defaultSettings)
		);
	}
	// Refresh the diagnostics since the `maxNumberOfProblems` could have changed.
	// We could optimize things here and re-fetch the setting first can compare it
	// to the existing setting, but this is out of scope for this example.
	documents.all().forEach(validateTextDocument);
});

function getDocumentSettings(resource: string): Thenable<ExampleSettings> {
	if (!hasConfigurationCapability) {
		return Promise.resolve(globalSettings);
	}
	let result = documentSettings.get(resource);
	if (!result) {
		result = connection.workspace.getConfiguration({
			scopeUri: resource,
			section: 'slineLanguageServer'
		});
		documentSettings.set(resource, result);
	}
	return result;
}

// Only keep settings for open documents
documents.onDidClose(e => {
	documentSettings.delete(e.document.uri);
});



// The content of a text document has changed. This event is emitted
// when the text document first opened or when its content has changed.
documents.onDidChangeContent(change => {
	// 恢复实时语法检测
	validateTextDocument(change.document);
});

// Also provide diagnostics on document open
documents.onDidOpen(change => {
	// 文档打开时进行语法检查
	validateTextDocument(change.document);
});

async function validateTextDocument(textDocument: TextDocument): Promise<void> {
	try {
		const settings = await getDocumentSettings(textDocument.uri);
		// 防护：如果 settings 为 null，使用默认设置
		const maxProblems = settings?.maxNumberOfProblems || defaultSettings.maxNumberOfProblems;

		const text = textDocument.getText();
		const diagnostics: Diagnostic[] = [];

		// 增强的语法验证 - 检查各种语法错误
		const problems: Array<{
			range: any;
			message: string;
			code?: string;
			suggestion?: string;
		}> = findBasicSyntaxErrors(text);

		// 如果有参考数据管理器，添加智能错误检测
		if (referenceDataManager) {
			problems.push(...findSmartSyntaxErrors(text, textDocument));
		}

		let problemCount = 0;
		for (const problem of problems) {
			if (problemCount >= maxProblems) {
				break;
			}

			// 根据错误类型确定严重程度
			let severity: DiagnosticSeverity = DiagnosticSeverity.Warning;
			if (problem.message.includes('Unclosed tag') ||
				problem.message.includes('Unmatched brackets') ||
				problem.message.includes('Unexpected closing tag')) {
				severity = DiagnosticSeverity.Error;
			} else if (problem.message.includes('should be self-closing')) {
				severity = DiagnosticSeverity.Warning;
			} else if (problem.message.includes('Incomplete')) {
				severity = DiagnosticSeverity.Error;
			} else if (problem.message.includes('Unknown') || problem.message.includes('Invalid')) {
				severity = DiagnosticSeverity.Error;
			} else if (problem.message.includes('Deprecated')) {
				severity = DiagnosticSeverity.Warning;
			}

			const diagnostic: Diagnostic = {
				severity: severity,
				range: problem.range,
				message: problem.message,
				source: 'SLine',
				code: problem.code || 'syntax-error'
			};

			if (hasDiagnosticRelatedInformationCapability) {
				diagnostic.relatedInformation = [
					{
						location: {
							uri: textDocument.uri,
							range: problem.range
						},
						message: problem.suggestion || 'SLine 语法问题 - 请检查标签语法是否正确'
					}
				];
			}

			diagnostics.push(diagnostic);
			problemCount++;
		}

		// 发送诊断信息到 VSCode
		connection.sendDiagnostics({ uri: textDocument.uri, diagnostics });

		// 记录诊断信息用于调试
		if (diagnostics.length > 0) {
			connection.console.log(`Found ${diagnostics.length} syntax issues in ${textDocument.uri}`);
		}
	} catch (error) {
		// 如果验证过程中出现错误，记录但不抛出
		connection.console.log('Error in validateTextDocument: ' + error);
		// 发送空的诊断信息，清除之前的错误
		connection.sendDiagnostics({ uri: textDocument.uri, diagnostics: [] });
	}
}

/**
 * 智能语法错误检测 - 使用参考数据进行验证
 */
function findSmartSyntaxErrors(text: string, textDocument: TextDocument): Array<{
	range: any;
	message: string;
	code?: string;
	suggestion?: string;
}> {
	const problems: Array<{
		range: any;
		message: string;
		code?: string;
		suggestion?: string;
	}> = [];

	if (!referenceDataManager) {
		return problems;
	}

	// 检查未知的标签
	const tagRegex = /\{\{#([a-zA-Z_][a-zA-Z0-9_]*)/g;
	let match;

	while ((match = tagRegex.exec(text)) !== null) {
		const tagName = match[1];
		const startPos = match.index;
		const endPos = startPos + match[0].length;

		// 检查标签是否存在
		if (!referenceDataManager.hasTag(tagName)) {
			const startPosition = textDocument.positionAt(startPos);
			const endPosition = textDocument.positionAt(endPos);

			// 获取相似标签建议
			const suggestions = referenceDataManager.getSimilarTags(tagName);
			let message = `Unknown tag '${tagName}'`;
			let suggestion = 'Check the tag name for typos';

			if (suggestions.length > 0) {
				message += `. Did you mean: ${suggestions.join(', ')}?`;
				suggestion = `Consider using: ${suggestions[0]}`;
			}

			problems.push({
				range: {
					start: startPosition,
					end: endPosition
				},
				message: message,
				code: 'unknown-tag',
				suggestion: suggestion
			});
		} else {
			// 检查是否是已弃用的标签
			const hoverInfo = referenceDataManager.getHoverInfo(tagName);
			if (hoverInfo?.deprecated) {
				const startPosition = textDocument.positionAt(startPos);
				const endPosition = textDocument.positionAt(endPos);

				problems.push({
					range: {
						start: startPosition,
						end: endPosition
					},
					message: `Deprecated tag '${tagName}' - consider using an alternative`,
					code: 'deprecated-tag',
					suggestion: 'Check documentation for recommended alternatives'
				});
			}
		}
	}

	// 检查未知的过滤器 - 使用智能管道符检测
	const filterRegex = /\|\s*([a-zA-Z_][a-zA-Z0-9_]*)/g;

	while ((match = filterRegex.exec(text)) !== null) {
		const matchStart = match.index;
		const pipePosition = matchStart;

		// 检查这个管道符是否是逻辑或运算符的一部分
		const prevChar = pipePosition > 0 ? text[pipePosition - 1] : '';
		const nextChar = pipePosition < text.length - 1 ? text[pipePosition + 1] : '';

		// 如果是逻辑或运算符，跳过
		if (prevChar === '|' || nextChar === '|') {
			continue;
		}

		const filterName = match[1];
		const startPos = match.index + match[0].indexOf(filterName);
		const endPos = startPos + filterName.length;

		// 检查过滤器是否存在
		if (!referenceDataManager.hasFilter(filterName)) {
			const startPosition = textDocument.positionAt(startPos);
			const endPosition = textDocument.positionAt(endPos);

			// 获取相似过滤器建议
			const suggestions = referenceDataManager.getSimilarFilters(filterName);
			let message = `Unknown filter '${filterName}'`;
			let suggestion = 'Check the filter name for typos';

			if (suggestions.length > 0) {
				message += `. Did you mean: ${suggestions.join(', ')}?`;
				suggestion = `Consider using: ${suggestions[0]}`;
			}

			problems.push({
				range: {
					start: startPosition,
					end: endPosition
				},
				message: message,
				code: 'unknown-filter',
				suggestion: suggestion
			});
		} else {
			// 检查是否是已弃用的过滤器
			const hoverInfo = referenceDataManager.getHoverInfo(filterName);
			if (hoverInfo?.deprecated) {
				const startPosition = textDocument.positionAt(startPos);
				const endPosition = textDocument.positionAt(endPos);

				problems.push({
					range: {
						start: startPosition,
						end: endPosition
					},
					message: `Deprecated filter '${filterName}' - consider using an alternative`,
					code: 'deprecated-filter',
					suggestion: 'Check documentation for recommended alternatives'
				});
			}
		}
	}

	// 检查对象属性访问 - 暂时禁用，因为对象属性数据还不够完善
	// TODO: 当对象属性数据完善后，重新启用此功能
	/*
	const propertyRegex = /([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)/g;

	while ((match = propertyRegex.exec(text)) !== null) {
		const objectName = match[1];
		const propertyName = match[2];
		const startPos = match.index;
		const endPos = startPos + match[0].length;

		// 检查对象是否存在
		if (referenceDataManager.hasObject(objectName)) {
			// 检查属性是否存在
			const properties = referenceDataManager.getObjectPropertyCompletions(objectName, '');
			const hasProperty = properties.some(prop => prop.label === propertyName);

			if (!hasProperty) {
				const startPosition = textDocument.positionAt(startPos + objectName.length + 1);
				const endPosition = textDocument.positionAt(endPos);

				// 获取可用属性建议
				const availableProps = properties.slice(0, 3).map(prop => prop.label);
				let message = `Unknown property '${propertyName}' on object '${objectName}'`;
				let suggestion = 'Check the property name for typos';

				if (availableProps.length > 0) {
					message += `. Available properties: ${availableProps.join(', ')}`;
					suggestion = `Consider using: ${availableProps[0]}`;
				}

				problems.push({
					range: {
						start: startPosition,
						end: endPosition
					},
					message: message,
					code: 'unknown-property',
					suggestion: suggestion
				});
			}
		}
	}
	*/

	return problems;
}

// 计算两个字符串的编辑距离（Levenshtein距离）
function calculateEditDistance(str1: string, str2: string): number {
	const matrix: number[][] = [];
	const len1 = str1.length;
	const len2 = str2.length;

	// 初始化矩阵
	for (let i = 0; i <= len1; i++) {
		matrix[i] = [i];
	}
	for (let j = 0; j <= len2; j++) {
		matrix[0][j] = j;
	}

	// 计算编辑距离
	for (let i = 1; i <= len1; i++) {
		for (let j = 1; j <= len2; j++) {
			if (str1[i - 1] === str2[j - 1]) {
				matrix[i][j] = matrix[i - 1][j - 1];
			} else {
				matrix[i][j] = Math.min(
					matrix[i - 1][j] + 1,     // 删除
					matrix[i][j - 1] + 1,     // 插入
					matrix[i - 1][j - 1] + 1  // 替换
				);
			}
		}
	}

	return matrix[len1][len2];
}

// 注意：findSimilarTag 和 findSimilarFilter 函数已移除，现在使用 referenceDataManager.getSimilarTags/getSimilarFilters

/**
 * 智能检测自闭合标签错误
 * 算法：程序逻辑自动识别，不依赖硬编码的标签列表
 * 对于任何开始标签，如果没有对应的结束标签，则必须是自闭合的
 */
function checkSelfClosingTags(text: string): Array<{range: any, message: string}> {
	const problems: Array<{range: any, message: string}> = [];
	
	try {
		const lines = text.split('\n');
		
		// 存储所有的开始标签和结束标签信息
		interface TagInfo {
			name: string;
			line: number;
			character: number;
			fullMatch: string;
			isSelfClosing: boolean;
			isClosingTag: boolean;
		}
		
		const allTags: TagInfo[] = [];
		
		// 1. 扫描所有标签
		for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
			const line = lines[lineIndex];
			
			// 查找开始标签 {{#tagname ... }}
			const startTagRegex = /\{\{#([a-zA-Z_][a-zA-Z0-9_]*)\s*[^}]*\}\}/g;
			let startMatch;
			
			while ((startMatch = startTagRegex.exec(line)) !== null) {
				const tagName = startMatch[1];
				const fullMatch = startMatch[0];
				const isSelfClosing = fullMatch.endsWith('/}}');
				
				allTags.push({
					name: tagName,
					line: lineIndex,
					character: startMatch.index,
					fullMatch: fullMatch,
					isSelfClosing: isSelfClosing,
					isClosingTag: false
				});
			}
			
			// 查找结束标签 {{/tagname}}
			const endTagRegex = /\{\{\/([a-zA-Z_][a-zA-Z0-9_]*)\}\}/g;
			let endMatch;
			
			while ((endMatch = endTagRegex.exec(line)) !== null) {
				const tagName = endMatch[1];
				
				allTags.push({
					name: tagName,
					line: lineIndex,
					character: endMatch.index,
					fullMatch: endMatch[0],
					isSelfClosing: false,
					isClosingTag: true
				});
			}
		}
		
		// 2. 使用栈来匹配开始和结束标签
		const tagStack: TagInfo[] = [];
		const unmatchedStartTags: TagInfo[] = [];
		
		for (const tag of allTags) {
			if (tag.isClosingTag) {
				// 寻找对应的开始标签
				let matched = false;
				for (let i = tagStack.length - 1; i >= 0; i--) {
					if (tagStack[i].name === tag.name) {
						// 找到匹配的开始标签，移除它
						tagStack.splice(i, 1);
						matched = true;
						break;
					}
				}
				
				if (!matched) {
					// 没有找到对应的开始标签，这是一个多余的结束标签
					// 这种错误可以在别的地方处理，这里专注于自闭合标签
				}
			} else {
				// 开始标签
				if (tag.isSelfClosing) {
					// 自闭合标签不需要结束标签，跳过
					continue;
				} else {
					// 非自闭合的开始标签，加入栈中等待匹配
					tagStack.push(tag);
				}
			}
		}
		
		// 3. 栈中剩余的都是没有匹配的开始标签
		for (const unmatchedTag of tagStack) {
			problems.push({
				range: {
					start: { line: unmatchedTag.line, character: unmatchedTag.character },
					end: { 
						line: unmatchedTag.line, 
						character: unmatchedTag.character + unmatchedTag.fullMatch.length 
					}
				},
				message: `Tag '${unmatchedTag.name}' is not self-closing and has no matching closing tag. Either add '{{/${unmatchedTag.name}}}' or make it self-closing with '/}}'`
			});
		}
		
	} catch (error) {
		// 安全错误处理，避免语法检测功能影响整体功能
		console.error('Error in checkSelfClosingTags:', error);
	}
	
	return problems;
}

function findBasicSyntaxErrors(text: string): Array<{
	range: any;
	message: string;
	code?: string;
	suggestion?: string;
}> {
	const problems: Array<{
		range: any;
		message: string;
		code?: string;
		suggestion?: string;
	}> = [];
	
	try {
		const lines = text.split('\n');
		let openBrackets = 0;
		
		// 智能自闭合标签检测
		const selfClosingErrors = checkSelfClosingTags(text);
		problems.push(...selfClosingErrors);
		
		// 智能的多行语法检查，支持跨行标签属性
		const unclosedExpressions: {line: number, character: number}[] = [];
		
		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			
			// 1. 跟踪括号匹配，支持多行标签
			const openMatches = line.match(/\{\{/g);
			const closeMatches = line.match(/\}\}/g);
			
			// 记录本行新开启的表达式位置
			if (openMatches) {
				let startIndex = 0;
				for (let j = 0; j < openMatches.length; j++) {
					startIndex = line.indexOf('{{', startIndex);
					if (startIndex >= 0) {
						unclosedExpressions.push({line: i, character: startIndex});
						openBrackets++;
						startIndex += 2; // 移动到下一个可能的位置
					}
				}
			}
			
			// 移除已关闭的表达式
			if (closeMatches) {
				const closeCount = Math.min(closeMatches.length, unclosedExpressions.length);
				for (let j = 0; j < closeCount; j++) {
					unclosedExpressions.pop(); // 移除最近的未关闭表达式
					openBrackets--;
				}
			}

			// 注意：标签和过滤器验证已移至 findSmartSyntaxErrors 中统一处理
		}
		
		// 7. 检查剩余的未关闭表达式（支持多行标签）
		for (const unclosedExpr of unclosedExpressions) {
			problems.push({
				range: {
					start: { line: unclosedExpr.line, character: unclosedExpr.character },
					end: { line: unclosedExpr.line, character: unclosedExpr.character + 2 }
				},
				message: 'Incomplete Sline expression - missing closing }}'
			});
		}
	} catch (error) {
		// 如果有任何错误，记录但不抛出
		connection.console.log('Error in syntax checking: ' + error);
	}
	
	return problems;
}

connection.onDidChangeWatchedFiles(_change => {
	// Monitored files have changed in VSCode
	connection.console.log('We received a file change event');
});

// 注意：SLINE_FILTERS 和 SLINE_TAGS 常量已移除，现在统一使用 referenceDataManager 进行验证

// Sline 关键词和助手（保持向后兼容）
const SLINE_KEYWORDS = [
	'if', 'each', 'unless', 'with', 'layout', 'component', 'content', 'section', 'var',
	'schema', 'else', 'elseif'
];

// 标签分类和描述函数 (已废弃，现在使用 referenceDataManager)
/*
function getTagType(tag: string): { category: string, description: string } {
	// 控制流标签
	if (['if', 'case', 'switch', 'for', 'capture', 'set', 'var'].includes(tag)) {
		return {
			category: '控制流标签',
			description: getControlFlowDescription(tag)
		};
	}
	
	// 布局和组件标签
	if (['layout', 'block', 'blocks', 'component', 'content', 'section', 'sections', 'schema'].includes(tag)) {
		return {
			category: '布局组件标签',
			description: getLayoutDescription(tag)
		};
	}
	
	// 表单标签
	if (tag.includes('form') || tag.includes('customer_') || tag.includes('_form')) {
		return {
			category: '表单标签',
			description: getFormDescription(tag)
		};
	}
	
	// 媒体和资源标签
	if (['image_tag', 'video_tag', 'external_video_tag', 'style', 'stylesheet', 'script', 'preload_tag', 'time_tag', 'placeholder_svg', 'payment_type_svg'].includes(tag)) {
		return {
			category: '媒体资源标签',
			description: getMediaDescription(tag)
		};
	}
	
	// 功能性标签
	if (tag.includes('link_to') || ['payment_button', 'format_address', 'highlight', 'metafield_tag'].includes(tag)) {
		return {
			category: '功能标签',
			description: getFunctionDescription(tag)
		};
	}
	
	// 默认分类
	return {
		category: 'Sline 标签',
		description: `${tag} - Sline 内置标签`
	};
}
*/

/*
function getControlFlowDescription(tag: string): string {
	const descriptions: { [key: string]: string } = {
		'if': '条件判断: {{#if condition}}...{{/if}}',
		'case': '多分支选择: {{#case variable}}{{#when value}}...{{/when}}{{/case}}',
		'switch': '开关控制: {{#switch variable}}{{#case value}}...{{/case}}{{/switch}}',
		'for': '循环遍历: {{#for item in items}}...{{/for}}',
		'capture': '捕获输出: {{#capture variable}}...{{/capture}}',
		'set': '设置变量: {{#set name = value}}',
		'var': '声明变量: {{#var name="value"}}'
	};
	return descriptions[tag] || `${tag} - 控制流标签`;
}

function getLayoutDescription(tag: string): string {
	const descriptions: { [key: string]: string } = {
		'layout': '页面布局: {{#layout "template-name"}}',
		'block': '内容块: {{#block "block-name"}}...{{/block}}',
		'blocks': '多个内容块: {{#blocks}}...{{/blocks}}',
		'component': '组件引用: {{#component "component-name"}}',
		'content': '内容区域: {{#content "section-name"}}',
		'section': '页面区块: {{#section "section-name"}}...{{/section}}',
		'sections': '多个区块: {{#sections}}...{{/sections}}',
		'schema': '数据模式: {{#schema}}...{{/schema}}'
	};
	return descriptions[tag] || `${tag} - 布局组件标签`;
}

function getFormDescription(tag: string): string {
	const descriptions: { [key: string]: string } = {
		'customer_form': '客户表单: {{#customer_form}}...{{/customer_form}}',
		'customer_login_form': '登录表单: {{#customer_login_form}}...{{/customer_login_form}}',
		'customer_address_form': '地址表单: {{#customer_address_form}}...{{/customer_address_form}}',
		'create_customer_form': '注册表单: {{#create_customer_form}}...{{/create_customer_form}}',
		'cart_form': '购物车表单: {{#cart_form}}...{{/cart_form}}',
		'contact_form': '联系表单: {{#contact_form}}...{{/contact_form}}',
		'new_comment_form': '评论表单: {{#new_comment_form}}...{{/new_comment_form}}'
	};
	return descriptions[tag] || `${tag} - 表单处理标签`;
}

function getMediaDescription(tag: string): string {
	const descriptions: { [key: string]: string } = {
		'image_tag': '图片标签: {{#image_tag src="..." alt="..."}}',
		'video_tag': '视频标签: {{#video_tag src="..."}}',
		'external_video_tag': '外部视频: {{#external_video_tag url="..."}}',
		'style': '样式标签: {{#style}}CSS内容{{/style}}',
		'stylesheet': '样式表: {{#stylesheet "style.css"}}',
		'script': '脚本标签: {{#script "script.js"}}',
		'preload_tag': '预加载: {{#preload_tag "resource.css"}}',
		'time_tag': '时间标签: {{#time_tag datetime="..."}}',
		'placeholder_svg': 'SVG占位符: {{#placeholder_svg width="..." height="..."}}',
		'payment_type_svg': '支付图标: {{#payment_type_svg type="..."}}'
	};
	return descriptions[tag] || `${tag} - 媒体资源标签`;
}

function getFunctionDescription(tag: string): string {
	const descriptions: { [key: string]: string } = {
		'link_to': '生成链接: {{#link_to "url"}}文本{{/link_to}}',
		'link_to_customer_login': '登录链接: {{#link_to_customer_login}}登录{{/link_to_customer_login}}',
		'link_to_customer_logout': '退出链接: {{#link_to_customer_logout}}退出{{/link_to_customer_logout}}',
		'link_to_customer_register': '注册链接: {{#link_to_customer_register}}注册{{/link_to_customer_register}}',
		'payment_button': '支付按钮: {{#payment_button}}立即支付{{/payment_button}}',
		'format_address': '地址格式化: {{#format_address address}}',
		'highlight': '高亮显示: {{#highlight query in text}}',
		'metafield_tag': '元字段: {{#metafield_tag namespace.key}}'
	};
	return descriptions[tag] || `${tag} - 功能性标签`;
}
*/

/**
 * 基础完成建议（回退逻辑）
 */
function getBasicCompletions(document: TextDocument, textDocumentPosition: TextDocumentPositionParams): CompletionItem[] {
	const text = document.getText();
	const offset = document.offsetAt(textDocumentPosition.position);

	// 获取光标周围的文本，不仅限于当前行
	const beforeCursor = text.substring(0, offset);
	const afterCursor = text.substring(offset);

	// 检查是否在 {{ }} 内部 (支持多行表达式)
	const lastOpenBrace = beforeCursor.lastIndexOf('{{');
	const lastCloseBrace = beforeCursor.lastIndexOf('}}');
	const nextCloseBrace = afterCursor.indexOf('}}');

	const insideBraces = lastOpenBrace !== -1 &&
		(lastCloseBrace === -1 || lastOpenBrace > lastCloseBrace) &&
		nextCloseBrace !== -1;

	if (!insideBraces) {
		return [];
	}

	// 获取 {{ 后的内容
	const expressionContent = beforeCursor.substring(lastOpenBrace + 2);

	// 检查是否在管道符后面 (应该提示过滤器)
	const pipeIndex = (ContextAnalyzer as any).findLastPipeIndex(expressionContent);
	if (pipeIndex !== -1) {
		// 在管道符后面，提供过滤器建议
		const afterPipe = expressionContent.substring(pipeIndex + 1).trim();

		// 使用 referenceDataManager 获取过滤器建议
		if (referenceDataManager) {
			return referenceDataManager.getFilterCompletions(afterPipe);
		}

		// 如果没有 referenceDataManager，返回空数组
		return [];
	}

	// 在表达式开始位置，提供标签建议
	const trimmedExpression = expressionContent.trim();

	// 如果以 # 开头，提供标签建议
	if (trimmedExpression.startsWith('#')) {
		const searchTerm = trimmedExpression.replace('#', '').toLowerCase();

		// 使用 referenceDataManager 获取标签建议
		if (referenceDataManager) {
			return referenceDataManager.getTagCompletions(searchTerm);
		}

		// 如果没有 referenceDataManager，返回空数组
		return [];
	}

	// 如果表达式为空，提供常用关键词建议
	if (trimmedExpression === '') {
		const matchingKeywords = SLINE_KEYWORDS.map((keyword, index) => {
			const isHelper = ['layout', 'component', 'content', 'section', 'var', 'schema'].includes(keyword);
			return {
				label: keyword,
				kind: isHelper ? CompletionItemKind.Function : CompletionItemKind.Keyword,
				data: SLINE_KEYWORDS.indexOf(keyword) + 1,
				insertText: keyword,
				detail: `Sline ${isHelper ? '助手' : '关键词'}`,
				sortText: `2${index.toString().padStart(3, '0')}`
			};
		});

		return matchingKeywords;
	}

	return [];
}

// This handler provides the initial list of the completion items.
connection.onCompletion(
	(textDocumentPosition: TextDocumentPositionParams): CompletionItem[] => {
		// 获取文档和当前位置
		const document = documents.get(textDocumentPosition.textDocument.uri);
		if (!document || !referenceDataManager) {
			// 如果没有参考数据管理器，回退到原有逻辑
			if (!document) {
				return [];
			}
			return getBasicCompletions(document, textDocumentPosition);
		}

		// 使用新的上下文分析器
		const context = ContextAnalyzer.analyzeCompletionContext(document, textDocumentPosition.position);

		// 根据上下文类型提供相应的完成建议
		switch (context.type) {
			case 'tag':
				return referenceDataManager.getTagCompletions(context.prefix);

			case 'filter':
				return referenceDataManager.getFilterCompletions(context.prefix);

			case 'object_property':
				if (context.objectName) {
					return referenceDataManager.getObjectPropertyCompletions(context.objectName, context.prefix);
				}
				// 如果没有对象名，提供所有对象的建议
				return referenceDataManager.getAllObjectNames()
					.filter(name => name.toLowerCase().startsWith(context.prefix.toLowerCase()))
					.map(name => ({
						label: name,
						kind: CompletionItemKind.Class,
						detail: `Sline Object`,
						insertText: name,
						data: { type: 'object', name }
					}));

			case 'tag_parameter':
				if (context.tagName) {
					return referenceDataManager.getTagParameterCompletions(context.tagName, context.prefix);
				}
				break;

			case 'filter_parameter':
				if (context.filterName) {
					return referenceDataManager.getFilterParameterCompletions(context.filterName, context.prefix);
				}
				break;

			default:
				// 提供通用建议 - 标签、对象和代码片段
				const suggestions: CompletionItem[] = [];

				// 添加代码片段建议（优先级最高）
				if (snippetManager) {
					suggestions.push(...snippetManager.getSnippetCompletions(context.prefix).slice(0, 5));
				}

				// 添加标签建议
				suggestions.push(...referenceDataManager.getTagCompletions('').slice(0, 10));

				// 添加常用对象建议
				const commonObjects = ['product', 'cart', 'customer', 'shop', 'blog', 'article'];
				for (const objName of commonObjects) {
					if (referenceDataManager.hasObject(objName)) {
						suggestions.push({
							label: objName,
							kind: CompletionItemKind.Class,
							detail: `Sline Object`,
							insertText: objName,
							data: { type: 'object', name: objName }
						});
					}
				}

				return suggestions;
		}

		return [];
	}
);

// Hover provider for rich documentation
connection.onHover((params): Hover | null => {
	const document = documents.get(params.textDocument.uri);
	if (!document || !referenceDataManager) {
		return null;
	}

	// Check if we're in a Sline expression
	if (!ContextAnalyzer.isInSlineExpression(document, params.position)) {
		return null;
	}

	// Get the word at the current position
	const wordRange = ContextAnalyzer.getWordRangeAtPosition(document, params.position);
	const word = document.getText(wordRange);

	if (!word) {
		return null;
	}

	// Analyze context to provide better hover information
	const context = ContextAnalyzer.analyzeCompletionContext(document, params.position);

	// Get hover information from reference data
	const hoverInfo = referenceDataManager.getHoverInfo(word, context);

	if (hoverInfo) {
		let content = `# ${hoverInfo.name}\n\n${hoverInfo.summary}\n\n`;

		if (hoverInfo.syntax) {
			content += `## Syntax\n\`\`\`sline\n${hoverInfo.syntax}\n\`\`\`\n\n`;
		}

		if (hoverInfo.parameters && hoverInfo.parameters.length > 0) {
			content += `## Parameters\n`;
			for (const param of hoverInfo.parameters) {
				content += `- **${param.name}** (${param.types.join(' | ')}): ${param.description}\n`;
			}
			content += '\n';
		}

		if (hoverInfo.returnType) {
			content += `## Returns\n**${hoverInfo.returnType}**\n\n`;
		}

		if (hoverInfo.examples && hoverInfo.examples.length > 0) {
			content += `## Example\n\`\`\`sline\n${hoverInfo.examples[0].raw_sline}\n\`\`\`\n\n`;
		}

		if (hoverInfo.deprecated) {
			content = `⚠️ **Deprecated** ⚠️\n\n${content}`;
		}

		content += `[📖 Documentation](${hoverInfo.link})`;

		return {
			contents: {
				kind: MarkupKind.Markdown,
				value: content
			},
			range: wordRange
		};
	}

	return null;
});

// Signature help provider for parameter hints
connection.onSignatureHelp((params): SignatureHelp | null => {
	const document = documents.get(params.textDocument.uri);
	if (!document || !referenceDataManager) {
		return null;
	}

	// Check if we're in a Sline expression
	if (!ContextAnalyzer.isInSlineExpression(document, params.position)) {
		return null;
	}

	// Analyze context to determine if we're in a function call
	const context = ContextAnalyzer.analyzeCompletionContext(document, params.position);

	let signatures: SignatureInformation[] = [];
	let activeSignature = 0;
	let activeParameter = 0;

	if (context.type === 'tag_parameter' && context.tagName) {
		// Get tag information
		const hoverInfo = referenceDataManager.getHoverInfo(context.tagName);
		if (hoverInfo && hoverInfo.parameters) {
			const parameters: ParameterInformation[] = hoverInfo.parameters.map(param => ({
				label: `${param.name}: ${param.types.join(' | ')}`,
				documentation: param.description
			}));

			signatures.push({
				label: `${context.tagName}(${hoverInfo.parameters.map(p => p.name).join(', ')})`,
				documentation: hoverInfo.summary,
				parameters: parameters
			});

			// Try to determine active parameter based on cursor position
			const expression = ContextAnalyzer.getCurrentExpression(document, params.position);
			if (expression) {
				const tagMatch = expression.match(new RegExp(`#${context.tagName}\\s+(.*)$`));
				if (tagMatch) {
					const paramText = tagMatch[1];
					// Count spaces/commas to estimate parameter position
					const paramCount = (paramText.match(/\s+/g) || []).length;
					activeParameter = Math.min(paramCount, parameters.length - 1);
				}
			}
		}
	} else if (context.type === 'filter_parameter' && context.filterName) {
		// Get filter information
		const hoverInfo = referenceDataManager.getHoverInfo(context.filterName);
		if (hoverInfo && hoverInfo.parameters) {
			const parameters: ParameterInformation[] = hoverInfo.parameters.map(param => ({
				label: `${param.name}: ${param.types.join(' | ')}`,
				documentation: param.description
			}));

			signatures.push({
				label: `${context.filterName}(${hoverInfo.parameters.map(p => p.name).join(', ')})`,
				documentation: hoverInfo.summary,
				parameters: parameters
			});

			// Try to determine active parameter based on cursor position
			const expression = ContextAnalyzer.getCurrentExpression(document, params.position);
			if (expression) {
				const filterMatch = expression.match(new RegExp(`\\|\\s*${context.filterName}\\s*\\(([^)]*)$`));
				if (filterMatch) {
					const paramText = filterMatch[1];
					// Count commas to estimate parameter position
					const paramCount = (paramText.match(/,/g) || []).length;
					activeParameter = Math.min(paramCount, parameters.length - 1);
				}
			}
		}
	}

	if (signatures.length > 0) {
		return {
			signatures: signatures,
			activeSignature: activeSignature,
			activeParameter: activeParameter
		};
	}

	return null;
});

// 注意：FILTER_DESCRIPTIONS 常量已移除，现在使用 referenceDataManager 提供过滤器信息
/*
const FILTER_DESCRIPTIONS: { [key: string]: { detail: string, documentation: string } } = {
	'abs': { detail: '绝对值过滤器', documentation: '返回数字的绝对值: {{ -5 | abs() }} → 5' },
	'append': { detail: '追加过滤器', documentation: '在字符串末尾追加内容: {{ "hello" | append(" world") }} → "hello world"' },
	'asset_url': { detail: '资源URL过滤器', documentation: '生成资源文件的URL: {{ "style.css" | asset_url() }}' },
	'capitalize': { detail: '首字母大写过滤器', documentation: '首字母大写: {{ "hello" | capitalize() }} → "Hello"' },
	'date': { detail: '日期格式化过滤器', documentation: '格式化日期: {{ created_at | date("%Y-%m-%d") }}' },
	'default': { detail: '默认值过滤器', documentation: '提供默认值: {{ variable | default("默认值") }}' },
	'downcase': { detail: '小写过滤器', documentation: '转换为小写: {{ "HELLO" | downcase() }} → "hello"' },
	'upcase': { detail: '大写过滤器', documentation: '转换为大写: {{ "hello" | upcase() }} → "HELLO"' },
	'escape': { detail: 'HTML转义过滤器', documentation: '转义HTML字符: {{ "<script>" | escape() }}' },
	'first': { detail: '首元素过滤器', documentation: '获取数组第一个元素: {{ array | first() }}' },
	'last': { detail: '末元素过滤器', documentation: '获取数组最后一个元素: {{ array | last() }}' },
	'join': { detail: '连接过滤器', documentation: '用分隔符连接数组: {{ array | join(", ") }}' },
	'map': { detail: '映射过滤器', documentation: '提取数组中对象的属性: {{ products | map("title") }}' },
	'size': { detail: '大小过滤器', documentation: '获取数组或字符串长度: {{ array | size() }}' },
	'sort': { detail: '排序过滤器', documentation: '对数组排序: {{ array | sort("name") }}' },
	'reverse': { detail: '反转过滤器', documentation: '反转数组顺序: {{ array | reverse() }}' },
	'slice': { detail: '切片过滤器', documentation: '截取数组或字符串: {{ string | slice(0, 5) }}' },
	'split': { detail: '分割过滤器', documentation: '分割字符串为数组: {{ "a,b,c" | split(",") }}' },
	'strip_html': { detail: '去除HTML过滤器', documentation: '移除HTML标签: {{ "<p>text</p>" | strip_html() }} → "text"' },
	'truncate': { detail: '截断过滤器', documentation: '截断字符串: {{ string | truncate(50) }}' },
	'where': { detail: '筛选过滤器', documentation: '筛选数组: {{ products | where("available", true) }}' }
};
*/

// This handler resolves additional information for the item selected in
// the completion list.
connection.onCompletionResolve(
	(item: CompletionItem): CompletionItem => {
		// 现在标签和过滤器的完成项都来自 referenceDataManager，已经包含完整信息
		// 只需要处理关键词的情况

		// 处理关键词
		switch (item.data) {
			case 1:
				item.detail = 'if 条件块';
				item.documentation = '条件判断块: {{#if condition}}...{{/if}}';
				break;
			case 2:
				item.detail = 'each 循环块';
				item.documentation = '遍历数组: {{#each items}}...{{/each}}';
				break;
			case 3:
				item.detail = 'unless 反向条件块';
				item.documentation = '反向条件判断: {{#unless condition}}...{{/unless}}';
				break;
			case 4:
				item.detail = 'with 上下文块';
				item.documentation = '上下文切换: {{#with object}}...{{/with}}';
				break;
			case 5:
				item.detail = 'layout 布局助手';
				item.documentation = '布局组件: {{#layout "name" /}}';
				break;
			case 6:
				item.detail = 'component 组件助手';
				item.documentation = '组件引用: {{#component "name" /}}';
				break;
			case 7:
				item.detail = 'content 内容助手';
				item.documentation = '内容区域: {{#content "name" /}}';
				break;
			case 8:
				item.detail = 'section 区块助手';
				item.documentation = '区块定义: {{#section "name"}}...{{/section}}';
				break;
			case 9:
				item.detail = 'var 变量助手';
				item.documentation = '变量定义: {{#var name="value"}}';
				break;
			case 10:
				item.detail = 'schema 模式助手';
				item.documentation = '数据模式: {{#schema}}...{{/schema}}';
				break;
			case 11:
				item.detail = 'else 否则块';
				item.documentation = 'else 分支: {{#if}}...{{else}}...{{/if}}';
				break;
			case 12:
				item.detail = 'elseif 否则如果块';
				item.documentation = 'elseif 分支: {{#if}}...{{elseif condition}}...{{/if}}';
				break;
		}
		return item;
	}
);

// Make the text document manager listen on the connection
// for open, change and close text document events
documents.listen(connection);

// Listen on the connection
connection.listen();
