// SLine 代码片段管理器
import { CompletionItem, CompletionItemKind, InsertTextFormat } from 'vscode-languageserver/node';

/**
 * SLine 代码片段定义
 */
export interface SlineSnippet {
    name: string;
    prefix: string;
    body: string[];
    description: string;
    description_cn: string;
    category: 'control' | 'form' | 'layout' | 'data' | 'utility';
}

/**
 * SLine 代码片段管理器
 */
export class SnippetManager {
    private snippets: SlineSnippet[] = [
        // 控制流片段
        {
            name: 'for-loop',
            prefix: 'for',
            body: [
                '{{#for ${1:item} in ${2:collection}}}',
                '  ${3:<!-- content -->}',
                '{{/for}}'
            ],
            description: 'For loop to iterate over a collection',
            description_cn: '遍历集合的 for 循环',
            category: 'control'
        },
        {
            name: 'if-condition',
            prefix: 'if',
            body: [
                '{{#if ${1:condition}}}',
                '  ${2:<!-- content -->}',
                '{{/if}}'
            ],
            description: 'Conditional statement',
            description_cn: '条件语句',
            category: 'control'
        },
        {
            name: 'if-else',
            prefix: 'ifelse',
            body: [
                '{{#if ${1:condition}}}',
                '  ${2:<!-- if content -->}',
                '{{#else}}',
                '  ${3:<!-- else content -->}',
                '{{/if}}'
            ],
            description: 'If-else conditional statement',
            description_cn: 'If-else 条件语句',
            category: 'control'
        },
        {
            name: 'unless-condition',
            prefix: 'unless',
            body: [
                '{{#unless ${1:condition}}}',
                '  ${2:<!-- content -->}',
                '{{/unless}}'
            ],
            description: 'Unless conditional statement',
            description_cn: 'Unless 条件语句',
            category: 'control'
        },

        // 表单片段
        {
            name: 'customer-login-form',
            prefix: 'loginform',
            body: [
                '{{#customer_login_form}}',
                '  <div class="form-group">',
                '    <label for="customer-email">邮箱</label>',
                '    <input type="email" id="customer-email" name="customer[email]" required>',
                '  </div>',
                '  <div class="form-group">',
                '    <label for="customer-password">密码</label>',
                '    <input type="password" id="customer-password" name="customer[password]" required>',
                '  </div>',
                '  <button type="submit">登录</button>',
                '{{/customer_login_form}}'
            ],
            description: 'Customer login form template',
            description_cn: '客户登录表单模板',
            category: 'form'
        },
        {
            name: 'customer-register-form',
            prefix: 'registerform',
            body: [
                '{{#customer_register_form}}',
                '  <div class="form-group">',
                '    <label for="first-name">名字</label>',
                '    <input type="text" id="first-name" name="customer[first_name]" required>',
                '  </div>',
                '  <div class="form-group">',
                '    <label for="last-name">姓氏</label>',
                '    <input type="text" id="last-name" name="customer[last_name]" required>',
                '  </div>',
                '  <div class="form-group">',
                '    <label for="email">邮箱</label>',
                '    <input type="email" id="email" name="customer[email]" required>',
                '  </div>',
                '  <div class="form-group">',
                '    <label for="password">密码</label>',
                '    <input type="password" id="password" name="customer[password]" required>',
                '  </div>',
                '  <button type="submit">注册</button>',
                '{{/customer_register_form}}'
            ],
            description: 'Customer registration form template',
            description_cn: '客户注册表单模板',
            category: 'form'
        },
        {
            name: 'cart-form',
            prefix: 'cartform',
            body: [
                '{{#cart_form}}',
                '  {{#for item in cart.items}}',
                '    <div class="cart-item">',
                '      <h3>{{ item.product.title }}</h3>',
                '      <p>价格: {{ item.price | money() }}</p>',
                '      <input type="number" name="updates[{{ item.key }}]" value="{{ item.quantity }}" min="0">',
                '    </div>',
                '  {{/for}}',
                '  <button type="submit">更新购物车</button>',
                '{{/cart_form}}'
            ],
            description: 'Shopping cart form template',
            description_cn: '购物车表单模板',
            category: 'form'
        },

        // 布局片段
        {
            name: 'layout-basic',
            prefix: 'layout',
            body: [
                '<!DOCTYPE html>',
                '<html lang="zh-CN">',
                '<head>',
                '  <meta charset="UTF-8">',
                '  <meta name="viewport" content="width=device-width, initial-scale=1.0">',
                '  <title>{{ page_title | default(shop.name) }}</title>',
                '  {{ content_for_header }}',
                '</head>',
                '<body>',
                '  <header>',
                '    <h1>{{ shop.name }}</h1>',
                '  </header>',
                '  <main>',
                '    {{ content_for_layout }}',
                '  </main>',
                '  <footer>',
                '    <p>&copy; {{ "now" | date("%Y") }} {{ shop.name }}</p>',
                '  </footer>',
                '</body>',
                '</html>'
            ],
            description: 'Basic layout template',
            description_cn: '基础布局模板',
            category: 'layout'
        },

        // 数据展示片段
        {
            name: 'product-card',
            prefix: 'productcard',
            body: [
                '<div class="product-card">',
                '  {{#if product.featured_image}}',
                '    <img src="{{ product.featured_image | image_url(width: 300) }}" alt="{{ product.title }}">',
                '  {{/if}}',
                '  <h3>{{ product.title }}</h3>',
                '  <p class="price">{{ product.price | money() }}</p>',
                '  {{#if product.compare_at_price}}',
                '    <p class="compare-price">{{ product.compare_at_price | money() }}</p>',
                '  {{/if}}',
                '  <a href="{{ product.url }}">查看详情</a>',
                '</div>'
            ],
            description: 'Product card display template',
            description_cn: '产品卡片展示模板',
            category: 'data'
        },
        {
            name: 'article-preview',
            prefix: 'articlepreview',
            body: [
                '<article class="article-preview">',
                '  {{#if article.image}}',
                '    <img src="{{ article.image | image_url(width: 400) }}" alt="{{ article.title }}">',
                '  {{/if}}',
                '  <h2><a href="{{ article.url }}">{{ article.title }}</a></h2>',
                '  <p class="meta">',
                '    <time datetime="{{ article.published_at | date("%Y-%m-%d") }}">',
                '      {{ article.published_at | date("%Y年%m月%d日") }}',
                '    </time>',
                '    {{#if article.author}}',
                '      by {{ article.author }}',
                '    {{/if}}',
                '  </p>',
                '  <div class="excerpt">',
                '    {{ article.excerpt | default(article.content | strip_html | truncate(150)) }}',
                '  </div>',
                '  <a href="{{ article.url }}">阅读更多</a>',
                '</article>'
            ],
            description: 'Article preview template',
            description_cn: '文章预览模板',
            category: 'data'
        },

        // 实用工具片段
        {
            name: 'pagination',
            prefix: 'pagination',
            body: [
                '{{#if paginate.pages > 1}}',
                '  <nav class="pagination">',
                '    {{#if paginate.previous}}',
                '      <a href="{{ paginate.previous.url }}" class="prev">上一页</a>',
                '    {{/if}}',
                '    {{#for part in paginate.parts}}',
                '      {{#if part.is_link}}',
                '        <a href="{{ part.url }}">{{ part.title }}</a>',
                '      {{#else}}',
                '        <span class="current">{{ part.title }}</span>',
                '      {{/if}}',
                '    {{/for}}',
                '    {{#if paginate.next}}',
                '      <a href="{{ paginate.next.url }}" class="next">下一页</a>',
                '    {{/if}}',
                '  </nav>',
                '{{/if}}'
            ],
            description: 'Pagination navigation template',
            description_cn: '分页导航模板',
            category: 'utility'
        },
        {
            name: 'breadcrumb',
            prefix: 'breadcrumb',
            body: [
                '<nav class="breadcrumb">',
                '  <a href="/">首页</a>',
                '  {{#if collection}}',
                '    <span class="separator">/</span>',
                '    <a href="{{ collection.url }}">{{ collection.title }}</a>',
                '  {{/if}}',
                '  {{#if product}}',
                '    <span class="separator">/</span>',
                '    <span class="current">{{ product.title }}</span>',
                '  {{/if}}',
                '  {{#if page}}',
                '    <span class="separator">/</span>',
                '    <span class="current">{{ page.title }}</span>',
                '  {{/if}}',
                '</nav>'
            ],
            description: 'Breadcrumb navigation template',
            description_cn: '面包屑导航模板',
            category: 'utility'
        }
    ];

    /**
     * 获取所有代码片段的完成项
     */
    getSnippetCompletions(prefix: string = ''): CompletionItem[] {
        return this.snippets
            .filter(snippet => snippet.prefix.toLowerCase().startsWith(prefix.toLowerCase()))
            .map(snippet => this.createSnippetCompletionItem(snippet));
    }

    /**
     * 根据类别获取代码片段
     */
    getSnippetsByCategory(category: SlineSnippet['category']): CompletionItem[] {
        return this.snippets
            .filter(snippet => snippet.category === category)
            .map(snippet => this.createSnippetCompletionItem(snippet));
    }

    /**
     * 创建代码片段完成项
     */
    private createSnippetCompletionItem(snippet: SlineSnippet): CompletionItem {
        return {
            label: snippet.prefix,
            kind: CompletionItemKind.Snippet,
            detail: `Snippet - ${snippet.description}`,
            documentation: {
                kind: 'markdown',
                value: `**${snippet.name}**\n\n${snippet.description_cn}\n\n\`\`\`sline\n${snippet.body.join('\n')}\n\`\`\``
            },
            insertText: snippet.body.join('\n'),
            insertTextFormat: InsertTextFormat.Snippet,
            data: { type: 'snippet', name: snippet.name },
            sortText: `snippet_${snippet.prefix}`,
            filterText: snippet.prefix
        };
    }

    /**
     * 获取特定名称的代码片段
     */
    getSnippetByName(name: string): SlineSnippet | undefined {
        return this.snippets.find(snippet => snippet.name === name);
    }

    /**
     * 获取所有代码片段前缀
     */
    getAllPrefixes(): string[] {
        return this.snippets.map(snippet => snippet.prefix);
    }
}
