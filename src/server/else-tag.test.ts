/**
 * 测试 else 和 elseif 标签的验证，以及动态对象属性验证
 */

import { describe, test, expect } from '@jest/globals';

describe('Else Tag Validation', () => {
    test('should recognize else as a valid tag', () => {
        // 模拟 SLINE_TAGS 数组
        const SLINE_TAGS = [
            'if', 'else', 'elseif', 'for', 'var', 'set', 'capture'
        ];

        // 测试 else 标签是否被识别
        expect(SLINE_TAGS.includes('else')).toBe(true);
        expect(SLINE_TAGS.includes('elseif')).toBe(true);
    });

    test('should validate else tag syntax in if blocks', () => {
        const validSyntax = [
            '{{#if condition}}...{{#else}}...{{/if}}',
            '{{#if condition}}...{{#elseif condition2}}...{{#else}}...{{/if}}',
            '{{#if condition}}...{{#else /}}...{{/if}}'
        ];

        // 这些语法应该被认为是有效的
        validSyntax.forEach(syntax => {
            const hasElse = syntax.includes('{{#else');
            expect(hasElse).toBe(true);
        });
    });

    test('should extract tag names correctly', () => {
        const testCases = [
            { input: '{{#else}}', expected: 'else' },
            { input: '{{#elseif condition}}', expected: 'elseif' },
            { input: '{{#else /}}', expected: 'else' }
        ];

        testCases.forEach(({ input, expected }) => {
            const match = input.match(/\{\{#([a-zA-Z_][a-zA-Z0-9_]*)/);
            expect(match).not.toBeNull();
            expect(match![1]).toBe(expected);
        });
    });
});

describe('Dynamic Object Property Validation', () => {
    test('should identify dynamic objects that should not be validated', () => {
        const dynamicObjects = new Set(['props', 'settings', 'block', 'section', 'forloop', 'forblock', 'this']);

        // 测试动态对象是否在白名单中
        expect(dynamicObjects.has('props')).toBe(true);
        expect(dynamicObjects.has('settings')).toBe(true);
        expect(dynamicObjects.has('block')).toBe(true);
        expect(dynamicObjects.has('section')).toBe(true);

        // 测试非动态对象不在白名单中
        expect(dynamicObjects.has('product')).toBe(false);
        expect(dynamicObjects.has('customer')).toBe(false);
    });

    test('should extract object and property names from property access', () => {
        const testCases = [
            { input: 'props.type', expectedObject: 'props', expectedProperty: 'type' },
            { input: 'settings.title', expectedObject: 'settings', expectedProperty: 'title' },
            { input: 'product.price', expectedObject: 'product', expectedProperty: 'price' },
            { input: 'block.shopline_attributes', expectedObject: 'block', expectedProperty: 'shopline_attributes' }
        ];

        const propertyRegex = /([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)/g;

        testCases.forEach(({ input, expectedObject, expectedProperty }) => {
            const match = propertyRegex.exec(input);
            expect(match).not.toBeNull();
            expect(match![1]).toBe(expectedObject);
            expect(match![2]).toBe(expectedProperty);

            // 重置正则表达式的 lastIndex
            propertyRegex.lastIndex = 0;
        });
    });
});
