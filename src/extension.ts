// src/extension.ts
// SLine VSCode 插件主入口文件

import * as vscode from 'vscode';
import * as client from './client';

/**
 * 插件激活时调用的函数
 * @param context - VSCode 扩展上下文
 */
export function activate(context: vscode.ExtensionContext) {
    console.log('SLine Highlight extension is now active!');

    // 启动语言服务器客户端
    client.activate(context);

    // 注册命令
    let disposable = vscode.commands.registerCommand('sline-highlight.helloWorld', () => {
        vscode.window.showInformationMessage('Hello from SLine Highlight with LSP!');
    });

    // 注册重启语言服务器命令
    let restartServerCommand = vscode.commands.registerCommand('sline-highlight.restartServer', () => {
        vscode.window.showInformationMessage('Restarting SLine Language Server...');
        client.deactivate()?.then(() => {
            client.activate(context);
        });
    });

    // 注册手动切换语言模式命令
    let setLanguageModeCommand = vscode.commands.registerCommand('sline-highlight.setLanguageMode', () => {
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            vscode.languages.setTextDocumentLanguage(activeEditor.document, 'sline');
            vscode.window.showInformationMessage('已切换到 SLine 语言模式');
        } else {
            vscode.window.showWarningMessage('没有活动的编辑器');
        }
    });

    // 智能检测 HTML 文件中的 SLine 语法
    setupSlineDetection(context);

    context.subscriptions.push(disposable, restartServerCommand, setLanguageModeCommand);
}

/**
 * 设置 SLine 语法智能检测
 */
function setupSlineDetection(context: vscode.ExtensionContext) {
    // 检测当前打开的文档
    if (vscode.window.activeTextEditor) {
        checkAndSuggestSlineMode(vscode.window.activeTextEditor.document);
    }

    // 监听文档打开事件
    const onDidOpenTextDocument = vscode.workspace.onDidOpenTextDocument((document) => {
        checkAndSuggestSlineMode(document);
    });

    // 监听活动编辑器变化事件
    const onDidChangeActiveTextEditor = vscode.window.onDidChangeActiveTextEditor((editor) => {
        if (editor) {
            checkAndSuggestSlineMode(editor.document);
        }
    });

    // 监听文档内容变化事件（用于实时检测）
    const onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument((event) => {
        // 只在文档有实质性变化时检测，避免频繁检测
        if (event.contentChanges.length > 0) {
            checkAndSuggestSlineMode(event.document);
        }
    });

    context.subscriptions.push(onDidOpenTextDocument, onDidChangeActiveTextEditor, onDidChangeTextDocument);
}

/**
 * 检测文档是否包含 SLine 语法，并建议切换语言模式
 */
function checkAndSuggestSlineMode(document: vscode.TextDocument) {
    // 只处理 HTML 文件
    if (document.languageId !== 'html') {
        return;
    }

    // 检查文件名是否包含 SLine 相关关键词
    const fileName = document.fileName.toLowerCase();
    const slineKeywords = ['sline', 'shopline', 'template', 'liquid'];
    const hasKeywordInName = slineKeywords.some(keyword => fileName.includes(keyword));

    // 检测 SLine 语法模式
    const text = document.getText();
    const hasSlineSyntax = detectSlineSyntax(text);

    // 如果检测到 SLine 语法或文件名包含相关关键词，建议切换
    if (hasSlineSyntax || hasKeywordInName) {
        suggestSlineMode(document, hasSlineSyntax, hasKeywordInName);
    }
}

/**
 * 检测文本中是否包含 SLine 语法
 */
function detectSlineSyntax(text: string): boolean {
    // SLine 语法特征模式
    const slinePatterns = [
        // 标签语法
        /\{\{#\s*(if|for|each|unless|with|component|layout|section|var|set|capture|block|case|switch)\b/g,
        // 过滤器语法
        /\{\{[^}]*\|\s*[a-zA-Z_][a-zA-Z0-9_]*\s*\(/g,
        // 对象属性访问
        /\{\{\s*(product|customer|cart|shop|blog|article|collection|page|settings|request|routes)\.[a-zA-Z_]/g,
        // SLine 注释
        /\{\{!--[\s\S]*?--\}\}/g,
        // 结束标签
        /\{\{\/[a-zA-Z_][a-zA-Z0-9_]*\}\}/g,
        // else 和 elseif
        /\{\{#(else|elseif)\b/g
    ];

    // 检查是否匹配任何 SLine 模式
    return slinePatterns.some(pattern => {
        pattern.lastIndex = 0; // 重置正则表达式状态
        return pattern.test(text);
    });
}

/**
 * 建议用户切换到 SLine 语言模式
 */
function suggestSlineMode(document: vscode.TextDocument, hasSyntax: boolean, hasKeyword: boolean) {
    const config = vscode.workspace.getConfiguration('slineLanguageServer');
    const autoSwitch = config.get<boolean>('autoSwitchMode', false);
    const showSuggestion = config.get<boolean>('showModeSuggestion', true);

    if (autoSwitch) {
        // 自动切换模式
        vscode.languages.setTextDocumentLanguage(document, 'sline');
        vscode.window.showInformationMessage(
            `检测到 SLine 语法，已自动切换语言模式`,
            '不再自动切换'
        ).then(selection => {
            if (selection === '不再自动切换') {
                config.update('autoSwitchMode', false, vscode.ConfigurationTarget.Global);
            }
        });
    } else if (showSuggestion) {
        // 显示建议
        const reason = hasSyntax ? '检测到 SLine 语法' : hasKeyword ? '文件名包含 SLine 关键词' : '可能包含 SLine 语法';
        vscode.window.showInformationMessage(
            `${reason}，是否切换到 SLine 语言模式以获得更好的开发体验？`,
            '切换',
            '总是自动切换',
            '不再提示'
        ).then(selection => {
            switch (selection) {
                case '切换':
                    vscode.languages.setTextDocumentLanguage(document, 'sline');
                    break;
                case '总是自动切换':
                    config.update('autoSwitchMode', true, vscode.ConfigurationTarget.Global);
                    vscode.languages.setTextDocumentLanguage(document, 'sline');
                    break;
                case '不再提示':
                    config.update('showModeSuggestion', false, vscode.ConfigurationTarget.Global);
                    break;
            }
        });
    }
}

/**
 * 插件停用时调用的函数
 */
export function deactivate(): Thenable<void> | undefined {
    console.log('SLine Highlight extension is now deactivated!');
    return client.deactivate();
}
