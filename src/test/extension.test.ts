import * as assert from 'assert';
import * as vscode from 'vscode';

suite('Extension Test Suite', () => {
  vscode.window.showInformationMessage('Start all tests.');

  test('Extension should be present', () => {
    assert.ok(vscode.extensions.getExtension('your-publisher-name.sline-highlight'));
  });

  test('Should activate extension', async () => {
    const extension = vscode.extensions.getExtension('your-publisher-name.sline-highlight');
    assert.ok(extension);
    
    if (!extension!.isActive) {
      await extension!.activate();
    }
    
    assert.ok(extension!.isActive);
  });

  test('Should register sline language', () => {
    const languages = vscode.languages.getLanguages();
    assert.ok(languages.then(langs => langs.includes('sline')));
  });
});
