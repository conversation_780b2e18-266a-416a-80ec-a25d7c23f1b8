// Jest 测试设置文件
import 'jest';

// 全局测试设置
beforeAll(() => {
  // 设置测试环境
  process.env.NODE_ENV = 'test';
});

afterAll(() => {
  // 清理测试环境
});

// 全局模拟
global.console = {
  ...console,
  // 在测试中静默某些日志
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// 扩展 Jest 匹配器
expect.extend({
  toBeValidSlineExpression(received: string) {
    const pass = /\{\{.*\}\}/.test(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid Sline expression`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Sline expression`,
        pass: false,
      };
    }
  },
});

// TypeScript 声明扩展
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidSlineExpression(): R;
    }
  }
}
